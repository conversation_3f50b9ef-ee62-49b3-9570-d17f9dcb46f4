<?php

use App\Http\Controllers\Admin\AccommodationController;
use App\Http\Controllers\Admin\AgencyController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\ExperienceController;
use App\Http\Controllers\Admin\LocationController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\RestaurantController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    // Events
    Route::resource('events', EventController::class);

    // News
    Route::resource('news', NewsController::class);

    // Locations
    Route::resource('locations', LocationController::class);

    // Agencies
    Route::resource('agencies', AgencyController::class);
    Route::get('agencies/{agency}/manage-users', [AgencyController::class, 'manageUsers'])->name('agencies.manage-users');
    Route::post('agencies/{agency}/add-users', [AgencyController::class, 'addUsers'])->name('agencies.add-users');
    Route::delete('agencies/{agency}/users/{user}', [AgencyController::class, 'removeUser'])->name('agencies.remove-user');

    // Experiences
    Route::resource('experiences', ExperienceController::class);

    // Accommodations
    Route::resource('accommodations', AccommodationController::class);

    // Restaurants
    Route::resource('restaurants', RestaurantController::class);
});
