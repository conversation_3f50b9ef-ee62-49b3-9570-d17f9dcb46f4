<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed agencies first if they don't exist
        if (!\App\Models\Agency::where('slug', 'cork-experience')->exists()) {
            $this->call(AgencySeeder::class);
        }

        // Create a superadmin user if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'role' => 'superadmin',
            ]);
        }

        // Create a regular user for testing if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'role' => 'user',
            ]);
        }

        // Create an agency user if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'Test Agency User',
                'email' => '<EMAIL>',
                'role' => 'agency',
                'agency_id' => 1,
            ]);
        }

        // Seed other data
        $this->call(NewsSeeder::class);
        $this->call(EventSeeder::class);
        $this->call(LocationSeeder::class);
        $this->call(ExperiencesTableSeeder::class);
        $this->call(RouteSeeder::class);
        $this->call(RoutePointSeeder::class);
        $this->call(AppSettingsSeeder::class);

        // Create story tours
        $locations = \App\Models\Location::take(5)->get();

        if ($locations->isNotEmpty()) {
            $storyTours = [
                [
                    'title' => 'Historia del Corcho en Extremadura',
                    'description' => 'Un recorrido por la historia del corcho en la región de Extremadura, desde sus orígenes hasta la actualidad.',
                    'content' => 'El corcho ha sido un material fundamental en la economía de Extremadura durante siglos. Este tour te llevará a través de los principales hitos históricos y lugares emblemáticos relacionados con la industria corchera en la región.',
                    'image' => 'story_tours/cork_history.jpg',
                    'duration' => 120, // 2 hours
                    'difficulty' => 'easy',
                ],
                [
                    'title' => 'Ruta de los Alcornocales',
                    'description' => 'Descubre los bosques de alcornoques más impresionantes de la región y aprende sobre su importancia ecológica.',
                    'content' => 'Los alcornocales son ecosistemas únicos que albergan una gran biodiversidad. En esta ruta, visitarás algunos de los bosques mejor conservados y aprenderás sobre las especies que habitan en ellos y la importancia de su conservación.',
                    'image' => 'story_tours/cork_oak_forests.jpg',
                    'duration' => 180, // 3 hours
                    'difficulty' => 'medium',
                ],
                [
                    'title' => 'Del Árbol a la Botella',
                    'description' => 'Proceso completo de extracción y transformación del corcho hasta convertirse en tapones para vino.',
                    'content' => 'Este tour te muestra el fascinante proceso de transformación del corcho, desde su extracción del alcornoque hasta su conversión en tapones para botellas de vino. Visitarás fábricas tradicionales y modernas para entender la evolución de esta industria.',
                    'image' => 'story_tours/cork_to_bottle.jpg',
                    'duration' => 150, // 2.5 hours
                    'difficulty' => 'easy',
                ],
            ];

            foreach ($storyTours as $index => $tourData) {
                // Associate each tour with a location
                $location = $locations[$index % count($locations)];

                if (!\App\Models\StoryTour::where('slug', \Illuminate\Support\Str::slug($tourData['title']))->exists()) {
                    \App\Models\StoryTour::create([
                        'title' => $tourData['title'],
                        'slug' => \Illuminate\Support\Str::slug($tourData['title']),
                        'description' => $tourData['description'],
                        'content' => $tourData['content'],
                        'image' => $tourData['image'],
                        'location_id' => $location->id,
                        'duration' => $tourData['duration'],
                        'difficulty' => $tourData['difficulty'],
                        'is_active' => true,
                    ]);
                }
            }
        }
    }
}
