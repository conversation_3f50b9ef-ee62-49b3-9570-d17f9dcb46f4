<?php

namespace App\Services;

use App\Models\User;
use App\Models\Agency;
use App\Notifications\SystemAlert;
use App\Notifications\SystemNotification;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Exception;
use App\Events\NotificationEvent;

class NotificationService
{
    /**
     * Send a notification to all superadmins.
     *
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifySuperadmins(Notification $notification): array
    {
        try {
            $superadmins = User::where('role', 'superadmin')->get();

            if ($superadmins->isEmpty()) {
                Log::warning('No superadmins found to send notification', [
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No superadmins found'];
            }

            return self::sendNotificationToUsers($superadmins, $notification, 'superadmin');

        } catch (Exception $e) {
            Log::error('Critical error in notifySuperadmins', [
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to all users of a specific agency.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyAgencyUsers(int $agencyId, Notification $notification): array
    {
        try {
            // Validate agency exists
            $agency = Agency::find($agencyId);
            if (!$agency) {
                Log::warning('Agency not found for notification', [
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'Agency not found'];
            }

            $agencyUsers = User::where('agency_id', $agencyId)->get();

            if ($agencyUsers->isEmpty()) {
                Log::info('No users found for agency', [
                    'agency_id' => $agencyId,
                    'agency_name' => $agency->name
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found for agency'];
            }

            return self::sendNotificationToUsers($agencyUsers, $notification, 'agency', $agencyId);

        } catch (Exception $e) {
            Log::error('Critical error in notifyAgencyUsers', [
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to agency admins only.
     *
     * @param int $agencyId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyAgencyAdmins(int $agencyId, Notification $notification): array
    {
        try {
            // Validate agency exists
            $agency = Agency::find($agencyId);
            if (!$agency) {
                Log::warning('Agency not found for admin notification', [
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'Agency not found'];
            }

            $agencyAdmins = User::where('agency_id', $agencyId)
                               ->whereIn('role', ['admin', 'agency_admin'])
                               ->get();

            if ($agencyAdmins->isEmpty()) {
                Log::info('No agency admins found', [
                    'agency_id' => $agencyId,
                    'agency_name' => $agency->name
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No agency admins found'];
            }

            return self::sendNotificationToUsers($agencyAdmins, $notification, 'agency_admin', $agencyId);

        } catch (Exception $e) {
            Log::error('Critical error in notifyAgencyAdmins', [
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to a specific user.
     *
     * @param int $userId
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyUser(int $userId, Notification $notification): array
    {
        try {
            $user = User::find($userId);

            if (!$user) {
                Log::warning('User not found for notification', [
                    'user_id' => $userId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'User not found'];
            }

            return self::sendNotificationToUsers(collect([$user]), $notification, 'individual');

        } catch (Exception $e) {
            Log::error('Critical error in notifyUser', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send a notification to multiple agencies.
     *
     * @param array $agencyIds
     * @param Notification $notification
     * @return array Array with success count and failed count
     */
    public static function notifyMultipleAgencies(array $agencyIds, Notification $notification): array
    {
        $totalSuccess = 0;
        $totalFailed = 0;
        $results = [];

        foreach ($agencyIds as $agencyId) {
            $result = self::notifyAgencyUsers($agencyId, $notification);
            $totalSuccess += $result['success'];
            $totalFailed += $result['failed'];
            $results[] = ['agency_id' => $agencyId, 'result' => $result];
        }

        Log::info('Multiple agency notifications completed', [
            'agency_ids' => $agencyIds,
            'total_success' => $totalSuccess,
            'total_failed' => $totalFailed,
            'notification_type' => get_class($notification),
            'detailed_results' => $results
        ]);

        return ['success' => $totalSuccess, 'failed' => $totalFailed, 'details' => $results];
    }

    /**
     * Send notifications based on user roles.
     *
     * @param array $roles
     * @param Notification $notification
     * @param int|null $agencyId Optional agency filter
     * @return array Array with success count and failed count
     */
    public static function notifyByRole(array $roles, Notification $notification, ?int $agencyId = null): array
    {
        try {
            $query = User::whereIn('role', $roles);

            if ($agencyId) {
                $query->where('agency_id', $agencyId);
            }

            $users = $query->get();

            if ($users->isEmpty()) {
                Log::info('No users found for roles', [
                    'roles' => $roles,
                    'agency_id' => $agencyId,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found for specified roles'];
            }

            return self::sendNotificationToUsers($users, $notification, 'role_based', $agencyId);

        } catch (Exception $e) {
            Log::error('Critical error in notifyByRole', [
                'roles' => $roles,
                'agency_id' => $agencyId,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Send system-wide announcement to all users (excluding specific roles if needed).
     *
     * @param Notification $notification
     * @param array $excludeRoles Roles to exclude from notification
     * @return array Array with success count and failed count
     */
    public static function sendSystemAnnouncement(Notification $notification, array $excludeRoles = []): array
    {
        try {
            $query = User::query();

            if (!empty($excludeRoles)) {
                $query->whereNotIn('role', $excludeRoles);
            }

            $users = $query->get();

            if ($users->isEmpty()) {
                Log::info('No users found for system announcement', [
                    'exclude_roles' => $excludeRoles,
                    'notification_type' => get_class($notification)
                ]);
                return ['success' => 0, 'failed' => 0, 'message' => 'No users found'];
            }

            return self::sendNotificationToUsers($users, $notification, 'system_announcement');

        } catch (Exception $e) {
            Log::error('Critical error in sendSystemAnnouncement', [
                'exclude_roles' => $excludeRoles,
                'error' => $e->getMessage(),
                'notification_type' => get_class($notification)
            ]);
            return ['success' => 0, 'failed' => 1, 'message' => 'Critical error occurred'];
        }
    }

    /**
     * Private helper method to send notifications to a collection of users with robust error handling.
     *
     * @param Collection $users
     * @param Notification $notification
     * @param string $context
     * @param int|null $agencyId
     * @return array
     */
    private static function sendNotificationToUsers(Collection $users, Notification $notification, string $context, ?int $agencyId = null): array
    {
        $successCount = 0;
        $failedCount = 0;
        $skippedCount = 0;
        $retryQueue = [];
        $notificationType = get_class($notification);

        // Generate unique notification hash for duplicate prevention
        $notificationHash = md5($notificationType . serialize($notification) . $context . $agencyId);

        foreach ($users as $user) {
            try {
                // Comprehensive user validation
                if (!self::validateUser($user, $context, $agencyId)) {
                    $skippedCount++;
                    continue;
                }

                // Check for duplicate notifications within last 5 minutes
                $duplicateKey = "notification_sent_{$user->id}_{$notificationHash}";
                if (Cache::has($duplicateKey)) {
                    Log::info('Notificación duplicada evitada', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'context' => $context,
                        'notification_type' => $notificationType
                    ]);
                    $skippedCount++;
                    continue;
                }

                // Attempt to send notification with retry logic
                $sent = self::sendNotificationWithRetry($user, $notification, $context, $agencyId);

                if ($sent) {
                    $successCount++;
                    // Cache successful notification to prevent duplicates
                    Cache::put($duplicateKey, true, 300); // 5 minutes

                    Log::debug('Notificación enviada exitosamente', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'context' => $context,
                        'notification_type' => $notificationType
                    ]);
                } else {
                    $failedCount++;
                    // Add to retry queue for later processing
                    $retryQueue[] = [
                        'user_id' => $user->id,
                        'notification' => $notification,
                        'context' => $context,
                        'agency_id' => $agencyId,
                        'attempts' => 1
                    ];
                }

            } catch (Exception $e) {
                Log::error('Error crítico al enviar notificación a usuario', [
                    'user_id' => $user->id,
                    'email' => $user->email ?? 'N/A',
                    'context' => $context,
                    'agency_id' => $agencyId,
                    'error' => $e->getMessage(),
                    'notification_type' => $notificationType,
                    'trace' => $e->getTraceAsString()
                ]);
                $failedCount++;
            }
        }

        // Process retry queue if there are failures
        if (!empty($retryQueue)) {
            self::processRetryQueue($retryQueue);
        }

        $result = [
            'success' => $successCount,
            'failed' => $failedCount,
            'skipped' => $skippedCount,
            'total_processed' => $users->count()
        ];

        Log::info('Resumen de envío de notificaciones', [
            'context' => $context,
            'agency_id' => $agencyId,
            'notification_type' => $notificationType,
            'result' => $result
        ]);

        return $result;
    }

    /**
     * Validate user before sending notification.
     *
     * @param User $user
     * @param string $context
     * @param int|null $agencyId
     * @return bool
     */
    private static function validateUser(User $user, string $context, ?int $agencyId = null): bool
    {
        // Check if user has valid email
        if (!$user->email || !filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
            Log::warning('Usuario con email inválido omitido', [
                'user_id' => $user->id,
                'email' => $user->email ?? 'NULL',
                'context' => $context,
                'agency_id' => $agencyId
            ]);
            return false;
        }

        // Check if user is active
        if (isset($user->status) && $user->status !== 'active') {
            Log::info('Usuario inactivo omitido', [
                'user_id' => $user->id,
                'status' => $user->status,
                'context' => $context
            ]);
            return false;
        }

        // Validate agency association for agency-specific notifications
        if ($agencyId && $user->agency_id != $agencyId) {
            Log::warning('Usuario no pertenece a la agencia especificada', [
                'user_id' => $user->id,
                'user_agency_id' => $user->agency_id,
                'expected_agency_id' => $agencyId,
                'context' => $context
            ]);
            return false;
        }

        // Additional role-based validation
        if ($context === 'superadmin' && $user->role !== 'superadmin') {
            Log::warning('Usuario no es superadministrador', [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'context' => $context
            ]);
            return false;
        }

        if ($context === 'agency_admin' && !in_array($user->role, ['admin', 'agency_admin'])) {
            Log::warning('Usuario no es administrador de agencia', [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'context' => $context
            ]);
            return false;
        }

        return true;
    }

    /**
     * Send notification with retry logic.
     *
     * @param User $user
     * @param Notification $notification
     * @param string $context
     * @param int|null $agencyId
     * @param int $maxRetries
     * @return bool
     */
    private static function sendNotificationWithRetry(User $user, Notification $notification, string $context, ?int $agencyId = null, int $maxRetries = 3): bool
    {
        $attempts = 0;

        while ($attempts < $maxRetries) {
            try {
                $user->notify($notification);
                return true;

            } catch (Exception $e) {
                $attempts++;

                Log::warning('Intento de envío de notificación falló', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'attempt' => $attempts,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage(),
                    'context' => $context,
                    'agency_id' => $agencyId
                ]);

                if ($attempts < $maxRetries) {
                    // Wait before retry (exponential backoff)
                    usleep(pow(2, $attempts) * 100000); // 0.2s, 0.4s, 0.8s
                }
            }
        }

        Log::error('Falló el envío de notificación después de todos los intentos', [
            'user_id' => $user->id,
            'email' => $user->email,
            'total_attempts' => $attempts,
            'context' => $context,
            'agency_id' => $agencyId
        ]);

        return false;
    }

    /**
     * Process retry queue for failed notifications.
     *
     * @param array $retryQueue
     * @return void
     */
    private static function processRetryQueue(array $retryQueue): void
    {
        if (empty($retryQueue)) {
            return;
        }

        Log::info('Procesando cola de reintentos de notificaciones', [
            'queue_size' => count($retryQueue)
        ]);

        // Store retry queue in cache for background processing
        $retryKey = 'notification_retry_queue_' . time();
        Cache::put($retryKey, $retryQueue, 3600); // 1 hour

        Log::info('Cola de reintentos almacenada para procesamiento en segundo plano', [
            'retry_key' => $retryKey,
            'queue_size' => count($retryQueue)
        ]);
    }

    /**
     * Get notification statistics for monitoring purposes.
     *
     * @return array
     */
    public static function getNotificationStats(): array
    {
        try {
            return [
                'total_users' => User::count(),
                'active_users' => User::where('status', 'active')->count(),
                'superadmins' => User::where('role', 'superadmin')->count(),
                'agency_admins' => User::whereIn('role', ['admin', 'agency_admin'])->count(),
                'total_agencies' => Agency::count(),
                'agencies_with_users' => Agency::has('users')->count(),
            ];
        } catch (Exception $e) {
            Log::error('Error getting notification stats', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Create and send a system notification to superadmins.
     */
    public static function sendSystemNotificationToSuperadmins(
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info',
        bool $broadcast = true
    ): array {
        $notification = new SystemNotification($title, $message, $actionText, $actionUrl, $importance);
        return self::notifySuperadmins($notification, $broadcast);
    }

    /**
     * Create and send a system notification to agency users.
     */
    public static function sendSystemNotificationToAgency(
        int $agencyId,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info',
        bool $broadcast = true
    ): array {
        $notification = new SystemNotification($title, $message, $actionText, $actionUrl, $importance);
        return self::notifyAgencyUsers($agencyId, $notification, $broadcast);
    }

    /**
     * Create and send a system notification to agency admins.
     */
    public static function sendSystemNotificationToAgencyAdmins(
        int $agencyId,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info',
        bool $broadcast = true
    ): array {
        $notification = new SystemNotification($title, $message, $actionText, $actionUrl, $importance);
        return self::notifyAgencyAdmins($agencyId, $notification, $broadcast);
    }

    /**
     * Broadcast a notification for real-time updates.
     */
    private static function broadcastNotification(User $user, SystemNotification $notification): void
    {
        try {
            $notificationData = [
                'id' => uniqid(),
                'title' => $notification->title,
                'message' => $notification->message,
                'importance' => $notification->importance,
                'action_text' => $notification->actionText,
                'action_url' => $notification->actionUrl,
                'created_at' => now()->toISOString(),
                'read' => false
            ];

            event(new NotificationEvent($notificationData, $user->id));

        } catch (Exception $e) {
            Log::error('Failed to broadcast notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}