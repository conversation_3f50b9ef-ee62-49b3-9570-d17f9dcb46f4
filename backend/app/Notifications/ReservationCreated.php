<?php

namespace App\Notifications;

use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class ReservationCreated extends Notification
{
    use Queueable;

    protected $reservation;

    /**
     * Create a new notification instance.
     */
    public function __construct(Reservation $reservation)
    {
        $this->reservation = $reservation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        try {
            // Validate required relationships are loaded
            if (!$this->reservation) {
                throw new \Exception('Reservation data is missing');
            }

            // Load experience relationship if not already loaded
            if (!$this->reservation->relationLoaded('experience')) {
                $this->reservation->load('experience');
            }

            $experienceTitle = $this->reservation->experience->title ?? 'Experiencia no especificada';
            $reservationDate = $this->reservation->reservation_date ?
                $this->reservation->reservation_date->format('d/m/Y') : 'Fecha no especificada';
            $reservationTime = $this->reservation->reservation_time ?
                $this->reservation->reservation_time->format('H:i') : 'No especificada';
            $numPeople = $this->reservation->num_people ?? 'No especificado';

            return (new MailMessage)
                ->subject('Nueva Reserva Creada - Cork Experience')
                ->line('Se ha creado una nueva reserva para una de tus experiencias.')
                ->line('Experiencia: ' . $experienceTitle)
                ->line('Fecha: ' . $reservationDate)
                ->line('Hora: ' . $reservationTime)
                ->line('Personas: ' . $numPeople)
                ->action('Ver Detalles', url('/dashboard/reservations/' . $this->reservation->id))
                ->line('¡Gracias por usar Cork Experience!');

        } catch (\Exception $e) {
            \Log::error('Error al generar email de reserva creada', [
                'reservation_id' => $this->reservation->id ?? 'unknown',
                'error' => $e->getMessage(),
                'notifiable_id' => $notifiable->id ?? 'unknown'
            ]);

            // Return a fallback email
            return (new MailMessage)
                ->subject('Nueva Reserva Creada - Cork Experience')
                ->line('Se ha creado una nueva reserva.')
                ->line('Por favor, revisa el panel de administración para más detalles.')
                ->action('Ver Panel', url('/dashboard'))
                ->line('¡Gracias por usar Cork Experience!');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        try {
            // Ensure relationships are loaded
            if (!$this->reservation->relationLoaded('experience')) {
                $this->reservation->load('experience');
            }
            if (!$this->reservation->relationLoaded('user')) {
                $this->reservation->load('user');
            }

            return [
                'reservation_id' => $this->reservation->id,
                'experience_id' => $this->reservation->experience_id,
                'experience_title' => $this->reservation->experience->title ?? 'Experiencia no especificada',
                'user_name' => $this->reservation->user->name ?? 'Usuario no especificado',
                'user_email' => $this->reservation->user->email ?? null,
                'reservation_date' => $this->reservation->reservation_date ?
                    $this->reservation->reservation_date->format('Y-m-d') : null,
                'reservation_time' => $this->reservation->reservation_time ?
                    $this->reservation->reservation_time->format('H:i') : null,
                'num_people' => $this->reservation->num_people ?? 1,
                'status' => $this->reservation->status ?? 'pending',
                'agency_id' => $this->reservation->experience->agency_id ?? null,
                'type' => 'reservation_created',
                'created_at' => $this->reservation->created_at ?
                    $this->reservation->created_at->toISOString() : now()->toISOString(),
            ];

        } catch (\Exception $e) {
            \Log::error('Error al generar datos de array para reserva creada', [
                'reservation_id' => $this->reservation->id ?? 'unknown',
                'error' => $e->getMessage(),
                'notifiable_id' => $notifiable->id ?? 'unknown'
            ]);

            // Return minimal safe data
            return [
                'reservation_id' => $this->reservation->id ?? null,
                'type' => 'reservation_created',
                'error' => 'Error al cargar datos de la reserva',
                'created_at' => now()->toISOString(),
            ];
        }
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);

        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));

        return $data;
    }
}
