<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class NewAgencyUser extends Notification
{
    use Queueable;

    protected $newUser;
    protected $agency;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $newUser)
    {
        $this->newUser = $newUser;

        // Safely load agency relationship
        try {
            if (!$newUser->relationLoaded('agency')) {
                $newUser->load('agency');
            }
            $this->agency = $newUser->agency;

            if (!$this->agency) {
                \Log::warning('Usuario sin agencia asociada en notificación NewAgencyUser', [
                    'user_id' => $newUser->id,
                    'user_email' => $newUser->email
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error al cargar agencia en notificación NewAgencyUser', [
                'user_id' => $newUser->id,
                'error' => $e->getMessage()
            ]);
            $this->agency = null;
        }
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        try {
            $userName = $this->newUser->name ?? 'Usuario sin nombre';
            $userEmail = $this->newUser->email ?? 'Email no especificado';
            $agencyName = $this->agency->name ?? 'Agencia no especificada';

            return (new MailMessage)
                ->subject('Nuevo Usuario en tu Agencia - Cork Experience')
                ->line('Se ha añadido un nuevo usuario a tu agencia.')
                ->line('Nombre: ' . $userName)
                ->line('Email: ' . $userEmail)
                ->line('Agencia: ' . $agencyName)
                ->action('Ver Usuarios de la Agencia', url('/dashboard/agency-user-management'))
                ->line('¡Gracias por usar Cork Experience!');

        } catch (\Exception $e) {
            \Log::error('Error al generar email de nuevo usuario de agencia', [
                'user_id' => $this->newUser->id ?? 'unknown',
                'agency_id' => $this->agency->id ?? 'unknown',
                'error' => $e->getMessage(),
                'notifiable_id' => $notifiable->id ?? 'unknown'
            ]);

            // Return a fallback email
            return (new MailMessage)
                ->subject('Nuevo Usuario en tu Agencia - Cork Experience')
                ->line('Se ha añadido un nuevo usuario a tu agencia.')
                ->line('Por favor, revisa el panel de administración para más detalles.')
                ->action('Ver Panel', url('/dashboard'))
                ->line('¡Gracias por usar Cork Experience!');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->newUser->id,
            'user_name' => $this->newUser->name,
            'user_email' => $this->newUser->email,
            'agency_id' => $this->agency->id,
            'agency_name' => $this->agency->name,
            'type' => 'new_agency_user',
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);

        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));

        return $data;
    }
}
