<?php

namespace App\Notifications;

use App\Models\Agency;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class NewAgencyCreated extends Notification
{
    use Queueable;

    /**
     * The agency that was created.
     *
     * @var \App\Models\Agency
     */
    protected $agency;

    /**
     * Create a new notification instance.
     *
     * @param \App\Models\Agency $agency
     * @return void
     */
    public function __construct(Agency $agency)
    {
        $this->agency = $agency;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Nueva Agencia Creada')
            ->line('Se ha creado una nueva agencia en el sistema.')
            ->line('Nombre: ' . $this->agency->name)
            ->line('Email: ' . $this->agency->email)
            ->action('Ver Detalles', url('/dashboard/agencies/' . $this->agency->id))
            ->line('¡Gracias por usar Cork Experience!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'agency_id' => $this->agency->id,
            'agency_name' => $this->agency->name,
            'agency_email' => $this->agency->email,
            'type' => 'new_agency_created',
        ];
    }
    
    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);
        
        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));
        
        return $data;
    }
}
