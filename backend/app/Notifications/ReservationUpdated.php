<?php

namespace App\Notifications;

use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class ReservationUpdated extends Notification
{
    use Queueable;

    protected $reservation;
    protected $changes;

    /**
     * Create a new notification instance.
     */
    public function __construct(Reservation $reservation, array $changes = [])
    {
        $this->reservation = $reservation;
        $this->changes = $changes;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Reserva Actualizada')
            ->line('Una reserva ha sido actualizada para una de tus experiencias.')
            ->line('Experiencia: ' . $this->reservation->experience->title)
            ->line('Fecha: ' . $this->reservation->reservation_date->format('d/m/Y'))
            ->line('Hora: ' . ($this->reservation->reservation_time ? $this->reservation->reservation_time->format('H:i') : 'No especificada'))
            ->line('Estado: ' . $this->reservation->status)
            ->action('Ver Detalles', url('/dashboard/reservations/' . $this->reservation->id))
            ->line('¡Gracias por usar Cork Experience!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'reservation_id' => $this->reservation->id,
            'experience_id' => $this->reservation->experience_id,
            'experience_title' => $this->reservation->experience->title,
            'user_name' => $this->reservation->user->name,
            'reservation_date' => $this->reservation->reservation_date->format('Y-m-d'),
            'reservation_time' => $this->reservation->reservation_time ? $this->reservation->reservation_time->format('H:i') : null,
            'status' => $this->reservation->status,
            'changes' => $this->changes,
            'agency_id' => $this->reservation->experience->agency_id,
            'type' => 'reservation_updated',
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);

        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));

        return $data;
    }
}
