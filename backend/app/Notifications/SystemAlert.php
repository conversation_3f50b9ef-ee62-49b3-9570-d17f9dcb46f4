<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class SystemAlert extends Notification
{
    use Queueable;

    /**
     * The alert title.
     *
     * @var string
     */
    protected $title;

    /**
     * The alert message.
     *
     * @var string
     */
    protected $message;

    /**
     * The alert level (info, warning, error).
     *
     * @var string
     */
    protected $level;

    /**
     * The action text.
     *
     * @var string|null
     */
    protected $actionText;

    /**
     * The action URL.
     *
     * @var string|null
     */
    protected $actionUrl;

    /**
     * Create a new notification instance.
     *
     * @param string $title
     * @param string $message
     * @param string $level
     * @param string|null $actionText
     * @param string|null $actionUrl
     * @return void
     */
    public function __construct(string $title, string $message, string $level = 'info', ?string $actionText = null, ?string $actionUrl = null)
    {
        $this->title = $title;
        $this->message = $message;
        $this->level = $level;
        $this->actionText = $actionText;
        $this->actionUrl = $actionUrl;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mail = (new MailMessage)
            ->subject('Alerta del Sistema: ' . $this->title)
            ->line($this->message);

        if ($this->level === 'warning') {
            $mail->warning();
        } elseif ($this->level === 'error') {
            $mail->error();
        }

        if ($this->actionText && $this->actionUrl) {
            $mail->action($this->actionText, $this->actionUrl);
        }

        return $mail->line('¡Gracias por usar Cork Experience!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'level' => $this->level,
            'action_text' => $this->actionText,
            'action_url' => $this->actionUrl,
            'type' => 'system_alert',
        ];
    }
    
    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);
        
        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));
        
        return $data;
    }
}
