<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Notifications\Notification;
use App\Events\NotificationEvent;

class SystemNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The notification title.
     *
     * @var string
     */
    public $title;

    /**
     * The notification message.
     *
     * @var string
     */
    public $message;

    /**
     * The action button text.
     *
     * @var string|null
     */
    public $actionText;

    /**
     * The action URL.
     *
     * @var string|null
     */
    public $actionUrl;

    /**
     * The importance level.
     *
     * @var string
     */
    public $importance;

    /**
     * Create a new notification instance.
     *
     * @param string $title
     * @param string $message
     * @param string|null $actionText
     * @param string|null $actionUrl
     * @param string $importance ('info', 'success', 'warning', 'error')
     * @return void
     */
    public function __construct(
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info'
    ) {
        $this->title = $title;
        $this->message = $message;
        $this->actionText = $actionText;
        $this->actionUrl = $actionUrl;
        $this->importance = $importance;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $channels = ['database', 'broadcast'];
        
        // Add email channel for important notifications
        if (in_array($this->importance, ['warning', 'error'])) {
            $channels[] = 'mail';
        }
        
        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mailMessage = (new MailMessage)
            ->subject($this->title)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($this->message);

        // Add action button if provided
        if ($this->actionText && $this->actionUrl) {
            $mailMessage->action($this->actionText, url($this->actionUrl));
        }

        // Set mail theme based on importance
        switch ($this->importance) {
            case 'error':
                $mailMessage->error();
                break;
            case 'success':
                $mailMessage->success();
                break;
            default:
                break;
        }

        return $mailMessage->line('Thank you for using our application!');
    }

    /**
     * Get the database representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'action_text' => $this->actionText,
            'action_url' => $this->actionUrl,
            'importance' => $this->importance,
            'type' => 'system',
            'created_at' => now(),
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return $this->toDatabase($notifiable);
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        $data = $this->toArray($notifiable);

        // Dispatch a custom event for real-time updates
        event(new NotificationEvent($data, $notifiable->id));

        return $data;
    }
}
