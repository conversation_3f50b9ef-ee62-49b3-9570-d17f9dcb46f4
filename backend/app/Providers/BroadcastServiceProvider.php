<?php

namespace App\Providers;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Use auth:sanctum middleware for broadcasting authentication
        Broadcast::routes(['middleware' => ['auth:sanctum']]);

        // Load the channels file
        require base_path('routes/channels.php');
    }
}
