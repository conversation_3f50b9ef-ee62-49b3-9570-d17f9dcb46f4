<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AppSetting extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'is_public',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get a setting by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();

        return $setting ? $setting->value : $default;
    }

    /**
     * Set a setting by key
     *
     * @param string $key
     * @param mixed $value
     * @param array $attributes Additional attributes to update
     * @return AppSetting
     */
    public static function set(string $key, $value, array $attributes = [])
    {
        $setting = static::firstOrNew(['key' => $key]);
        $setting->value = $value;

        foreach ($attributes as $attr => $val) {
            $setting->{$attr} = $val;
        }

        $setting->save();

        return $setting;
    }
}
