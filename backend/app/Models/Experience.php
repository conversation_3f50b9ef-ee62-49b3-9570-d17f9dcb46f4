<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Experience extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'short_description',
        'location_id',
        'agency_id',
        'type',
        'duration',
        'distance',
        'difficulty',
        'price',
        'image',
        'start_date',
        'end_date',
        'is_featured',
        'is_active',
        // Restaurant-specific fields
        'address',
        'phone',
        'email',
        'website',
        'cuisine_type',
        'opening_hours',
        'menu_url',
        // Time slot and capacity fields
        'available_time_slots',
        'recurring_time_slots',
        'use_recurring_slots',
        'capacity_per_time_slot',
        'max_reservations_per_day',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'float',
        'start_date' => 'date',
        'end_date' => 'date',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'available_time_slots' => 'array',
        'recurring_time_slots' => 'array',
        'use_recurring_slots' => 'boolean',
        'capacity_per_time_slot' => 'integer',
        'max_reservations_per_day' => 'integer',
    ];

    /**
     * Get the location that the experience belongs to.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the agency that the experience belongs to.
     */
    public function agency(): BelongsTo
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * Get the reservations for the experience.
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }
}
