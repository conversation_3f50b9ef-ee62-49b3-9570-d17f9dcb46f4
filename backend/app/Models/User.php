<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'agency_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the agency that the user belongs to.
     */
    public function agency(): BelongsTo
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * Check if the user is a superadmin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'superadmin';
    }

    /**
     * Check if the user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin' || $this->role === 'superadmin';
    }

    /**
     * Check if the user is an agency admin.
     */
    public function isAgencyAdmin(): bool
    {
        return $this->role === 'agency_admin' && $this->agency_id !== null;
    }

    /**
     * Check if the user is an agency user.
     */
    public function isAgencyUser(): bool
    {
        return $this->agency_id !== null;
    }

    /**
     * Get the reservations for the user.
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the groups that the user belongs to.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_users')
            ->withPivot('is_guide')
            ->withTimestamps();
    }

    /**
     * Get the groups where the user is a guide.
     */
    public function guidedGroups()
    {
        return $this->groups()->wherePivot('is_guide', true);
    }

    /**
     * Check if the user is a guide in any group or has guide role.
     */
    public function isGuide(): bool
    {
        return $this->hasRole('guide') || $this->guidedGroups()->exists();
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        $roles = $this->getRoles();
        return in_array($role, $roles);
    }

    /**
     * Check if user can access CMS backend.
     */
    public function canAccessCMS(): bool
    {
        $cmsRoles = ['superadmin', 'agency', 'entity'];
        $userRoles = $this->getRoles();

        return !empty(array_intersect($cmsRoles, $userRoles));
    }

    /**
     * Check if user can access mobile frontend.
     */
    public function canAccessMobile(): bool
    {
        $mobileRoles = ['tourist', 'guide', 'group'];
        $userRoles = $this->getRoles();

        return !empty(array_intersect($mobileRoles, $userRoles));
    }

    /**
     * Get the primary access type for this user.
     */
    public function getPrimaryAccessType(): string
    {
        if ($this->canAccessCMS()) {
            return 'cms';
        } elseif ($this->canAccessMobile()) {
            return 'mobile';
        }

        return 'none';
    }

    /**
     * Check if user is a backend operator (CMS access).
     */
    public function isBackendOperator(): bool
    {
        return $this->hasRole('superadmin') || $this->hasRole('agency') || $this->hasRole('entity');
    }

    /**
     * Check if user is a mobile app user.
     */
    public function isMobileUser(): bool
    {
        return $this->hasRole('tourist') || $this->hasRole('guide') || $this->hasRole('group');
    }

    /**
     * Get all roles for this user.
     */
    public function getRoles(): array
    {
        $roles = [];

        // Add primary role
        if ($this->role) {
            $roles[] = $this->role;
        }

        // Add additional roles from JSON field
        if ($this->additional_roles) {
            $additionalRoles = is_string($this->additional_roles)
                ? json_decode($this->additional_roles, true)
                : $this->additional_roles;

            if (is_array($additionalRoles)) {
                $roles = array_merge($roles, $additionalRoles);
            }
        }

        return array_unique($roles);
    }

    /**
     * Add a role to the user.
     */
    public function addRole(string $role): void
    {
        $currentRoles = $this->getRoles();

        if (!in_array($role, $currentRoles)) {
            // If this is the first role, set it as primary
            if (!$this->role) {
                $this->role = $role;
            } else {
                // Add to additional roles
                $additionalRoles = $this->additional_roles
                    ? (is_string($this->additional_roles) ? json_decode($this->additional_roles, true) : $this->additional_roles)
                    : [];

                if (!is_array($additionalRoles)) {
                    $additionalRoles = [];
                }

                $additionalRoles[] = $role;
                $this->additional_roles = json_encode(array_unique($additionalRoles));
            }

            $this->save();
        }
    }

    /**
     * Remove a role from the user.
     */
    public function removeRole(string $role): void
    {
        $currentRoles = $this->getRoles();

        if (in_array($role, $currentRoles)) {
            if ($this->role === $role) {
                // If removing primary role, promote first additional role
                $additionalRoles = $this->additional_roles
                    ? (is_string($this->additional_roles) ? json_decode($this->additional_roles, true) : $this->additional_roles)
                    : [];

                if (is_array($additionalRoles) && count($additionalRoles) > 0) {
                    $this->role = array_shift($additionalRoles);
                    $this->additional_roles = count($additionalRoles) > 0 ? json_encode($additionalRoles) : null;
                } else {
                    $this->role = 'user'; // Default role
                    $this->additional_roles = null;
                }
            } else {
                // Remove from additional roles
                $additionalRoles = $this->additional_roles
                    ? (is_string($this->additional_roles) ? json_decode($this->additional_roles, true) : $this->additional_roles)
                    : [];

                if (is_array($additionalRoles)) {
                    $additionalRoles = array_values(array_filter($additionalRoles, function($r) use ($role) {
                        return $r !== $role;
                    }));
                    $this->additional_roles = count($additionalRoles) > 0 ? json_encode($additionalRoles) : null;
                }
            }

            $this->save();
        }
    }

    /**
     * Check if the user is a guide in the specified group.
     */
    public function isGuideInGroup(Group $group): bool
    {
        return $this->guidedGroups()->where('group_id', $group->id)->exists();
    }
}
