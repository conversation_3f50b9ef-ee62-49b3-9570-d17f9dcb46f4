<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Reservation extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'group_id',
        'is_group_reservation',
        'experience_id',
        'reservation_date',
        'reservation_time',
        'num_people',
        'special_requests',
        'status',
        'admin_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'reservation_date' => 'date',
        'reservation_time' => 'datetime:H:i',
        'is_group_reservation' => 'boolean',
    ];

    /**
     * Get the user that owns the reservation.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the experience that is reserved.
     */
    public function experience()
    {
        return $this->belongsTo(Experience::class);
    }

    /**
     * Scope a query to only include reservations with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include upcoming reservations.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('reservation_date', '>=', now()->toDateString())
                     ->whereNotIn('status', ['cancelled', 'completed']);
    }

    /**
     * Scope a query to only include past reservations.
     */
    public function scopePast($query)
    {
        return $query->where('reservation_date', '<', now()->toDateString())
                     ->orWhere('status', 'completed');
    }

    /**
     * Get the group that the reservation belongs to.
     */
    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get the booking code for the reservation.
     */
    public function bookingCode()
    {
        return $this->hasOne(BookingCode::class);
    }

    /**
     * Check if the reservation is a group reservation.
     *
     * @return bool
     */
    public function isGroupReservation(): bool
    {
        return $this->is_group_reservation;
    }
}
