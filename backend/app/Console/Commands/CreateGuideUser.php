<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateGuideUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create-guide {--force : Force creation even if user exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a guide user for testing the guide management system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = '<EMAIL>';
        $force = $this->option('force');

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();
        
        if ($existingUser && !$force) {
            $this->error("User with email {$email} already exists!");
            $this->info("Use --force flag to recreate the user.");
            return 1;
        }

        if ($existingUser && $force) {
            $this->info("Deleting existing user...");
            $existingUser->delete();
        }

        // Create the guide user
        $user = User::create([
            'name' => 'Guide User',
            'email' => $email,
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'role' => 'guide',
            'agency_id' => 1, // Assuming agency with ID 1 exists
        ]);

        $this->info("Guide user created successfully!");
        $this->table(
            ['Field', 'Value'],
            [
                ['ID', $user->id],
                ['Name', $user->name],
                ['Email', $user->email],
                ['Role', $user->role],
                ['Agency ID', $user->agency_id],
                ['Created At', $user->created_at],
            ]
        );

        $this->info("You can now log in with:");
        $this->info("Email: {$email}");
        $this->info("Password: password");

        return 0;
    }
}
