<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\SystemNotification;
use Illuminate\Console\Command;

class SendTestEmailNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:test-email {email : The email address to send the test notification to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email notification to a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        // Find the user by email
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }
        
        // Send a test notification
        $user->notify(new SystemNotification(
            'Test Notification',
            'This is a test notification to verify that email notifications are working correctly.',
            'Visit Dashboard',
            url('/dashboard')
        ));
        
        $this->info("Test notification sent to {$email}. Check the logs to see the email content.");
        
        return 0;
    }
}
