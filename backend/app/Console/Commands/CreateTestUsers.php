<?php

namespace App\Console\Commands;

use App\Models\Agency;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateTestUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-test-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create test users for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Create superadmin user
        $superadmin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password'),
                'role' => 'superadmin',
                'email_verified_at' => now(),
            ]
        );

        $this->info('Superadmin user created: <EMAIL> / password');

        // Create or find an agency
        $agency = Agency::firstOrCreate(
            ['name' => 'Test Agency'],
            [
                'description' => 'This is a test agency',
                'address' => 'Test Address',
                'phone' => '123456789',
                'email' => '<EMAIL>',
                'website' => 'https://example.com',
                'logo' => null,
                'slug' => 'test-agency',
            ]
        );

        // Create agency user
        $agencyUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Agency User',
                'password' => Hash::make('password'),
                'role' => 'agency',
                'agency_id' => $agency->id,
                'email_verified_at' => now(),
            ]
        );

        $this->info('Agency user created: <EMAIL> / password');

        // Create regular user
        $user = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('password'),
                'role' => 'user',
                'email_verified_at' => now(),
            ]
        );

        $this->info('Regular user created: <EMAIL> / password');

        return Command::SUCCESS;
    }
}
