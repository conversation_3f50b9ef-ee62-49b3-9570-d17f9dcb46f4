<?php

namespace App\Console\Commands;

use App\Models\StoryTour;
use App\Models\Location;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class SeedStoryTours extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:seed-story-tours';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed the story tours table with sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Seeding story tours...');

        // Get some locations to associate with story tours
        $locations = Location::take(5)->get();

        if ($locations->isEmpty()) {
            $this->error('No locations found. Please run the location seeder first.');
            return Command::FAILURE;
        }

        $storyTours = [
            [
                'title' => 'Historia del Corcho en Extremadura',
                'description' => 'Un recorrido por la historia del corcho en la región de Extremadura, desde sus orígenes hasta la actualidad.',
                'content' => 'El corcho ha sido un material fundamental en la economía de Extremadura durante siglos. Este tour te llevará a través de los principales hitos históricos y lugares emblemáticos relacionados con la industria corchera en la región.',
                'image' => 'story_tours/cork_history.jpg',
                'duration' => 120, // 2 hours
                'difficulty' => 'easy',
            ],
            [
                'title' => 'Ruta de los Alcornocales',
                'description' => 'Descubre los bosques de alcornoques más impresionantes de la región y aprende sobre su importancia ecológica.',
                'content' => 'Los alcornocales son ecosistemas únicos que albergan una gran biodiversidad. En esta ruta, visitarás algunos de los bosques mejor conservados y aprenderás sobre las especies que habitan en ellos y la importancia de su conservación.',
                'image' => 'story_tours/cork_oak_forests.jpg',
                'duration' => 180, // 3 hours
                'difficulty' => 'medium',
            ],
            [
                'title' => 'Del Árbol a la Botella',
                'description' => 'Proceso completo de extracción y transformación del corcho hasta convertirse en tapones para vino.',
                'content' => 'Este tour te muestra el fascinante proceso de transformación del corcho, desde su extracción del alcornoque hasta su conversión en tapones para botellas de vino. Visitarás fábricas tradicionales y modernas para entender la evolución de esta industria.',
                'image' => 'story_tours/cork_to_bottle.jpg',
                'duration' => 150, // 2.5 hours
                'difficulty' => 'easy',
            ],
            [
                'title' => 'Arquitectura del Corcho',
                'description' => 'Edificios y construcciones que utilizan el corcho como material principal o decorativo.',
                'content' => 'El corcho no solo se usa para tapones, sino también en la construcción y decoración. Este tour te llevará a conocer edificios innovadores que utilizan el corcho como material de construcción, aprovechando sus propiedades aislantes y sostenibles.',
                'image' => 'story_tours/cork_architecture.jpg',
                'duration' => 90, // 1.5 hours
                'difficulty' => 'easy',
            ],
            [
                'title' => 'Gastronomía y Corcho',
                'description' => 'Ruta gastronómica por bodegas y restaurantes relacionados con la cultura del corcho.',
                'content' => 'Esta experiencia combina la cultura del corcho con la gastronomía local. Visitarás bodegas que utilizan tapones de corcho de alta calidad y degustarás vinos y platos típicos de la región en un entorno relacionado con la industria corchera.',
                'image' => 'story_tours/cork_gastronomy.jpg',
                'duration' => 240, // 4 hours
                'difficulty' => 'easy',
            ],
        ];

        $count = 0;
        foreach ($storyTours as $index => $tourData) {
            // Associate each tour with a location
            $location = $locations[$index % count($locations)];

            $tour = StoryTour::firstOrCreate(
                ['title' => $tourData['title']],
                [
                    'description' => $tourData['description'],
                    'content' => $tourData['content'] ?? '',
                    'image' => $tourData['image'],
                    'location_id' => $location->id,
                    'type' => $tourData['difficulty'] ?? 'easy',
                    'is_active' => true,
                ]
            );

            if ($tour->wasRecentlyCreated) {
                $count++;
            }
        }

        $this->info("Created {$count} new story tours.");
        return Command::SUCCESS;
    }
}
