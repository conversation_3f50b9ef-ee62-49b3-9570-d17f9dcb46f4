<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\SystemAlert;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class SendTestRoleNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:test-role {role : The role to send the notification to (superadmin, agency_admin, all)} {agency_id? : The agency ID if sending to agency users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test notification to users with a specific role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $role = $this->argument('role');
        $agencyId = $this->argument('agency_id');
        
        if ($role === 'agency_admin' && !$agencyId) {
            $this->error('You must provide an agency_id when sending to agency_admin role.');
            return 1;
        }
        
        $title = 'Test Notification';
        $message = 'This is a test notification for role-based notification delivery.';
        $notification = new SystemAlert($title, $message, 'info', 'View Dashboard', url('/dashboard'));
        
        switch ($role) {
            case 'superadmin':
                NotificationService::notify<PERSON><PERSON>radmins($notification);
                $this->info('Test notification sent to all superadmins.');
                break;
                
            case 'agency_admin':
                NotificationService::notifyAgencyAdmins($agencyId, $notification);
                $this->info("Test notification sent to agency admins of agency ID: {$agencyId}.");
                break;
                
            case 'all':
                // Send to all users
                $users = User::all();
                foreach ($users as $user) {
                    $user->notify($notification);
                }
                $this->info('Test notification sent to all users.');
                break;
                
            default:
                $this->error('Invalid role. Use superadmin, agency_admin, or all.');
                return 1;
        }
        
        return 0;
    }
}
