<?php

namespace App\Console\Commands;

use App\Helpers\NotificationHelper;
use App\Models\Agency;
use App\Models\User;
use Illuminate\Console\Command;

class SendSystemNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:send
                            {--to=all : Target audience (all, superadmins, agency, user)}
                            {--agency-id= : Agency ID when target is agency}
                            {--user-id= : User ID when target is user}
                            {--title= : Notification title}
                            {--message= : Notification message}
                            {--action-text= : Action button text}
                            {--action-url= : Action URL}
                            {--importance=info : Importance level (info, success, warning, error)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a system notification to users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $to = $this->option('to');
        $title = $this->option('title');
        $message = $this->option('message');
        $actionText = $this->option('action-text');
        $actionUrl = $this->option('action-url');
        $importance = $this->option('importance');
        
        // Validate required options
        if (!$title || !$message) {
            $title = $title ?: $this->ask('Enter notification title:');
            $message = $message ?: $this->ask('Enter notification message:');
        }
        
        // Validate importance
        $validImportance = ['info', 'success', 'warning', 'error'];
        if (!in_array($importance, $validImportance)) {
            $this->error("Invalid importance level. Must be one of: " . implode(', ', $validImportance));
            return Command::FAILURE;
        }
        
        // Send notification based on target audience
        switch ($to) {
            case 'superadmins':
                $this->sendToSuperadmins($title, $message, $actionText, $actionUrl, $importance);
                break;
                
            case 'agency':
                $agencyId = $this->option('agency-id');
                if (!$agencyId) {
                    $agencies = Agency::orderBy('name')->get(['id', 'name']);
                    $agencyChoices = $agencies->pluck('name', 'id')->toArray();
                    $agencyId = $this->choice('Select agency:', $agencyChoices);
                    $agencyId = array_search($agencyId, $agencyChoices);
                }
                
                $this->sendToAgency($agencyId, $title, $message, $actionText, $actionUrl, $importance);
                break;
                
            case 'user':
                $userId = $this->option('user-id');
                if (!$userId) {
                    $userId = $this->ask('Enter user ID:');
                }
                
                $this->sendToUser($userId, $title, $message, $actionText, $actionUrl, $importance);
                break;
                
            case 'all':
            default:
                $this->sendToAll($title, $message, $actionText, $actionUrl, $importance);
                break;
        }
        
        return Command::SUCCESS;
    }
    
    /**
     * Send notification to all superadmins.
     */
    private function sendToSuperadmins($title, $message, $actionText, $actionUrl, $importance)
    {
        NotificationHelper::notifySuperadmins($title, $message, $actionText, $actionUrl, $importance);
        $count = User::where('role', 'superadmin')->count();
        $this->info("Notification sent to {$count} superadmins.");
    }
    
    /**
     * Send notification to all users of a specific agency.
     */
    private function sendToAgency($agencyId, $title, $message, $actionText, $actionUrl, $importance)
    {
        $agency = Agency::find($agencyId);
        if (!$agency) {
            $this->error("Agency with ID {$agencyId} not found.");
            return;
        }
        
        NotificationHelper::notifyAgency($agencyId, $title, $message, $actionText, $actionUrl, $importance);
        $count = User::where('agency_id', $agencyId)->count();
        $this->info("Notification sent to {$count} users of agency '{$agency->name}'.");
    }
    
    /**
     * Send notification to a specific user.
     */
    private function sendToUser($userId, $title, $message, $actionText, $actionUrl, $importance)
    {
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found.");
            return;
        }
        
        NotificationHelper::notifyUser($user, $title, $message, $actionText, $actionUrl, $importance);
        $this->info("Notification sent to user '{$user->name}'.");
    }
    
    /**
     * Send notification to all users.
     */
    private function sendToAll($title, $message, $actionText, $actionUrl, $importance)
    {
        $users = User::all();
        foreach ($users as $user) {
            NotificationHelper::notifyUser($user, $title, $message, $actionText, $actionUrl, $importance);
        }
        
        $this->info("Notification sent to all {$users->count()} users.");
    }
}
