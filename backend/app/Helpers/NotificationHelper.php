<?php

namespace App\Helpers;

use App\Models\User;
use App\Notifications\SystemNotification;

class NotificationHelper
{
    /**
     * Send a system notification to all superadmins.
     *
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string|null $actionText The action button text (optional)
     * @param string|null $actionUrl The action URL (optional)
     * @param string $importance The importance level ('info', 'success', 'warning', 'error')
     * @return void
     */
    public static function notifySuperadmins(
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info'
    ): void {
        $superadmins = User::where('role', 'superadmin')->get();
        
        foreach ($superadmins as $admin) {
            $admin->notify(new SystemNotification($title, $message, $actionText, $actionUrl, $importance));
        }
    }
    
    /**
     * Send a system notification to all users of a specific agency.
     *
     * @param int $agencyId The agency ID
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string|null $actionText The action button text (optional)
     * @param string|null $actionUrl The action URL (optional)
     * @param string $importance The importance level ('info', 'success', 'warning', 'error')
     * @return void
     */
    public static function notifyAgency(
        int $agencyId,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info'
    ): void {
        $agencyUsers = User::where('agency_id', $agencyId)->get();
        
        foreach ($agencyUsers as $user) {
            $user->notify(new SystemNotification($title, $message, $actionText, $actionUrl, $importance));
        }
    }
    
    /**
     * Send a system notification to all agency admins of a specific agency.
     *
     * @param int $agencyId The agency ID
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string|null $actionText The action button text (optional)
     * @param string|null $actionUrl The action URL (optional)
     * @param string $importance The importance level ('info', 'success', 'warning', 'error')
     * @return void
     */
    public static function notifyAgencyAdmins(
        int $agencyId,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info'
    ): void {
        $agencyAdmins = User::where('agency_id', $agencyId)
            ->where('role', 'agency_admin')
            ->get();
        
        foreach ($agencyAdmins as $admin) {
            $admin->notify(new SystemNotification($title, $message, $actionText, $actionUrl, $importance));
        }
    }
    
    /**
     * Send a system notification to a specific user.
     *
     * @param User $user The user to notify
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string|null $actionText The action button text (optional)
     * @param string|null $actionUrl The action URL (optional)
     * @param string $importance The importance level ('info', 'success', 'warning', 'error')
     * @return void
     */
    public static function notifyUser(
        User $user,
        string $title,
        string $message,
        ?string $actionText = null,
        ?string $actionUrl = null,
        string $importance = 'info'
    ): void {
        $user->notify(new SystemNotification($title, $message, $actionText, $actionUrl, $importance));
    }
}
