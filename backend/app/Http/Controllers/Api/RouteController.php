<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\RouteResource;
use App\Models\Route;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class RouteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): AnonymousResourceCollection
    {
        $routes = Route::with(['agency', 'points.location'])
            ->where('is_active', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return RouteResource::collection($routes);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'image' => 'nullable|image|max:2048',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'agency_id' => 'nullable|exists:agencies,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'points' => 'nullable|array',
            'points.*.location_id' => 'nullable|exists:locations,id',
            'points.*.order' => 'integer',
            'points.*.description' => 'nullable|string',
            'points.*.image' => 'nullable|image|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('routes', 'public');
        }

        // Create the route
        $route = Route::create($validated);

        // Create route points if provided
        if ($request->has('points') && is_array($request->points)) {
            foreach ($request->points as $index => $pointData) {
                $pointData['order'] = $pointData['order'] ?? $index;

                // Handle point image upload
                if (isset($pointData['image']) && $pointData['image'] instanceof \Illuminate\Http\UploadedFile) {
                    $pointData['image'] = $pointData['image']->store('route_points', 'public');
                }

                $route->points()->create($pointData);
            }
        }

        return new RouteResource($route->load(['agency', 'points.location']));
    }

    /**
     * Display the specified resource.
     */
    public function show(Route $route)
    {
        return new RouteResource($route->load(['agency', 'points.location']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Route $route)
    {
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'short_description' => 'nullable|string|max:255',
            'image' => 'nullable|image|max:2048',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'agency_id' => 'nullable|exists:agencies,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('routes', 'public');
        }

        // Update the route
        $route->update($validated);

        return new RouteResource($route->load(['agency', 'points.location']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Route $route)
    {
        $route->delete();

        return response()->json(['message' => 'Route deleted successfully']);
    }
}
