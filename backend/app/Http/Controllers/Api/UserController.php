<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\GroupResource;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    /**
     * Get the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\UserResource
     */
    public function profile(Request $request)
    {
        $user = Auth::user();
        return new UserResource($user);
    }

    /**
     * Get the authenticated user's groups.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function groups(Request $request)
    {
        $user = Auth::user();
        $groups = $user->groups()->with('users')->get();
        
        return GroupResource::collection($groups);
    }
}
