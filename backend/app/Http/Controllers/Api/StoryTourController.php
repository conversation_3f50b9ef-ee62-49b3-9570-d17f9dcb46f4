<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StoryTourResource;
use App\Models\StoryTour;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Storage;

class StoryTourController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = StoryTour::with(['location', 'agency'])
            ->where('is_active', true);

        // Filter by agency if provided
        if ($request->has('agency_id')) {
            $query->where('agency_id', $request->agency_id);
        }

        // Apply filters if provided
        if ($request->has('territory') && $request->territory !== 'todos') {
            $query->where('territory', $request->territory);
        }

        if ($request->has('modality') && $request->modality !== 'todos') {
            $query->where('modality', $request->modality);
        }

        if ($request->has('type') && $request->type !== 'todos') {
            $query->where('type', $request->type);
        }

        if ($request->has('year_min') && $request->has('year_max')) {
            $query->whereBetween('year', [$request->year_min, $request->year_max]);
        } else if ($request->has('year_min')) {
            $query->where('year', '>=', $request->year_min);
        } else if ($request->has('year_max')) {
            $query->where('year', '<=', $request->year_max);
        }

        // Apply sorting
        $query->orderBy('is_featured', 'desc')
              ->orderBy('created_at', 'desc');

        // Debug the SQL query
        \Log::info('StoryTour query: ' . $query->toSql());
        \Log::info('StoryTour query bindings: ' . json_encode($query->getBindings()));

        $storyTours = $query->get();

        // Debug the result count
        \Log::info('StoryTour count: ' . $storyTours->count());

        return StoryTourResource::collection($storyTours);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|max:2048',
            'type' => 'required|string|max:255',
            'territory' => 'nullable|string|max:255',
            'modality' => 'nullable|string|max:255',
            'has_crafts' => 'boolean',
            'has_artists' => 'boolean',
            'year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'audio_file' => 'nullable|file|mimes:mp3,wav,ogg|max:10240',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi|max:51200',
            'ar_model_file' => 'nullable|file|mimes:glb,gltf|max:20480',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('story_tours', 'public');
            $validated['image'] = $path;
        }

        // Handle audio file upload
        if ($request->hasFile('audio_file')) {
            $path = $request->file('audio_file')->store('story_tours/audio', 'public');
            $validated['audio_file'] = $path;
        }

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            $path = $request->file('video_file')->store('story_tours/video', 'public');
            $validated['video_file'] = $path;
        }

        // Handle AR model file upload
        if ($request->hasFile('ar_model_file')) {
            $path = $request->file('ar_model_file')->store('story_tours/ar_models', 'public');
            $validated['ar_model_file'] = $path;
        }

        $storyTour = StoryTour::create($validated);

        return new StoryTourResource($storyTour->load('location'));
    }

    /**
     * Display the specified resource.
     */
    public function show(StoryTour $storyTour)
    {
        return new StoryTourResource($storyTour->load('location'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StoryTour $storyTour)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|max:2048',
            'type' => 'required|string|max:255',
            'territory' => 'nullable|string|max:255',
            'modality' => 'nullable|string|max:255',
            'has_crafts' => 'boolean',
            'has_artists' => 'boolean',
            'year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'audio_file' => 'nullable|file|mimes:mp3,wav,ogg|max:10240',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi|max:51200',
            'ar_model_file' => 'nullable|file|mimes:glb,gltf|max:20480',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($storyTour->image) {
                Storage::disk('public')->delete($storyTour->image);
            }

            $path = $request->file('image')->store('story_tours', 'public');
            $validated['image'] = $path;
        }

        // Handle audio file upload
        if ($request->hasFile('audio_file')) {
            // Delete old audio file if it exists
            if ($storyTour->audio_file) {
                Storage::disk('public')->delete($storyTour->audio_file);
            }

            $path = $request->file('audio_file')->store('story_tours/audio', 'public');
            $validated['audio_file'] = $path;
        }

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            // Delete old video file if it exists
            if ($storyTour->video_file) {
                Storage::disk('public')->delete($storyTour->video_file);
            }

            $path = $request->file('video_file')->store('story_tours/video', 'public');
            $validated['video_file'] = $path;
        }

        // Handle AR model file upload
        if ($request->hasFile('ar_model_file')) {
            // Delete old AR model file if it exists
            if ($storyTour->ar_model_file) {
                Storage::disk('public')->delete($storyTour->ar_model_file);
            }

            $path = $request->file('ar_model_file')->store('story_tours/ar_models', 'public');
            $validated['ar_model_file'] = $path;
        }

        $storyTour->update($validated);

        return new StoryTourResource($storyTour->load('location'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StoryTour $storyTour)
    {
        // Delete associated files
        if ($storyTour->image) {
            Storage::disk('public')->delete($storyTour->image);
        }

        if ($storyTour->audio_file) {
            Storage::disk('public')->delete($storyTour->audio_file);
        }

        if ($storyTour->video_file) {
            Storage::disk('public')->delete($storyTour->video_file);
        }

        if ($storyTour->ar_model_file) {
            Storage::disk('public')->delete($storyTour->ar_model_file);
        }

        $storyTour->delete();

        return response()->json(['message' => 'StoryTour eliminado correctamente']);
    }

    /**
     * Get story tours by type.
     */
    public function getByType(string $type)
    {
        $storyTours = StoryTour::with('location')
            ->where('type', $type)
            ->where('is_active', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return StoryTourResource::collection($storyTours);
    }

    /**
     * Get filter options for StoryTours.
     */
    public function getFilterOptions(Request $request)
    {
        $query = StoryTour::where('is_active', true);

        // Filter by agency if provided
        if ($request->has('agency_id')) {
            $query->where('agency_id', $request->agency_id);
        }

        // Get unique territories
        $territories = (clone $query)
            ->whereNotNull('territory')
            ->distinct()
            ->pluck('territory')
            ->toArray();

        // Get unique modalities
        $modalities = (clone $query)
            ->whereNotNull('modality')
            ->distinct()
            ->pluck('modality')
            ->toArray();

        // Get unique types
        $types = (clone $query)
            ->whereNotNull('type')
            ->distinct()
            ->pluck('type')
            ->toArray();

        // Get min and max years
        $minYear = (clone $query)
            ->whereNotNull('year')
            ->min('year') ?? 1900;

        $maxYear = (clone $query)
            ->whereNotNull('year')
            ->max('year') ?? date('Y');

        return response()->json([
            'territories' => $territories,
            'modalities' => $modalities,
            'types' => $types,
            'year_range' => [
                'min' => (int) $minYear,
                'max' => (int) $maxYear
            ]
        ]);
    }
}
