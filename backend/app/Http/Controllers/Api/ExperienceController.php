<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ExperienceResource;
use App\Models\Experience;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Storage;

class ExperienceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Experience::query();

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by featured if provided
        if ($request->has('featured')) {
            $query->where('is_featured', $request->featured === 'true');
        }

        // Filter by active if provided
        if ($request->has('active')) {
            $query->where('is_active', $request->active === 'true');
        }

        // Filter by agency if provided
        if ($request->has('agency_id')) {
            $query->where('agency_id', $request->agency_id);
        }

        // Filter by location if provided
        if ($request->has('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        $experiences = $query->with(['location', 'agency'])->latest()->get();

        return ExperienceResource::collection($experiences);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'type' => 'required|string|max:50',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',  // Changed to accept image uploads
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('experiences', 'public');
            $validated['image'] = $path;
        }

        $experience = Experience::create($validated);

        return new ExperienceResource($experience->load(['location', 'agency']));
    }

    /**
     * Display the specified resource.
     */
    public function show(Experience $experience)
    {
        return new ExperienceResource($experience->load(['location', 'agency']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Experience $experience)
    {
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'type' => 'sometimes|string|max:50',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',  // Changed to accept image uploads
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($experience->image) {
                Storage::disk('public')->delete($experience->image);
            }

            $path = $request->file('image')->store('experiences', 'public');
            $validated['image'] = $path;
        }

        $experience->update($validated);

        return new ExperienceResource($experience->load(['location', 'agency']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Experience $experience)
    {
        // Delete the associated image if it exists
        if ($experience->image) {
            Storage::disk('public')->delete($experience->image);
        }

        $experience->delete();

        return response()->json(['message' => 'Experiencia eliminada correctamente']);
    }
}
