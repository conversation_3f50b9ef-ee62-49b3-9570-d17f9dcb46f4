<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AgencyResource;
use App\Models\Agency;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class AgencyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): AnonymousResourceCollection
    {
        // Only return active agencies
        $agencies = Agency::where('is_active', true)
            ->orderBy('name')
            ->paginate(10);

        return AgencyResource::collection($agencies);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // This endpoint is only accessible to superadmins
        if (!$request->user() || !$request->user()->isSuperAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:agencies',
            'description' => 'nullable|string',
            'logo' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $agency = Agency::create($validated);

        return new AgencyResource($agency);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $agency = Agency::where('id', $id)
            ->orWhere('slug', $id)
            ->firstOrFail();

        return new AgencyResource($agency);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // This endpoint is only accessible to superadmins
        if (!$request->user() || !$request->user()->isSuperAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $agency = Agency::findOrFail($id);

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'slug' => 'sometimes|required|string|max:255|unique:agencies,slug,' . $agency->id,
            'description' => 'nullable|string',
            'logo' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $agency->update($validated);

        return new AgencyResource($agency);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // This endpoint is only accessible to superadmins
        if (!auth()->user() || !auth()->user()->isSuperAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $agency = Agency::findOrFail($id);

        // Check if agency has users
        if ($agency->users()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete agency with associated users. Please reassign users first.'
            ], 422);
        }

        $agency->delete();

        return response()->json(['message' => 'Agency deleted successfully']);
    }
}
