<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ReservationRequest;
use App\Http\Resources\ReservationResource;
use App\Models\BookingCode;
use App\Models\Experience;
use App\Models\Group;
use App\Models\Reservation;
use App\Models\User;
use App\Notifications\ReservationCreated;
use App\Notifications\ReservationUpdated;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ReservationController extends Controller
{
    // In Laravel 12, middleware is applied in routes rather than in the controller constructor

    /**
     * Display a listing of the user's reservations.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        $status = $request->query('status');
        $upcoming = $request->boolean('upcoming', false);
        $past = $request->boolean('past', false);

        // Get reservations where the user is directly associated
        $query = $user->reservations()
            ->with(['experience', 'experience.agency', 'group', 'bookingCode'])
            ->orderBy('reservation_date', 'desc');

        // Also get reservations for groups the user belongs to
        $groupIds = $user->groups()->pluck('groups.id')->toArray();
        if (!empty($groupIds)) {
            $query->orWhereIn('group_id', $groupIds);
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($upcoming) {
            $query->upcoming();
        } elseif ($past) {
            $query->past();
        }

        $reservations = $query->paginate(10);

        return ReservationResource::collection($reservations);
    }

    /**
     * Store a newly created reservation in storage.
     *
     * @param  \App\Http\Requests\ReservationRequest  $request
     * @return \App\Http\Resources\ReservationResource
     */
    public function store(ReservationRequest $request)
    {
        \Log::info('Creating reservation with data: ' . json_encode($request->all()));

        $user = Auth::user();
        \Log::info('User ID: ' . $user->id);

        $validated = $request->validated();
        \Log::info('Validated data: ' . json_encode($validated));

        // Check if the user already has a reservation for this experience
        // but on a different day (if it's the same day, validation would have failed)
        if (!$request->has('force_update')) {
            $existingReservation = Reservation::where('user_id', $user->id)
                ->where('experience_id', $validated['experience_id'])
                ->first();

            if ($existingReservation &&
                $existingReservation->reservation_date->format('Y-m-d') !== $validated['reservation_date'] &&
                $existingReservation->reservation_date->greaterThanOrEqualTo(now()) && // Only consider future reservations
                $existingReservation->status !== 'cancelled') { // Don't consider cancelled reservations

                // Return a special response indicating there's an existing reservation
                return response()->json([
                    'has_existing_reservation' => true,
                    'existing_reservation' => new ReservationResource($existingReservation),
                    'message' => 'Ya tienes una reserva para esta experiencia en otra fecha. ¿Deseas modificar tu reserva existente?'
                ], 409); // 409 Conflict
            }
        }

        // Check if this is a group reservation
        $isGroupReservation = $request->has('group_id') && $validated['group_id'];
        $groupId = $isGroupReservation ? $validated['group_id'] : null;

        // If it's a group reservation, verify the user belongs to the group
        if ($isGroupReservation) {
            $group = Group::findOrFail($groupId);
            $userBelongsToGroup = $user->groups()->where('groups.id', $groupId)->exists();

            if (!$userBelongsToGroup) {
                return response()->json([
                    'message' => 'No perteneces a este grupo.'
                ], 403);
            }
        }

        // Create the reservation
        $reservation = new Reservation([
            'user_id' => $user->id,
            'group_id' => $groupId,
            'is_group_reservation' => $isGroupReservation,
            'experience_id' => $validated['experience_id'],
            'reservation_date' => $validated['reservation_date'],
            'reservation_time' => $validated['reservation_time'] ?? null,
            'num_people' => $validated['num_people'],
            'special_requests' => $validated['special_requests'] ?? null,
            'status' => 'pending',
        ]);

        $reservation->save();
        \Log::info('Reservation created with ID: ' . $reservation->id);

        // Load the relationships
        $reservation->load(['user', 'experience.agency', 'group']);

        // Generate booking code for group reservations
        if ($isGroupReservation) {
            $code = BookingCode::generateUniqueCode();

            BookingCode::create([
                'code' => $code,
                'reservation_id' => $reservation->id,
                'group_id' => $groupId,
                'is_active' => true,
            ]);

            // Reload with booking code
            $reservation->load('bookingCode');
        }

        // Send notification to agency admins
        if ($reservation->experience->agency_id) {
            NotificationService::notifyAgencyAdmins(
                $reservation->experience->agency_id,
                new ReservationCreated($reservation)
            );
        }

        return new ReservationResource($reservation);
    }

    /**
     * Display the specified reservation.
     *
     * @param  string  $id
     * @return \App\Http\Resources\ReservationResource
     */
    public function show(string $id): ReservationResource
    {
        $user = Auth::user();
        // Get the reservation by ID
        $query = Reservation::with(['experience', 'user', 'group', 'bookingCode'])
            ->where('id', $id);

        // User can access their own reservations
        $query->where(function($q) use ($user) {
            $q->where('user_id', $user->id);

            // Or reservations for groups they belong to
            $groupIds = $user->groups()->pluck('groups.id')->toArray();
            if (!empty($groupIds)) {
                $q->orWhereIn('group_id', $groupIds);
            }
        });

        $reservation = $query->firstOrFail();

        return new ReservationResource($reservation);
    }

    /**
     * Update the specified reservation in storage.
     *
     * @param  \App\Http\Requests\ReservationRequest  $request
     * @param  string  $id
     * @return \App\Http\Resources\ReservationResource
     */
    public function update(ReservationRequest $request, string $id)
    {
        \Log::info('Updating reservation with ID: ' . $id . ', data: ' . json_encode($request->all()));

        $user = Auth::user();
        $reservation = Reservation::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $validated = $request->validated();
        \Log::info('Validated update data: ' . json_encode($validated));

        // Only allow updates if the reservation is pending or confirmed
        if (!in_array($reservation->status, ['pending', 'confirmed'])) {
            return response()->json([
                'message' => 'No se puede modificar una reserva que ya ha sido cancelada o completada.'
            ], 403);
        }

        // Don't allow updates for past reservations
        if ($reservation->reservation_date->lessThan(now())) {
            return response()->json([
                'message' => 'No se puede modificar una reserva para una fecha pasada.'
            ], 403);
        }

        // If the reservation is confirmed, only allow changes to num_people and special_requests
        if ($reservation->status === 'confirmed') {
            // Check if user is trying to change date, time, or experience
            if (($validated['reservation_date'] != $reservation->reservation_date->format('Y-m-d')) ||
                (isset($validated['reservation_time']) && $validated['reservation_time'] != ($reservation->reservation_time ? $reservation->reservation_time->format('H:i') : null)) ||
                ($validated['experience_id'] != $reservation->experience_id)) {

                return response()->json([
                    'message' => 'Para una reserva confirmada, solo puedes modificar el número de personas y las solicitudes especiales.'
                ], 403);
            }
        }

        // Track changes for notification
        $changes = [];
        $updateData = [
            'experience_id' => $validated['experience_id'],
            'reservation_date' => $validated['reservation_date'],
            'reservation_time' => $validated['reservation_time'] ?? null,
            'num_people' => $validated['num_people'],
            'special_requests' => $validated['special_requests'] ?? null,
        ];

        foreach ($updateData as $key => $value) {
            if ($reservation->$key != $value) {
                $changes[$key] = [
                    'old' => $reservation->$key,
                    'new' => $value
                ];
            }
        }

        // Update the reservation
        $reservation->update($updateData);

        // Load the relationships
        $reservation->load(['user', 'experience.agency']);

        // Send notification to agency admins if there are changes
        if (!empty($changes) && $reservation->experience->agency_id) {
            NotificationService::notifyAgencyAdmins(
                $reservation->experience->agency_id,
                new ReservationUpdated($reservation, $changes)
            );
        }

        return new ReservationResource($reservation);
    }

    /**
     * Cancel the specified reservation.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(string $id)
    {
        $user = Auth::user();
        $reservation = Reservation::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // Only allow cancellation if the reservation is pending or confirmed
        if (!in_array($reservation->status, ['pending', 'confirmed'])) {
            return response()->json([
                'message' => 'No se puede cancelar una reserva que ya ha sido completada o cancelada.'
            ], 403);
        }

        // Update the status to cancelled
        $reservation->update(['status' => 'cancelled']);

        return response()->json([
            'message' => 'Reserva cancelada correctamente.'
        ]);
    }

    /**
     * Get reservations by booking code.
     *
     * @param  string  $code
     * @return \App\Http\Resources\ReservationResource
     */
    public function getByBookingCode(string $code)
    {
        $bookingCode = BookingCode::where('code', $code)
            ->where('is_active', true)
            ->first();

        if (!$bookingCode) {
            return response()->json([
                'message' => 'Código de reserva no válido o expirado.'
            ], 404);
        }

        // Check if the booking code has expired
        if ($bookingCode->expires_at && $bookingCode->expires_at->isPast()) {
            return response()->json([
                'message' => 'El código de reserva ha expirado.'
            ], 403);
        }

        $reservation = $bookingCode->reservation()->with(['experience', 'user', 'group'])->first();

        if (!$reservation) {
            return response()->json([
                'message' => 'La reserva asociada a este código no existe.'
            ], 404);
        }

        return new ReservationResource($reservation);
    }

    /**
     * Get all reservations for the authenticated user.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getUserReservations()
    {
        $user = Auth::user();

        // Get all reservations for the user
        $reservations = Reservation::with(['experience', 'experience.agency', 'group'])
            ->where('user_id', $user->id)
            ->orderBy('reservation_date', 'desc')
            ->get();

        return ReservationResource::collection($reservations);
    }

    /**
     * Get available time slots for a specific experience on a specific date.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableTimeSlots(Request $request)
    {
        $request->validate([
            'experience_id' => 'required|exists:experiences,id',
            'date' => 'required|date|after_or_equal:today',
        ]);

        $experienceId = $request->input('experience_id');
        $date = $request->input('date');

        $experience = Experience::findOrFail($experienceId);

        // Get the available time slots from the experience
        $availableTimeSlots = $experience->available_time_slots ?? [];

        // If no time slots are defined, use default time slots based on experience type
        if (empty($availableTimeSlots)) {
            if ($experience->type === 'restaurant') {
                $availableTimeSlots = [
                    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
                    '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00'
                ];
            } elseif ($experience->type === 'tour') {
                $availableTimeSlots = ['09:00', '10:00', '11:00', '16:00', '17:00'];
            }
        }

        // Get the capacity per time slot
        $capacityPerTimeSlot = $experience->capacity_per_time_slot ?? 10; // Default to 10 if not set

        // Get existing reservations for this experience on this date
        $existingReservations = Reservation::where('experience_id', $experienceId)
            ->where('reservation_date', $date)
            ->where('status', '!=', 'cancelled')
            ->get();

        // Count reservations per time slot
        $reservationsPerTimeSlot = [];
        foreach ($existingReservations as $reservation) {
            $timeSlot = $reservation->reservation_time ? date('H:i', strtotime($reservation->reservation_time)) : null;
            if ($timeSlot) {
                if (!isset($reservationsPerTimeSlot[$timeSlot])) {
                    $reservationsPerTimeSlot[$timeSlot] = 0;
                }
                $reservationsPerTimeSlot[$timeSlot] += $reservation->num_people;
            }
        }

        // Filter out time slots that are at capacity
        $availableTimeSlots = array_filter($availableTimeSlots, function($timeSlot) use ($reservationsPerTimeSlot, $capacityPerTimeSlot) {
            return !isset($reservationsPerTimeSlot[$timeSlot]) || $reservationsPerTimeSlot[$timeSlot] < $capacityPerTimeSlot;
        });

        // Check if we've reached the maximum reservations per day
        $maxReservationsPerDay = $experience->max_reservations_per_day ?? 0;
        $totalReservationsForDay = $existingReservations->sum('num_people');

        // If we've reached the maximum reservations per day, return no available time slots
        if ($maxReservationsPerDay > 0 && $totalReservationsForDay >= $maxReservationsPerDay) {
            $availableTimeSlots = [];
        }

        // Return the available time slots
        return response()->json([
            'date' => $date,
            'experience_id' => $experienceId,
            'time_slots' => array_values($availableTimeSlots), // Reset array keys
            'capacity_per_time_slot' => $capacityPerTimeSlot,
            'max_reservations_per_day' => $maxReservationsPerDay,
            'total_reservations_for_day' => $totalReservationsForDay,
        ]);
    }
}
