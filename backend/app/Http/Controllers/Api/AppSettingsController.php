<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AppSettingsController extends Controller
{
    /**
     * Get all public settings
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $settings = \App\Models\AppSetting::where('is_public', true)->get();
        return response()->json([
            'data' => \App\Http\Resources\AppSettingResource::collection($settings),
        ]);
    }

    /**
     * Get settings by group
     *
     * @param string $group
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByGroup(string $group)
    {
        $settings = \App\Models\AppSetting::where('is_public', true)
            ->where('group', $group)
            ->get();

        return response()->json([
            'data' => \App\Http\Resources\AppSettingResource::collection($settings),
        ]);
    }

    /**
     * Get welcome screen settings
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWelcomeSettings()
    {
        $settings = \App\Models\AppSetting::where('is_public', true)
            ->where('group', 'welcome')
            ->get();

        // Transform to key-value pairs
        $welcomeSettings = [];
        foreach ($settings as $setting) {
            $value = $setting->value;

            // Convert image/video paths to full URLs
            if (in_array($setting->type, ['image', 'video']) && $value) {
                $value = asset('storage/' . $value);
            }

            $welcomeSettings[$setting->key] = $value;
        }

        return response()->json([
            'data' => $welcomeSettings,
        ]);
    }
}
