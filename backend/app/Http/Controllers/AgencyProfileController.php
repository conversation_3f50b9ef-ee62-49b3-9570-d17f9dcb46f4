<?php

namespace App\Http\Controllers;

use App\Models\Agency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class AgencyProfileController extends Controller
{
    /**
     * Show the form for editing the agency profile.
     */
    public function edit()
    {
        $user = Auth::user();
        
        // Debug information
        \Log::info('User accessing agency profile', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role,
            'user_agency_id' => $user->agency_id,
        ]);
        
        // Check if user is associated with an agency
        if (!$user->agency_id) {
            return redirect()->route('dashboard')
                ->with('error', 'No tienes una agencia asociada.');
        }
        
        $agency = Agency::findOrFail($user->agency_id);
        
        // Debug agency information
        \Log::info('Agency found for profile', [
            'agency_id' => $agency->id,
            'agency_name' => $agency->name,
        ]);
        
        return Inertia::render('AgencyProfile/Edit', [
            'agency' => $agency,
        ]);
    }
    
    /**
     * Update the agency profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        // Debug information
        \Log::info('User updating agency profile', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role,
            'user_agency_id' => $user->agency_id,
            'request_data' => $request->all(),
        ]);
        
        // Check if user is associated with an agency
        if (!$user->agency_id) {
            return redirect()->route('dashboard')
                ->with('error', 'No tienes una agencia asociada.');
        }
        
        $agency = Agency::findOrFail($user->agency_id);
        
        // Agency users can only update certain fields
        $validated = $request->validate([
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
        ]);
        
        // Debug validated data
        \Log::info('Validated data for agency profile', [
            'validated' => $validated,
        ]);
        
        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($agency->logo) {
                Storage::disk('public')->delete($agency->logo);
            }

            $path = $request->file('logo')->store('agencies', 'public');
            $validated['logo'] = $path;
            
            \Log::info('Logo uploaded for agency profile', [
                'path' => $path,
            ]);
        }
        
        $agency->update($validated);
        
        \Log::info('Agency profile updated', [
            'agency_id' => $agency->id,
            'agency_name' => $agency->name,
        ]);
        
        return redirect()->route('agency-profile.edit')
            ->with('success', 'Información de la agencia actualizada con éxito');
    }
}
