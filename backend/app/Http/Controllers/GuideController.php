<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GuideController extends Controller
{
    /**
     * Check if the authenticated user is a guide.
     */
    private function checkGuidePermissions()
    {
        $user = Auth::user();

        // Check if user is a guide (either by role or group membership)
        if (!$user || (!$user->isGuide() && $user->role !== 'guide')) {
            abort(403, 'No tienes permisos para acceder a las funciones de guía');
        }

        return $user;
    }

    /**
     * Get dashboard statistics for the guide.
     */
    public function getDashboardStats(): JsonResponse
    {
        try {
            $user = $this->checkGuidePermissions();
            $agencyId = $user->agency_id;

            // Get basic statistics
            $totalExperiences = Experience::where('agency_id', $agencyId)->count();
            $totalGroups = Group::where('agency_id', $agencyId)->count();
            $activeReservations = 0; // TODO: Implement when Reservation model is ready
            $averageRating = 4.5; // TODO: Calculate from actual ratings

            // Get recent activities (mock data for now)
            $recentActivities = [
                [
                    'id' => 1,
                    'type' => 'experience_created',
                    'description' => 'Nueva experiencia "Tour por el centro histórico" creada',
                    'created_at' => now()->subHours(2)->toISOString()
                ],
                [
                    'id' => 2,
                    'type' => 'group_created',
                    'description' => 'Nuevo grupo "Familia García" creado',
                    'created_at' => now()->subHours(5)->toISOString()
                ],
                [
                    'id' => 3,
                    'type' => 'reservation_created',
                    'description' => 'Nueva reserva para "Cata de vinos"',
                    'created_at' => now()->subDay()->toISOString()
                ]
            ];

            return response()->json([
                'data' => [
                    'total_experiences' => $totalExperiences,
                    'total_groups' => $totalGroups,
                    'active_reservations' => $activeReservations,
                    'average_rating' => $averageRating,
                    'recent_activities' => $recentActivities
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting guide dashboard stats', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar las estadísticas del panel'
            ], 500);
        }
    }

    /**
     * Get guide profile and basic agency info (read-only).
     */
    public function getGuideProfile(): JsonResponse
    {
        try {
            $user = $this->checkGuidePermissions();
            $agency = $user->agency;

            // Return limited guide profile info (no agency configuration access)
            $profile = [
                'guide_name' => $user->name,
                'guide_email' => $user->email,
                'agency_name' => $agency->name ?? 'Sin Agencia',
                'agency_logo' => $agency->logo ?? '/assets/logos/CorkExpLogoBlack.png',
                'welcome_message' => 'Gestiona tus rutas y grupos desde aquí',
                'permissions' => [
                    'can_manage_experiences' => true,
                    'can_manage_groups' => true,
                    'can_assign_restaurants' => true,
                    'can_manage_time_slots' => true,
                    'can_view_reservations' => true,
                ],
                'agency_contact' => [
                    'email' => $agency->email ?? '',
                    'phone' => $agency->phone ?? '',
                ]
            ];

            return response()->json(['data' => $profile]);
        } catch (\Exception $e) {
            Log::error('Error getting guide profile', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Error al cargar el perfil del guía'
            ], 500);
        }
    }

    /**
     * Update experience details (guide-specific).
     */
    public function updateExperience(Request $request, $experienceId): JsonResponse
    {
        try {
            $user = $this->checkGuidePermissions();

            $experience = Experience::where('id', $experienceId)
                ->where('agency_id', $user->agency_id)
                ->firstOrFail();

            // Validate the request - guides can only update specific fields
            $validated = $request->validate([
                'guide_notes' => 'sometimes|string|max:1000',
                'special_instructions' => 'sometimes|string|max:1000',
                'meeting_point' => 'sometimes|string|max:255',
                'duration_notes' => 'sometimes|string|max:255',
                'difficulty_notes' => 'sometimes|string|max:255',
                'equipment_needed' => 'sometimes|string|max:500',
                'weather_considerations' => 'sometimes|string|max:500',
            ]);

            // Update experience with validated data
            $experience->update($validated);

            Log::info('Experience updated by guide', [
                'guide_id' => Auth::id(),
                'experience_id' => $experience->id,
                'updated_fields' => array_keys($validated)
            ]);

            return response()->json([
                'message' => 'Experiencia actualizada correctamente',
                'data' => $experience->fresh()
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating experience', [
                'user_id' => Auth::id(),
                'experience_id' => $experienceId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al actualizar la experiencia'
            ], 500);
        }
    }

    /**
     * Get experiences managed by the guide.
     */
    public function getMyExperiences(): JsonResponse
    {
        try {
            $user = $this->checkGuidePermissions();
            $agencyId = $user->agency_id;

            $experiences = Experience::where('agency_id', $agencyId)
                ->with(['location'])
                ->get();

            return response()->json(['data' => $experiences]);
        } catch (\Exception $e) {
            Log::error('Error getting guide experiences', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar las experiencias'
            ], 500);
        }
    }

    /**
     * Get groups managed by the guide.
     */
    public function getMyGroups(): JsonResponse
    {
        try {
            $user = $this->checkGuidePermissions();
            $agencyId = $user->agency_id;

            // Get groups with safe relationship loading
            $groups = Group::where('agency_id', $agencyId)
                ->get()
                ->map(function ($group) {
                    return [
                        'id' => $group->id,
                        'name' => $group->name,
                        'type' => $group->type,
                        'number_of_people' => $group->number_of_people,
                        'group_members' => $group->group_members ?? [],
                        'pet_name' => $group->pet_name,
                        'pet_breed' => $group->pet_breed,
                        'pet_size' => $group->pet_size,
                        'pet_age' => $group->pet_age,
                        'pet_notes' => $group->pet_notes,
                        'special_requirements' => $group->special_requirements,
                        'notes' => $group->notes,
                        'agency_id' => $group->agency_id,
                        'created_at' => $group->created_at,
                        'updated_at' => $group->updated_at,
                        // Add member count for convenience
                        'member_count' => is_array($group->group_members) ? count($group->group_members) : 0,
                    ];
                });

            return response()->json(['data' => $groups]);
        } catch (\Exception $e) {
            Log::error('Error getting guide groups', [
                'user_id' => Auth::id(),
                'agency_id' => $user->agency_id ?? 'null',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Error al cargar los grupos',
                'error' => config('app.debug') ? $e->getMessage() : 'Error interno del servidor'
            ], 500);
        }
    }

    /**
     * Get available restaurants for assignment.
     */
    public function getAvailableRestaurants(): JsonResponse
    {
        try {
            $user = $this->checkGuidePermissions();
            $agencyId = $user->agency_id;

            // For now, return mock restaurant data
            // TODO: Implement actual restaurant model and relationships
            $restaurants = [
                [
                    'id' => 1,
                    'name' => 'Restaurante El Rincón',
                    'description' => 'Cocina tradicional española con productos locales',
                    'cuisine_type' => 'Española',
                    'rating' => 4.5,
                    'price_range' => 'medium',
                    'address' => 'Calle Mayor 15, Madrid',
                    'website' => 'https://elrincon.com',
                    'menu_url' => 'https://elrincon.com/menu',
                    'opening_hours' => '12:00 - 23:00',
                    'image' => '/assets/images/restaurants/el-rincon.jpg'
                ],
                [
                    'id' => 2,
                    'name' => 'Taberna Los Arcos',
                    'description' => 'Tapas y vinos en ambiente acogedor',
                    'cuisine_type' => 'Tapas',
                    'rating' => 4.2,
                    'price_range' => 'low',
                    'address' => 'Plaza de Armas 8, Madrid',
                    'website' => 'https://losarcos.com',
                    'opening_hours' => '18:00 - 02:00',
                    'image' => '/assets/images/restaurants/los-arcos.jpg'
                ]
            ];

            return response()->json(['data' => $restaurants]);
        } catch (\Exception $e) {
            Log::error('Error getting available restaurants', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar los restaurantes'
            ], 500);
        }
    }
}
