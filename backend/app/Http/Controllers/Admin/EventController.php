<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class EventController extends Controller
{
    /**
     * Display a listing of the events.
     */
    public function index()
    {
        $user = auth()->user();
        $query = Event::query();

        // If user is associated with an agency and is not a superadmin, only show events from that agency
        if ($user->agency_id && $user->role !== 'superadmin') {
            $query->where('agency_id', $user->agency_id);
        }

        $events = $query->latest()->get();

        return Inertia::render('Admin/Events/Index', [
            'events' => $events,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
        ]);
    }

    /**
     * Display the specified event.
     */
    public function show(Event $event)
    {
        $user = auth()->user();

        // If user is associated with an agency and is not a superadmin, check if they own this event
        if ($user->agency_id && $user->role !== 'superadmin' && $event->agency_id !== $user->agency_id) {
            abort(403, 'Unauthorized action. You can only view your own events.');
        }

        return Inertia::render('Admin/Events/Show', [
            'event' => $event,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
        ]);
    }

    /**
     * Show the form for creating a new event.
     */
    public function create()
    {
        $user = auth()->user();

        return Inertia::render('Admin/Events/Create', [
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Store a newly created event in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'start_datetime' => 'required|date',
            'end_datetime' => 'nullable|date|after_or_equal:start_datetime',
            'location' => 'required|string|max:255',
            'image' => 'nullable|image|max:2048',  // Changed to accept image uploads
            'rating' => 'nullable|numeric|min:1|max:5',
            'is_featured' => 'boolean',
            'agency_id' => 'nullable|exists:agencies,id',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('events', 'public');
            $validated['image'] = $path;
        }

        $user = auth()->user();

        // If user is associated with an agency, automatically set the agency_id
        if ($user->agency_id && !isset($validated['agency_id'])) {
            $validated['agency_id'] = $user->agency_id;
        }

        Event::create($validated);

        return redirect()->route('admin.events.index')
            ->with('success', 'Evento creado correctamente.');
    }

    /**
     * Show the form for editing the specified event.
     */
    public function edit(Event $event)
    {
        $user = auth()->user();

        // If user is associated with an agency and is not a superadmin, check if they own this event
        if ($user->agency_id && $user->role !== 'superadmin' && $event->agency_id !== $user->agency_id) {
            abort(403, 'Unauthorized action. You can only edit your own events.');
        }

        return Inertia::render('Admin/Events/Edit', [
            'event' => $event,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Update the specified event in storage.
     */
    public function update(Request $request, Event $event)
    {
        $user = auth()->user();

        // If user is associated with an agency and is not a superadmin, check if they own this event
        if ($user->agency_id && $user->role !== 'superadmin' && $event->agency_id !== $user->agency_id) {
            abort(403, 'Unauthorized action. You can only update your own events.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'start_datetime' => 'required|date',
            'end_datetime' => 'nullable|date|after_or_equal:start_datetime',
            'location' => 'required|string|max:255',
            'image' => 'nullable|image|max:2048',  // Changed to accept image uploads
            'rating' => 'nullable|numeric|min:1|max:5',
            'is_featured' => 'boolean',
            'agency_id' => 'nullable|exists:agencies,id',
        ]);

        // Agency users cannot change the agency_id
        if ($user->agency_id && isset($validated['agency_id']) && $validated['agency_id'] !== $user->agency_id) {
            $validated['agency_id'] = $user->agency_id;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($event->image) {
                Storage::disk('public')->delete($event->image);
            }

            $path = $request->file('image')->store('events', 'public');
            $validated['image'] = $path;
        } else {
            // Keep the existing image if no new one is uploaded
            unset($validated['image']);
        }

        $event->update($validated);

        return redirect()->route('admin.events.index')
            ->with('success', 'Evento actualizado correctamente.');
    }

    /**
     * Remove the specified event from storage.
     */
    public function destroy(Event $event)
    {
        $user = auth()->user();

        // If user is associated with an agency and is not a superadmin, check if they own this event
        if ($user->agency_id && $user->role !== 'superadmin' && $event->agency_id !== $user->agency_id) {
            abort(403, 'Unauthorized action. You can only delete your own events.');
        }

        // Delete the associated image if it exists
        if ($event->image) {
            Storage::disk('public')->delete($event->image);
        }

        $event->delete();

        return redirect()->route('admin.events.index')
            ->with('success', 'Evento eliminado correctamente.');
    }
}
