<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BookingCode;
use App\Models\Group;
use App\Models\Reservation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookingCodeController extends Controller
{
    /**
     * Generate a booking code for a reservation.
     */
    public function generate(Request $request, Reservation $reservation)
    {
        $user = Auth::user();

        // Check if the user has access to this reservation
        if ($user->isAgencyUser()) {
            $experience = $reservation->experience;
            if ($experience && $experience->agency_id !== $user->agency_id) {
                abort(403, 'No tienes permiso para generar códigos de reserva para esta reserva.');
            }
        }

        // Check if the reservation already has a booking code
        if ($reservation->bookingCode) {
            return back()->with('error', 'Esta reserva ya tiene un código de reserva.');
        }

        $validated = $request->validate([
            'expires_at' => 'nullable|date|after:now',
        ]);

        // Generate a unique booking code
        $code = BookingCode::generateUniqueCode();

        // Create the booking code
        $bookingCode = BookingCode::create([
            'code' => $code,
            'reservation_id' => $reservation->id,
            'group_id' => $reservation->group_id,
            'expires_at' => $validated['expires_at'] ?? null,
            'is_active' => true,
        ]);

        return back()->with('success', 'Código de reserva generado correctamente: ' . $code);
    }

    /**
     * Deactivate a booking code.
     */
    public function deactivate(BookingCode $bookingCode)
    {
        $user = Auth::user();

        // Check if the user has access to this booking code
        if ($user->isAgencyUser()) {
            $reservation = $bookingCode->reservation;
            if ($reservation) {
                $experience = $reservation->experience;
                if ($experience && $experience->agency_id !== $user->agency_id) {
                    abort(403, 'No tienes permiso para desactivar este código de reserva.');
                }
            }
        }

        // Deactivate the booking code
        $bookingCode->update(['is_active' => false]);

        return back()->with('success', 'Código de reserva desactivado correctamente.');
    }

    /**
     * Activate a booking code.
     */
    public function activate(BookingCode $bookingCode)
    {
        $user = Auth::user();

        // Check if the user has access to this booking code
        if ($user->isAgencyUser()) {
            $reservation = $bookingCode->reservation;
            if ($reservation) {
                $experience = $reservation->experience;
                if ($experience && $experience->agency_id !== $user->agency_id) {
                    abort(403, 'No tienes permiso para activar este código de reserva.');
                }
            }
        }

        // Activate the booking code
        $bookingCode->update(['is_active' => true]);

        return back()->with('success', 'Código de reserva activado correctamente.');
    }

    /**
     * Update the expiration date of a booking code.
     */
    public function updateExpiration(Request $request, BookingCode $bookingCode)
    {
        $user = Auth::user();

        // Check if the user has access to this booking code
        if ($user->isAgencyUser()) {
            $reservation = $bookingCode->reservation;
            if ($reservation) {
                $experience = $reservation->experience;
                if ($experience && $experience->agency_id !== $user->agency_id) {
                    abort(403, 'No tienes permiso para actualizar este código de reserva.');
                }
            }
        }

        $validated = $request->validate([
            'expires_at' => 'nullable|date|after:now',
        ]);

        // Update the booking code
        $bookingCode->update(['expires_at' => $validated['expires_at']]);

        return back()->with('success', 'Fecha de expiración del código de reserva actualizada correctamente.');
    }
}
