<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NewsController extends Controller
{
    /**
     * Display a listing of the news.
     */
    public function index()
    {
        $news = News::latest()->get();

        return Inertia::render('Admin/News/Index', [
            'news' => $news,
        ]);
    }

    /**
     * Show the form for creating a new news item.
     */
    public function create()
    {
        return Inertia::render('Admin/News/Create');
    }

    /**
     * Store a newly created news item in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'summary' => 'required|string',
            'datetime' => 'required|date',
            'location' => 'required|string|max:255',
            'image' => 'nullable|string|max:255',
        ]);

        News::create($validated);

        return redirect()->route('admin.news.index')
            ->with('success', 'News item created successfully.');
    }

    /**
     * Display the specified news item.
     */
    public function show(News $news)
    {
        return Inertia::render('Admin/News/Show', [
            'news' => $news,
        ]);
    }

    /**
     * Show the form for editing the specified news item.
     */
    public function edit(News $news)
    {
        return Inertia::render('Admin/News/Edit', [
            'news' => $news,
        ]);
    }

    /**
     * Update the specified news item in storage.
     */
    public function update(Request $request, News $news)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'summary' => 'required|string',
            'datetime' => 'required|date',
            'location' => 'required|string|max:255',
            'image' => 'nullable|string|max:255',
        ]);

        $news->update($validated);

        return redirect()->route('admin.news.index')
            ->with('success', 'News item updated successfully.');
    }

    /**
     * Remove the specified news item from storage.
     */
    public function destroy(News $news)
    {
        $news->delete();

        return redirect()->route('admin.news.index')
            ->with('success', 'News item deleted successfully.');
    }
}
