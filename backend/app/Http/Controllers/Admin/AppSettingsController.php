<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AppSettingsController extends Controller
{
    /**
     * Display a listing of the app settings.
     */
    public function index(Request $request)
    {
        // Get settings grouped by their group
        $settingsByGroup = \App\Models\AppSetting::orderBy('group')
            ->orderBy('label')
            ->get()
            ->groupBy('group');

        return \Inertia\Inertia::render('Admin/AppSettings/Index', [
            'settingsByGroup' => $settingsByGroup,
        ]);
    }

    /**
     * Show the form for creating a new app setting.
     */
    public function create()
    {
        return \Inertia\Inertia::render('Admin/AppSettings/Create');
    }

    /**
     * Store a newly created app setting in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255|unique:app_settings,key',
            'value' => 'nullable|string',
            'type' => 'required|string|max:50',
            'group' => 'required|string|max:50',
            'label' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        \App\Models\AppSetting::create($validated);

        return redirect()->route('admin.app-settings.index')
            ->with('success', 'Configuración creada correctamente.');
    }

    /**
     * Show the form for editing the specified app setting.
     */
    public function edit(\App\Models\AppSetting $appSetting)
    {
        return \Inertia\Inertia::render('Admin/AppSettings/Edit', [
            'appSetting' => $appSetting,
        ]);
    }

    /**
     * Update the specified app setting in storage.
     */
    public function update(Request $request, \App\Models\AppSetting $appSetting)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255|unique:app_settings,key,' . $appSetting->id,
            'value' => 'nullable|string',
            'type' => 'required|string|max:50',
            'group' => 'required|string|max:50',
            'label' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        // Handle file uploads for image or video types
        if ($request->hasFile('file') && in_array($validated['type'], ['image', 'video'])) {
            $path = $request->file('file')->store($validated['type'] . 's', 'public');
            $validated['value'] = $path;
        }

        $appSetting->update($validated);

        return redirect()->route('admin.app-settings.index')
            ->with('success', 'Configuración actualizada correctamente.');
    }

    /**
     * Remove the specified app setting from storage.
     */
    public function destroy(\App\Models\AppSetting $appSetting)
    {
        $appSetting->delete();

        return redirect()->route('admin.app-settings.index')
            ->with('success', 'Configuración eliminada correctamente.');
    }

    /**
     * Update welcome screen settings.
     */
    public function updateWelcomeScreen(Request $request)
    {
        // Log the incoming request for debugging
        \Log::info('Welcome settings update request', [
            'has_files' => $request->hasFile('welcome_logo') || $request->hasFile('video_file') || $request->hasFile('video_placeholder'),
            'all_data' => $request->all(),
        ]);

        $validated = $request->validate([
            'welcome_title' => 'required|string|max:255',
            'welcome_logo' => 'nullable|file|image|max:2048',
            'video_title' => 'nullable|string|max:255',
            'video_description' => 'nullable|string',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi,webm|max:20480',
            'video_placeholder' => 'nullable|file|image|max:2048',
        ]);

        // Update text settings
        \App\Models\AppSetting::set('welcome_title', $validated['welcome_title'], [
            'type' => 'text',
            'group' => 'welcome',
            'label' => 'Título de Bienvenida',
            'description' => 'Título mostrado en la pantalla de bienvenida',
            'is_public' => true,
        ]);

        \App\Models\AppSetting::set('video_title', $validated['video_title'], [
            'type' => 'text',
            'group' => 'welcome',
            'label' => 'Título del Video',
            'description' => 'Título mostrado en la pantalla de video',
            'is_public' => true,
        ]);

        \App\Models\AppSetting::set('video_description', $validated['video_description'], [
            'type' => 'textarea',
            'group' => 'welcome',
            'label' => 'Descripción del Video',
            'description' => 'Texto descriptivo mostrado debajo del video',
            'is_public' => true,
        ]);

        // Handle file uploads
        if ($request->hasFile('welcome_logo')) {
            try {
                $file = $request->file('welcome_logo');
                $path = $file->store('welcome', 'public');
                \Log::info('Welcome logo uploaded', ['path' => $path, 'original_name' => $file->getClientOriginalName()]);

                \App\Models\AppSetting::set('welcome_logo', $path, [
                    'type' => 'image',
                    'group' => 'welcome',
                    'label' => 'Logo de Bienvenida',
                    'description' => 'Logo mostrado en la pantalla de bienvenida',
                    'is_public' => true,
                ]);
            } catch (\Exception $e) {
                \Log::error('Error uploading welcome logo', ['error' => $e->getMessage()]);
            }
        }

        if ($request->hasFile('video_file')) {
            try {
                $file = $request->file('video_file');
                $path = $file->store('videos', 'public');
                \Log::info('Video file uploaded', ['path' => $path, 'original_name' => $file->getClientOriginalName()]);

                \App\Models\AppSetting::set('welcome_video', $path, [
                    'type' => 'video',
                    'group' => 'welcome',
                    'label' => 'Video de Bienvenida',
                    'description' => 'Video mostrado en la pantalla de bienvenida',
                    'is_public' => true,
                ]);
            } catch (\Exception $e) {
                \Log::error('Error uploading video file', ['error' => $e->getMessage()]);
            }
        }

        if ($request->hasFile('video_placeholder')) {
            try {
                $file = $request->file('video_placeholder');
                $path = $file->store('images', 'public');
                \Log::info('Video placeholder uploaded', ['path' => $path, 'original_name' => $file->getClientOriginalName()]);

                \App\Models\AppSetting::set('video_placeholder', $path, [
                    'type' => 'image',
                    'group' => 'welcome',
                    'label' => 'Imagen de Placeholder del Video',
                    'description' => 'Imagen mostrada como placeholder del video',
                    'is_public' => true,
                ]);
            } catch (\Exception $e) {
                \Log::error('Error uploading video placeholder', ['error' => $e->getMessage()]);
            }
        }

        return redirect()->route('admin.welcome-settings')
            ->with('success', 'Configuración de bienvenida actualizada correctamente.');
    }
}
