<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\NewAgencyUser;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class AgencyUserManagementController extends Controller
{
    /**
     * Display a listing of agency users.
     */
    public function index()
    {
        $user = Auth::user();

        if (!$user->agency_id || $user->role !== 'agency_admin') {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado.');
        }

        $agency = $user->agency;
        $users = User::where('agency_id', $agency->id)->get();

        return Inertia::render('Admin/AgencyUserManagement/Index', [
            'agency' => $agency,
            'users' => $users,
        ]);
    }

    /**
     * Show the form for creating a new agency user.
     */
    public function create()
    {
        $user = Auth::user();

        if (!$user->agency_id || $user->role !== 'agency_admin') {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado.');
        }

        return Inertia::render('Admin/AgencyUserManagement/Create', [
            'agency' => $user->agency,
        ]);
    }

    /**
     * Store a newly created agency user in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        if (!$user->agency_id || $user->role !== 'agency_admin') {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', 'string', Rule::in(['user', 'agency_admin'])],
        ]);

        $newUser = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
            'agency_id' => $user->agency_id,
        ]);

        // Load the agency relationship
        $newUser->load('agency');

        // Notify other agency admins about the new user (except the creator)
        $otherAgencyAdmins = User::where('agency_id', $user->agency_id)
            ->where('role', 'agency_admin')
            ->where('id', '!=', $user->id) // Don't notify the creator
            ->get();

        foreach ($otherAgencyAdmins as $admin) {
            $admin->notify(new NewAgencyUser($newUser));
        }

        return redirect()->route('admin.agency-user-management.index')
            ->with('success', 'Usuario creado correctamente.');
    }

    /**
     * Show the form for editing the specified agency user.
     */
    public function edit(User $user)
    {
        $currentUser = Auth::user();

        if (!$currentUser->agency_id || $currentUser->role !== 'agency_admin' || $user->agency_id !== $currentUser->agency_id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado.');
        }

        return Inertia::render('Admin/AgencyUserManagement/Edit', [
            'agencyUser' => $user,
            'agency' => $currentUser->agency,
        ]);
    }

    /**
     * Update the specified agency user in storage.
     */
    public function update(Request $request, User $user)
    {
        $currentUser = Auth::user();

        if (!$currentUser->agency_id || $currentUser->role !== 'agency_admin' || $user->agency_id !== $currentUser->agency_id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'role' => ['required', 'string', Rule::in(['user', 'agency_admin'])],
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'role' => $validated['role'],
        ];

        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        $user->update($updateData);

        return redirect()->route('admin.agency-user-management.index')
            ->with('success', 'Usuario actualizado correctamente.');
    }

    /**
     * Remove the specified agency user from storage.
     */
    public function destroy(User $user)
    {
        $currentUser = Auth::user();

        if (!$currentUser->agency_id || $currentUser->role !== 'agency_admin' || $user->agency_id !== $currentUser->agency_id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado.');
        }

        // Prevent deleting yourself
        if ($user->id === $currentUser->id) {
            return redirect()->route('admin.agency-user-management.index')
                ->with('error', 'No puedes eliminar tu propio usuario.');
        }

        $user->delete();

        return redirect()->route('admin.agency-user-management.index')
            ->with('success', 'Usuario eliminado correctamente.');
    }
}
