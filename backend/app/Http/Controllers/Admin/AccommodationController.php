<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class AccommodationController extends Controller
{
    /**
     * Display a listing of the accommodations.
     */
    public function index()
    {
        $accommodations = Experience::with(['location', 'agency'])
            ->where('type', 'hotel')
            ->latest()
            ->get();

        return Inertia::render('Admin/Accommodations/Index', [
            'accommodations' => $accommodations,
        ]);
    }

    /**
     * Show the form for creating a new accommodation.
     */
    public function create()
    {
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);

        return Inertia::render('Admin/Accommodations/Create', [
            'locations' => $locations,
            'agencies' => $agencies,
        ]);
    }

    /**
     * Store a newly created accommodation in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:50',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'check_in' => 'nullable|string|max:50',
            'check_out' => 'nullable|string|max:50',
            'amenities' => 'nullable|string',
        ]);

        // Set type to hotel
        $validated['type'] = 'hotel';

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('accommodations', 'public');
            $validated['image'] = $path;
        }

        Experience::create($validated);

        return redirect()->route('admin.accommodations.index')
            ->with('success', 'Alojamiento creado correctamente.');
    }

    /**
     * Display the specified accommodation.
     */
    public function show(Experience $accommodation)
    {
        if ($accommodation->type !== 'hotel') {
            return redirect()->route('admin.accommodations.index')
                ->with('error', 'El recurso solicitado no es un alojamiento.');
        }

        $accommodation->load(['location', 'agency']);

        return Inertia::render('Admin/Accommodations/Show', [
            'accommodation' => $accommodation,
        ]);
    }

    /**
     * Show the form for editing the specified accommodation.
     */
    public function edit(Experience $accommodation)
    {
        if ($accommodation->type !== 'hotel') {
            return redirect()->route('admin.accommodations.index')
                ->with('error', 'El recurso solicitado no es un alojamiento.');
        }

        $locations = Location::orderBy('name')->get(['id', 'name']);
        $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);

        return Inertia::render('Admin/Accommodations/Edit', [
            'accommodation' => $accommodation,
            'locations' => $locations,
            'agencies' => $agencies,
        ]);
    }

    /**
     * Update the specified accommodation in storage.
     */
    public function update(Request $request, Experience $accommodation)
    {
        if ($accommodation->type !== 'hotel') {
            return redirect()->route('admin.accommodations.index')
                ->with('error', 'El recurso solicitado no es un alojamiento.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:50',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'check_in' => 'nullable|string|max:50',
            'check_out' => 'nullable|string|max:50',
            'amenities' => 'nullable|string',
        ]);

        // Type should remain as hotel
        $validated['type'] = 'hotel';

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($accommodation->image) {
                Storage::disk('public')->delete($accommodation->image);
            }

            $path = $request->file('image')->store('accommodations', 'public');
            $validated['image'] = $path;
        }

        $accommodation->update($validated);

        return redirect()->route('admin.accommodations.index')
            ->with('success', 'Alojamiento actualizado correctamente.');
    }

    /**
     * Remove the specified accommodation from storage.
     */
    public function destroy(Experience $accommodation)
    {
        if ($accommodation->type !== 'hotel') {
            return redirect()->route('admin.accommodations.index')
                ->with('error', 'El recurso solicitado no es un alojamiento.');
        }

        // Delete image if it exists
        if ($accommodation->image) {
            Storage::disk('public')->delete($accommodation->image);
        }

        $accommodation->delete();

        return redirect()->route('admin.accommodations.index')
            ->with('success', 'Alojamiento eliminado correctamente.');
    }
}
