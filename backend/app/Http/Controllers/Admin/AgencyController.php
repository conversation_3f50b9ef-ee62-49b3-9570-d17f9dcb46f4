<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\User;
use App\Notifications\NewAgencyCreated;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class AgencyController extends Controller
{
    /**
     * Display a listing of the agencies.
     */
    public function index()
    {
        // Check if user is superadmin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        $agencies = Agency::withCount('users')
            ->orderBy('name')
            ->paginate(10);

        return Inertia::render('Admin/Agencies/Index', [
            'agencies' => $agencies,
        ]);
    }

    /**
     * Show the form for creating a new agency.
     */
    public function create()
    {
        // Check if user is superadmin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        return Inertia::render('Admin/Agencies/Create');
    }

    /**
     * Store a newly created agency in storage.
     */
    public function store(Request $request)
    {
        // Check if user is superadmin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:agencies',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $path = $request->file('logo')->store('agencies', 'public');
            $validated['logo'] = $path;
        }

        $agency = Agency::create($validated);

        // Notify superadmins about the new agency
        NotificationService::notifySuperadmins(new NewAgencyCreated($agency));

        return redirect()->route('admin.agencies.index')
            ->with('success', 'Agencia creada con éxito');
    }

    /**
     * Display the specified agency.
     */
    public function show(Agency $agency)
    {
        $user = auth()->user();

        // Allow superadmins and users from the same agency to view
        if (!$user->isSuperAdmin() && $user->agency_id !== $agency->id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        // If this is an agency user viewing their own agency, redirect to edit page
        if (!$user->isSuperAdmin() && $user->agency_id === $agency->id) {
            return redirect()->route('admin.my-agency.edit');
        }

        $agency->load(['users']);

        return Inertia::render('Admin/Agencies/Show', [
            'agency' => $agency,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Show the form for editing the specified agency.
     */
    public function edit(Agency $agency)
    {
        $user = auth()->user();

        // Allow superadmins and users from the same agency to edit
        if (!$user->isSuperAdmin() && $user->agency_id !== $agency->id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        return Inertia::render('Admin/Agencies/Edit', [
            'agency' => $agency,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Update the specified agency in storage.
     */
    public function update(Request $request, Agency $agency)
    {
        $user = auth()->user();

        // Allow superadmins and users from the same agency to update
        if (!$user->isSuperAdmin() && $user->agency_id !== $agency->id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        // Agency users can only update certain fields
        $validationRules = [
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
        ];

        // Superadmins can update additional fields
        if ($user->isSuperAdmin()) {
            $validationRules = array_merge($validationRules, [
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:agencies,slug,' . $agency->id,
                'is_active' => 'boolean',
            ]);
        }

        $validated = $request->validate($validationRules);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($agency->logo) {
                Storage::disk('public')->delete($agency->logo);
            }

            $path = $request->file('logo')->store('agencies', 'public');
            $validated['logo'] = $path;
        }

        $agency->update($validated);

        return redirect()->route('admin.agencies.index')
            ->with('success', 'Agencia actualizada con éxito');
    }

    /**
     * Remove the specified agency from storage.
     */
    public function destroy(Agency $agency)
    {
        // Check if user is superadmin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        // Check if agency has users
        if ($agency->users()->count() > 0) {
            return redirect()->route('admin.agencies.index')
                ->with('error', 'No se puede eliminar la agencia con usuarios asociados. Por favor, reasigne los usuarios primero.');
        }

        // Delete logo if exists
        if ($agency->logo) {
            Storage::disk('public')->delete($agency->logo);
        }

        $agency->delete();

        return redirect()->route('admin.agencies.index')
            ->with('success', 'Agencia eliminada con éxito');
    }

    /**
     * Show the form for managing agency users.
     */
    public function manageUsers(Agency $agency)
    {
        $user = auth()->user();

        // Allow superadmins and users from the same agency to view users
        if (!$user->isSuperAdmin() && $user->agency_id !== $agency->id) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        $agency->load(['users']);

        // Superadmins can see all available users, agency admins can see users without an agency
        $availableUsers = [];
        if ($user->isSuperAdmin()) {
            $availableUsers = User::whereNull('agency_id')
                ->orWhere('agency_id', '!=', $agency->id)
                ->get();
        } elseif ($user->isAgencyAdmin() && $user->agency_id === $agency->id) {
            // Agency admins can only add users without an agency
            $availableUsers = User::whereNull('agency_id')->get();
        }

        return Inertia::render('Admin/Agencies/ManageUsers', [
            'agency' => $agency,
            'availableUsers' => $availableUsers,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->isSuperAdmin(),
            'isAgencyAdmin' => $user->isAgencyAdmin(),
            'canAddUsers' => $user->isSuperAdmin() || $user->isAgencyAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Add users to the agency.
     */
    public function addUsers(Request $request, Agency $agency)
    {
        // Check if user is superadmin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        User::whereIn('id', $validated['user_ids'])->update(['agency_id' => $agency->id]);

        return redirect()->route('admin.agencies.manage-users', $agency)
            ->with('success', 'Usuarios agregados a la agencia con éxito');
    }

    /**
     * Remove a user from the agency.
     */
    public function removeUser(Agency $agency, User $user)
    {
        // Check if user is superadmin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->route('dashboard')->with('error', 'Acceso no autorizado');
        }

        // Check if user belongs to the agency
        if ($user->agency_id !== $agency->id) {
            return redirect()->route('admin.agencies.manage-users', $agency)
                ->with('error', 'El usuario no pertenece a esta agencia');
        }

        $user->update(['agency_id' => null]);

        return redirect()->route('admin.agencies.manage-users', $agency)
            ->with('success', 'Usuario eliminado de la agencia con éxito');
    }

    /**
     * Show the form for agency users to edit their own agency.
     */
    public function editMyAgency()
    {
        $user = Auth::user();

        // Debug information
        \Log::info('User accessing editMyAgency', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role,
            'user_agency_id' => $user->agency_id,
        ]);

        // Check if user is associated with an agency
        if (!$user->agency_id) {
            return redirect()->route('dashboard')
                ->with('error', 'No tienes una agencia asociada.');
        }

        $agency = Agency::findOrFail($user->agency_id);

        // Debug agency information
        \Log::info('Agency found', [
            'agency_id' => $agency->id,
            'agency_name' => $agency->name,
        ]);

        // Create a custom form specifically for agency users
        return Inertia::render('Admin/Agencies/EditMyAgency', [
            'agency' => $agency,
        ]);
    }

    /**
     * Update the agency information for agency users.
     */
    public function updateMyAgency(Request $request)
    {
        $user = Auth::user();

        // Debug information
        \Log::info('User updating agency', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role,
            'user_agency_id' => $user->agency_id,
            'request_data' => $request->all(),
        ]);

        // Check if user is associated with an agency
        if (!$user->agency_id) {
            return redirect()->route('dashboard')
                ->with('error', 'No tienes una agencia asociada.');
        }

        $agency = Agency::findOrFail($user->agency_id);

        // Agency users can only update certain fields
        $validated = $request->validate([
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
        ]);

        // Debug validated data
        \Log::info('Validated data', [
            'validated' => $validated,
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($agency->logo) {
                Storage::disk('public')->delete($agency->logo);
            }

            $path = $request->file('logo')->store('agencies', 'public');
            $validated['logo'] = $path;

            \Log::info('Logo uploaded', [
                'path' => $path,
            ]);
        }

        $agency->update($validated);

        \Log::info('Agency updated', [
            'agency_id' => $agency->id,
            'agency_name' => $agency->name,
        ]);

        return redirect()->route('admin.my-agency.edit')
            ->with('success', 'Información de la agencia actualizada con éxito');
    }
}
