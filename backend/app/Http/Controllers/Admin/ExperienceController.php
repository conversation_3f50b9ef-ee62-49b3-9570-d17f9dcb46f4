<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ExperienceController extends Controller
{
    /**
     * Display a listing of the experiences.
     */
    public function index()
    {
        $user = auth()->user();
        $query = Experience::with(['location', 'agency']);

        // If user is associated with an agency and is not a superadmin, only show experiences from that agency
        if ($user->agency_id && $user->role !== 'superadmin') {
            $query->where('agency_id', $user->agency_id);
        }

        $experiences = $query->latest()->get();

        return Inertia::render('Admin/Experiences/Index', [
            'experiences' => $experiences,
        ]);
    }

    /**
     * Show the form for creating a new experience.
     */
    public function create()
    {
        $user = auth()->user();
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $agencies = [];

        // Only superadmins can select an agency
        if ($user->role === 'superadmin') {
            $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);
        } else if ($user->agency_id) {
            // For agency users, just get their own agency for display
            $agencies = Agency::where('id', $user->agency_id)->get(['id', 'name']);
        }

        return Inertia::render('Admin/Experiences/Create', [
            'locations' => $locations,
            'agencies' => $agencies,
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Store a newly created experience in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'type' => 'required|string|max:50',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',  // Changed to accept image uploads
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            // Restaurant-specific fields
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:50',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'cuisine_type' => 'nullable|string|max:100',
            'opening_hours' => 'nullable|string',
            'menu_url' => 'nullable|string|max:255',
            // Time slot and capacity fields
            'available_time_slots' => 'nullable|array',
            'available_time_slots.*' => 'nullable|string|date_format:H:i',
            'recurring_time_slots' => 'nullable|array',
            'use_recurring_slots' => 'boolean',
            'capacity_per_time_slot' => 'nullable|integer|min:1',
            'max_reservations_per_day' => 'nullable|integer|min:1',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('experiences', 'public');
            $validated['image'] = $path;
        }

        $user = auth()->user();

        // If user is associated with an agency, automatically set the agency_id
        if ($user->agency_id && !isset($validated['agency_id'])) {
            $validated['agency_id'] = $user->agency_id;
        }

        Experience::create($validated);

        return redirect()->route('admin.experiences.index')
            ->with('success', 'Experiencia creada correctamente.');
    }

    /**
     * Display the specified experience.
     */
    public function show(Experience $experience)
    {
        $experience->load(['location', 'agency']);

        return Inertia::render('Admin/Experiences/Show', [
            'experience' => $experience,
        ]);
    }

    /**
     * Show the form for editing the specified experience.
     */
    public function edit(Experience $experience)
    {
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);

        return Inertia::render('Admin/Experiences/Edit', [
            'experience' => $experience,
            'locations' => $locations,
            'agencies' => $agencies,
        ]);
    }

    /**
     * Update the specified experience in storage.
     */
    public function update(Request $request, Experience $experience)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'type' => 'required|string|max:50',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',  // Changed to accept image uploads
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            // Restaurant-specific fields
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:50',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'cuisine_type' => 'nullable|string|max:100',
            'opening_hours' => 'nullable|string',
            'menu_url' => 'nullable|string|max:255',
            // Time slot and capacity fields
            'available_time_slots' => 'nullable|array',
            'available_time_slots.*' => 'nullable|string|date_format:H:i',
            'recurring_time_slots' => 'nullable|array',
            'use_recurring_slots' => 'boolean',
            'capacity_per_time_slot' => 'nullable|integer|min:1',
            'max_reservations_per_day' => 'nullable|integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($experience->image) {
                Storage::disk('public')->delete($experience->image);
            }

            $path = $request->file('image')->store('experiences', 'public');
            $validated['image'] = $path;
        } else {
            // Keep the existing image if no new one is uploaded
            unset($validated['image']);
        }

        $experience->update($validated);

        return redirect()->route('admin.experiences.index')
            ->with('success', 'Experiencia actualizada correctamente.');
    }

    /**
     * Remove the specified experience from storage.
     */
    public function destroy(Experience $experience)
    {
        // Delete the associated image if it exists
        if ($experience->image) {
            Storage::disk('public')->delete($experience->image);
        }

        $experience->delete();

        return redirect()->route('admin.experiences.index')
            ->with('success', 'Experiencia eliminada correctamente.');
    }
}
