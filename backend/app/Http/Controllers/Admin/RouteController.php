<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Location;
use App\Models\Route;
use App\Models\RoutePoint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RouteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Return a very simple view with no data to avoid JavaScript errors
        return Inertia::render('Admin/Routes/RoutesIndex', [
            'message' => 'Esta sección está en mantenimiento. Por favor, inténtelo más tarde.'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();
        $locations = Location::orderBy('name')->get();
        $agencies = [];

        // Only superadmins can select an agency
        if ($user->role === 'superadmin') {
            $agencies = Agency::orderBy('name')->get();
        } else if ($user->agency_id) {
            // For agency users, just get their own agency for display
            $agencies = Agency::where('id', $user->agency_id)->get();
        }

        return Inertia::render('Admin/Routes/Create', [
            'agencies' => $agencies,
            'locations' => $locations,
            'userAgencyId' => $user->agency_id,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'image' => 'nullable|image|max:2048',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'agency_id' => 'nullable|exists:agencies,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'points' => 'nullable|array',
            'points.*.location_id' => 'nullable|exists:locations,id',
            'points.*.order' => 'integer',
            'points.*.description' => 'nullable|string',
            'points.*.image' => 'nullable|image|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('routes', 'public');
        }

        $user = auth()->user();

        // If user is associated with an agency, automatically set the agency_id
        if ($user->agency_id && !isset($validated['agency_id'])) {
            $validated['agency_id'] = $user->agency_id;
        }

        // Create the route
        $route = Route::create($validated);

        // Create route points if provided
        if ($request->has('points') && is_array($request->points)) {
            foreach ($request->points as $index => $pointData) {
                $pointData['order'] = $pointData['order'] ?? $index;

                // Handle point image upload
                if (isset($pointData['image']) && $pointData['image'] instanceof \Illuminate\Http\UploadedFile) {
                    $pointData['image'] = $pointData['image']->store('route_points', 'public');
                }

                $route->points()->create($pointData);
            }
        }

        return redirect()->route('admin.routes.index')
            ->with('success', 'Ruta creada exitosamente.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Route $route)
    {
        $user = auth()->user();
        $route->load(['agency', 'points.location']);

        // Make sure points is an array, even if empty
        if (!isset($route->points)) {
            $route->points = [];
        }

        return Inertia::render('Admin/Routes/Show', [
            'route' => $route,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Route $route)
    {
        $user = auth()->user();

        // Check if user has permission to edit this route
        if ($user->role !== 'superadmin' && $user->agency_id !== $route->agency_id) {
            return redirect()->route('admin.routes.index')
                ->with('error', 'No tienes permiso para editar esta ruta.');
        }

        $route->load('points');

        // Make sure points is an array, even if empty
        if (!isset($route->points)) {
            $route->points = [];
        }

        $agencies = [];

        // Only superadmins can select an agency
        if ($user->role === 'superadmin') {
            $agencies = Agency::orderBy('name')->get();
        } else if ($user->agency_id) {
            // For agency users, just get their own agency for display
            $agencies = Agency::where('id', $user->agency_id)->get();
        }

        $locations = Location::orderBy('name')->get();

        return Inertia::render('Admin/Routes/Edit', [
            'route' => $route,
            'agencies' => $agencies,
            'locations' => $locations,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Route $route)
    {
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'short_description' => 'nullable|string|max:255',
            'image' => 'nullable|image|max:2048',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'difficulty' => 'nullable|string|max:50',
            'agency_id' => 'nullable|exists:agencies,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'points' => 'nullable|array',
            'points.*.id' => 'nullable|integer|exists:route_points,id',
            'points.*.location_id' => 'nullable|exists:locations,id',
            'points.*.order' => 'integer',
            'points.*.description' => 'nullable|string',
            'points.*.image' => 'nullable|image|max:2048',
            'deleted_points' => 'nullable|array',
            'deleted_points.*' => 'integer|exists:route_points,id',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($route->image) {
                Storage::disk('public')->delete($route->image);
            }
            $validated['image'] = $request->file('image')->store('routes', 'public');
        }

        // Update the route
        $route->update($validated);

        // Update or create route points
        if ($request->has('points') && is_array($request->points)) {
            foreach ($request->points as $index => $pointData) {
                $pointData['order'] = $pointData['order'] ?? $index;

                // If point has ID, update it
                if (isset($pointData['id'])) {
                    $point = RoutePoint::find($pointData['id']);
                    if ($point) {
                        // Handle point image upload
                        if (isset($pointData['image']) && $pointData['image'] instanceof \Illuminate\Http\UploadedFile) {
                            // Delete old image if exists
                            if ($point->image) {
                                Storage::disk('public')->delete($point->image);
                            }
                            $pointData['image'] = $pointData['image']->store('route_points', 'public');
                        }
                        $point->update($pointData);
                    }
                } else {
                    // Create new point
                    // Handle point image upload
                    if (isset($pointData['image']) && $pointData['image'] instanceof \Illuminate\Http\UploadedFile) {
                        $pointData['image'] = $pointData['image']->store('route_points', 'public');
                    }
                    $route->points()->create($pointData);
                }
            }
        }

        // Delete points if needed
        if ($request->has('deleted_points') && is_array($request->deleted_points)) {
            foreach ($request->deleted_points as $pointId) {
                $point = RoutePoint::find($pointId);
                if ($point && $point->route_id === $route->id) {
                    // Delete image if exists
                    if ($point->image) {
                        Storage::disk('public')->delete($point->image);
                    }
                    $point->delete();
                }
            }
        }

        return redirect()->route('admin.routes.index')
            ->with('success', 'Ruta actualizada exitosamente.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Route $route)
    {
        // Delete route image if exists
        if ($route->image) {
            Storage::disk('public')->delete($route->image);
        }

        // Delete point images if exist
        foreach ($route->points as $point) {
            if ($point->image) {
                Storage::disk('public')->delete($point->image);
            }
        }

        $route->delete();

        return redirect()->route('admin.routes.index')
            ->with('success', 'Ruta eliminada exitosamente.');
    }
}
