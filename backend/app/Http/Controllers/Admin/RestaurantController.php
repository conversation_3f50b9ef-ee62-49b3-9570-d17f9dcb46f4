<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RestaurantController extends Controller
{
    /**
     * Display a listing of the restaurants.
     */
    public function index()
    {
        $restaurants = Experience::with(['location', 'agency'])
            ->where('type', 'restaurant')
            ->latest()
            ->get();

        return Inertia::render('Admin/Restaurants/Index', [
            'restaurants' => $restaurants,
        ]);
    }

    /**
     * Show the form for creating a new restaurant.
     */
    public function create()
    {
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);

        return Inertia::render('Admin/Restaurants/Create', [
            'locations' => $locations,
            'agencies' => $agencies,
        ]);
    }

    /**
     * Store a newly created restaurant in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:50',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'cuisine_type' => 'nullable|string|max:100',
            'opening_hours' => 'nullable|string',
            'menu_url' => 'nullable|string|max:255',
        ]);

        // Set type to restaurant
        $validated['type'] = 'restaurant';

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('restaurants', 'public');
            $validated['image'] = $path;
        }

        Experience::create($validated);

        return redirect()->route('admin.restaurants.index')
            ->with('success', 'Restaurante creado correctamente.');
    }

    /**
     * Display the specified restaurant.
     */
    public function show(Experience $restaurant)
    {
        if ($restaurant->type !== 'restaurant') {
            return redirect()->route('admin.restaurants.index')
                ->with('error', 'El recurso solicitado no es un restaurante.');
        }

        $restaurant->load(['location', 'agency']);

        return Inertia::render('Admin/Restaurants/Show', [
            'restaurant' => $restaurant,
        ]);
    }

    /**
     * Show the form for editing the specified restaurant.
     */
    public function edit(Experience $restaurant)
    {
        if ($restaurant->type !== 'restaurant') {
            return redirect()->route('admin.restaurants.index')
                ->with('error', 'El recurso solicitado no es un restaurante.');
        }

        $locations = Location::orderBy('name')->get(['id', 'name']);
        $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);

        return Inertia::render('Admin/Restaurants/Edit', [
            'restaurant' => $restaurant,
            'locations' => $locations,
            'agencies' => $agencies,
        ]);
    }

    /**
     * Update the specified restaurant in storage.
     */
    public function update(Request $request, Experience $restaurant)
    {
        if ($restaurant->type !== 'restaurant') {
            return redirect()->route('admin.restaurants.index')
                ->with('error', 'El recurso solicitado no es un restaurante.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:255',
            'location_id' => 'nullable|exists:locations,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'duration' => 'nullable|string|max:50',
            'distance' => 'nullable|string|max:50',
            'price' => 'nullable|numeric',
            'image' => 'nullable|image|max:2048',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:50',
            'email' => 'nullable|string|email|max:255',
            'website' => 'nullable|string|max:255',
            'cuisine_type' => 'nullable|string|max:100',
            'opening_hours' => 'nullable|string',
            'menu_url' => 'nullable|string|max:255',
        ]);

        // Type should remain as restaurant
        $validated['type'] = 'restaurant';

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($restaurant->image) {
                Storage::disk('public')->delete($restaurant->image);
            }

            $path = $request->file('image')->store('restaurants', 'public');
            $validated['image'] = $path;
        }

        $restaurant->update($validated);

        return redirect()->route('admin.restaurants.index')
            ->with('success', 'Restaurante actualizado correctamente.');
    }

    /**
     * Remove the specified restaurant from storage.
     */
    public function destroy(Experience $restaurant)
    {
        if ($restaurant->type !== 'restaurant') {
            return redirect()->route('admin.restaurants.index')
                ->with('error', 'El recurso solicitado no es un restaurante.');
        }

        // Delete image if it exists
        if ($restaurant->image) {
            Storage::disk('public')->delete($restaurant->image);
        }

        $restaurant->delete();

        return redirect()->route('admin.restaurants.index')
            ->with('success', 'Restaurante eliminado correctamente.');
    }
}
