<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Experience;
use App\Models\Group;
use App\Models\Reservation;
use App\Models\User;
use App\Models\BookingCode;
use App\Notifications\ReservationCreated;
use App\Notifications\ReservationUpdated;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ReservationController extends Controller
{
    // In Laravel 12, middleware is applied in routes rather than in the controller constructor

    /**
     * Display a listing of the reservations.
     */
    public function index(Request $request)
    {
        \Log::info('Admin: Fetching reservations');

        $user = Auth::user();
        \Log::info('Admin: User ID: ' . $user->id . ', Role: ' . $user->role);

        $query = Reservation::with(['user', 'experience', 'experience.agency', 'group', 'bookingCode'])
            ->orderBy('reservation_date', 'desc');

        // Log the total number of reservations
        \Log::info('Admin: Total reservations: ' . Reservation::count());

        // Filter by status if provided
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('date_from') && $request->date_from) {
            $query->where('reservation_date', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->where('reservation_date', '<=', $request->date_to);
        }

        // Filter by experience if provided
        if ($request->has('experience_id') && $request->experience_id) {
            $query->where('experience_id', $request->experience_id);
        }

        // Filter by user if provided
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by group if provided
        if ($request->has('group_id') && $request->group_id) {
            $query->where('group_id', $request->group_id);
        }

        // Filter by reservation type (individual or group)
        if ($request->has('reservation_type')) {
            if ($request->reservation_type === 'individual') {
                $query->where('is_group_reservation', false);
            } elseif ($request->reservation_type === 'group') {
                $query->where('is_group_reservation', true);
            }
        }

        // If agency user, only show reservations for their agency's experiences
        if ($user->isAgencyUser()) {
            $query->whereHas('experience', function ($q) use ($user) {
                $q->where('agency_id', $user->agency_id);
            });
        }

        $reservations = $query->paginate(10)->withQueryString();

        // Get all experiences for the filter dropdown
        $experiencesQuery = Experience::orderBy('title');
        if ($user->isAgencyUser()) {
            $experiencesQuery->where('agency_id', $user->agency_id);
        }
        $experiences = $experiencesQuery->get(['id', 'title']);

        // Get all users for the filter dropdown (only for superadmins)
        $users = $user->isSuperAdmin() ? User::orderBy('name')->get(['id', 'name', 'email']) : [];

        return Inertia::render('Admin/Reservations/Index', [
            'reservations' => $reservations,
            'experiences' => $experiences,
            'users' => $users,
            'filters' => $request->only(['status', 'date_from', 'date_to', 'experience_id', 'user_id']),
            'statuses' => [
                'all' => 'Todos',
                'pending' => 'Pendiente',
                'confirmed' => 'Confirmada',
                'cancelled' => 'Cancelada',
                'completed' => 'Completada',
            ],
        ]);
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create()
    {
        $user = Auth::user();

        // Get all experiences for the dropdown
        $experiencesQuery = Experience::where('is_active', true)
            ->orderBy('title');

        // If agency user, only show their agency's experiences
        if ($user->isAgencyUser()) {
            $experiencesQuery->where('agency_id', $user->agency_id);
        }

        $experiences = $experiencesQuery->get(['id', 'title', 'type']);

        // Get all users for the dropdown
        $usersQuery = User::orderBy('name');
        if (!$user->isSuperAdmin()) {
            // Regular admins can only create reservations for regular users
            $usersQuery->where('role', 'user');
        }
        $users = $usersQuery->get(['id', 'name', 'email']);

        // Get all groups for the dropdown
        $groupsQuery = Group::where('is_active', true)->orderBy('name');
        if ($user->isAgencyUser()) {
            $groupsQuery->where('agency_id', $user->agency_id);
        }
        $groups = $groupsQuery->get(['id', 'name', 'type']);

        return Inertia::render('Admin/Reservations/Create', [
            'experiences' => $experiences,
            'users' => $users,
            'groups' => $groups,
            'statuses' => [
                'pending' => 'Pendiente',
                'confirmed' => 'Confirmada',
                'cancelled' => 'Cancelada',
                'completed' => 'Completada',
            ],
        ]);
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'user_id' => 'required_without:group_id|exists:users,id',
            'group_id' => 'required_without:user_id|exists:groups,id',
            'is_group_reservation' => 'boolean',
            'experience_id' => 'required|exists:experiences,id',
            'reservation_date' => 'required|date',
            'reservation_time' => 'nullable|date_format:H:i',
            'num_people' => 'required|integer|min:1|max:50',
            'special_requests' => 'nullable|string|max:1000',
            'status' => 'required|in:pending,confirmed,cancelled,completed',
            'admin_notes' => 'nullable|string|max:1000',
            'generate_booking_code' => 'boolean',
            'booking_code_expires_at' => 'nullable|date|after:now',
        ]);

        // Check if the user has permission to create a reservation for this experience
        if ($user->isAgencyUser()) {
            $experience = Experience::findOrFail($validated['experience_id']);
            if ($experience->agency_id !== $user->agency_id) {
                return back()->with('error', 'No tienes permiso para crear reservas para esta experiencia.');
            }
        }

        // Determine if this is a group reservation
        $isGroupReservation = $request->has('is_group_reservation') ? $validated['is_group_reservation'] : false;

        // Create the reservation
        $reservation = Reservation::create([
            'user_id' => $validated['user_id'] ?? null,
            'group_id' => $validated['group_id'] ?? null,
            'is_group_reservation' => $isGroupReservation,
            'experience_id' => $validated['experience_id'],
            'reservation_date' => $validated['reservation_date'],
            'reservation_time' => $validated['reservation_time'] ?? null,
            'num_people' => $validated['num_people'],
            'special_requests' => $validated['special_requests'] ?? null,
            'status' => $validated['status'],
            'admin_notes' => $validated['admin_notes'] ?? null,
        ]);

        // Load the experience with agency
        $reservation->load(['experience.agency']);

        // Generate booking code if requested
        if ($request->has('generate_booking_code') && $validated['generate_booking_code']) {
            $code = BookingCode::generateUniqueCode();

            BookingCode::create([
                'code' => $code,
                'reservation_id' => $reservation->id,
                'group_id' => $validated['group_id'] ?? null,
                'expires_at' => $validated['booking_code_expires_at'] ?? null,
                'is_active' => true,
            ]);
        }

        // Send notification to agency users
        if ($reservation->experience->agency_id) {
            // Get agency admin users
            $agencyAdmins = User::where('agency_id', $reservation->experience->agency_id)
                ->where('role', 'agency_admin')
                ->get();

            // Notify agency admins
            foreach ($agencyAdmins as $admin) {
                $admin->notify(new ReservationCreated($reservation));
            }
        }

        return redirect()->route('admin.reservations.index')
            ->with('success', 'Reserva creada correctamente.');
    }

    /**
     * Display the specified reservation.
     */
    public function show(string $id)
    {
        $user = Auth::user();
        $reservation = Reservation::with(['user', 'experience', 'experience.agency'])
            ->findOrFail($id);

        // Check if the user has permission to view this reservation
        if ($user->isAgencyUser() && $reservation->experience->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para ver esta reserva.');
        }

        return Inertia::render('Admin/Reservations/Show', [
            'reservation' => $reservation,
            'statuses' => [
                'pending' => 'Pendiente',
                'confirmed' => 'Confirmada',
                'cancelled' => 'Cancelada',
                'completed' => 'Completada',
            ],
        ]);
    }

    /**
     * Show the form for editing the specified reservation.
     */
    public function edit(string $id)
    {
        $user = Auth::user();
        $reservation = Reservation::with(['user', 'experience'])
            ->findOrFail($id);

        // Check if the user has permission to edit this reservation
        if ($user->isAgencyUser() && $reservation->experience->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para editar esta reserva.');
        }

        // Get all experiences for the dropdown
        $experiencesQuery = Experience::orderBy('title');
        if ($user->isAgencyUser()) {
            $experiencesQuery->where('agency_id', $user->agency_id);
        }
        $experiences = $experiencesQuery->get(['id', 'title', 'type']);

        // Get all users for the dropdown
        $usersQuery = User::orderBy('name');
        if (!$user->isSuperAdmin()) {
            // Regular admins can only assign reservations to regular users
            $usersQuery->where('role', 'user');
        }
        $users = $usersQuery->get(['id', 'name', 'email']);

        return Inertia::render('Admin/Reservations/Edit', [
            'reservation' => $reservation,
            'experiences' => $experiences,
            'users' => $users,
            'statuses' => [
                'pending' => 'Pendiente',
                'confirmed' => 'Confirmada',
                'cancelled' => 'Cancelada',
                'completed' => 'Completada',
            ],
        ]);
    }

    /**
     * Update the specified reservation in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = Auth::user();
        $reservation = Reservation::findOrFail($id);

        // Check if the user has permission to update this reservation
        if ($user->isAgencyUser()) {
            $experience = Experience::findOrFail($reservation->experience_id);
            if ($experience->agency_id !== $user->agency_id) {
                return back()->with('error', 'No tienes permiso para actualizar esta reserva.');
            }
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'experience_id' => 'required|exists:experiences,id',
            'reservation_date' => 'required|date',
            'reservation_time' => 'nullable|date_format:H:i',
            'num_people' => 'required|integer|min:1|max:50',
            'special_requests' => 'nullable|string|max:1000',
            'status' => 'required|in:pending,confirmed,cancelled,completed',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        // If the experience is being changed, check if the user has permission for the new experience
        if ($user->isAgencyUser() && $validated['experience_id'] != $reservation->experience_id) {
            $newExperience = Experience::findOrFail($validated['experience_id']);
            if ($newExperience->agency_id !== $user->agency_id) {
                return back()->with('error', 'No tienes permiso para asignar esta reserva a la experiencia seleccionada.');
            }
        }

        // Track changes for notification
        $changes = [];
        foreach ($validated as $key => $value) {
            if ($reservation->$key != $value) {
                $changes[$key] = [
                    'old' => $reservation->$key,
                    'new' => $value
                ];
            }
        }

        // Update the reservation
        $reservation->update($validated);

        // Load the experience with agency
        $reservation->load(['experience.agency']);

        // Send notification to agency users if there are changes
        if (!empty($changes) && $reservation->experience->agency_id) {
            // Get agency admin users
            $agencyAdmins = User::where('agency_id', $reservation->experience->agency_id)
                ->where('role', 'agency_admin')
                ->get();

            // Notify agency admins
            foreach ($agencyAdmins as $admin) {
                $admin->notify(new ReservationUpdated($reservation, $changes));
            }
        }

        return redirect()->route('admin.reservations.index')
            ->with('success', 'Reserva actualizada correctamente.');
    }

    /**
     * Remove the specified reservation from storage.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();
        $reservation = Reservation::with('experience')->findOrFail($id);

        // Check if the user has permission to delete this reservation
        if ($user->isAgencyUser() && $reservation->experience->agency_id !== $user->agency_id) {
            return back()->with('error', 'No tienes permiso para eliminar esta reserva.');
        }

        // Soft delete the reservation
        $reservation->delete();

        return back()->with('success', 'Reserva eliminada correctamente.');
    }

    /**
     * Update the status of a reservation.
     */
    public function updateStatus(Request $request, string $id)
    {
        $user = Auth::user();
        $reservation = Reservation::with('experience')->findOrFail($id);

        // Check if the user has permission to update this reservation
        if ($user->isAgencyUser() && $reservation->experience->agency_id !== $user->agency_id) {
            return response()->json([
                'message' => 'No tienes permiso para actualizar esta reserva.'
            ], 403);
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled,completed',
        ]);

        // Track changes for notification
        $changes = [];
        if ($reservation->status != $validated['status']) {
            $changes['status'] = [
                'old' => $reservation->status,
                'new' => $validated['status']
            ];
        }

        // Update the status
        $reservation->update(['status' => $validated['status']]);

        // Send notification to agency users if there are changes
        if (!empty($changes) && $reservation->experience->agency_id) {
            // Get agency admin users
            $agencyAdmins = User::where('agency_id', $reservation->experience->agency_id)
                ->where('role', 'agency_admin')
                ->get();

            // Notify agency admins
            foreach ($agencyAdmins as $admin) {
                $admin->notify(new ReservationUpdated($reservation, $changes));
            }
        }

        return response()->json([
            'message' => 'Estado de la reserva actualizado correctamente.'
        ]);
    }
}
