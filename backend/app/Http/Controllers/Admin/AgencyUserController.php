<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\User;
use App\Notifications\NewAgencyUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class AgencyUserController extends Controller
{
    /**
     * Display a listing of the agency users.
     */
    public function index()
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes una agencia asignada.');
        }

        $users = User::where('agency_id', $agency->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Admin/AgencyUsers/Index', [
            'users' => $users,
            'agency' => $agency,
            'isAgencyAdmin' => $user->isAgencyAdmin(),
        ]);
    }

    /**
     * Show the form for creating a new agency user.
     */
    public function create()
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes una agencia asignada.');
        }

        return Inertia::render('Admin/AgencyUsers/Create', [
            'agency' => $agency,
        ]);
    }

    /**
     * Store a newly created agency user in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency || !$user->isAgencyAdmin()) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes permisos para crear usuarios de agencia.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', 'string', Rule::in(['agency_user', 'agency_admin'])],
        ]);

        $newUser = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
            'agency_id' => $agency->id,
        ]);

        // Load the agency relationship
        $newUser->load('agency');

        // Notify other agency admins about the new user
        $agencyAdmins = User::where('agency_id', $agency->id)
            ->where('role', 'agency_admin')
            ->where('id', '!=', $user->id) // Don't notify the creator
            ->get();

        foreach ($agencyAdmins as $admin) {
            $admin->notify(new NewAgencyUser($newUser));
        }

        return redirect()->route('admin.agency-users.index')
            ->with('success', 'Usuario de agencia creado correctamente.');
    }

    /**
     * Display the specified agency user.
     */
    public function show(User $agencyUser)
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency || $agencyUser->agency_id !== $agency->id) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes permisos para ver este usuario.');
        }

        return Inertia::render('Admin/AgencyUsers/Show', [
            'agencyUser' => $agencyUser,
            'agency' => $agency,
            'isAgencyAdmin' => $user->isAgencyAdmin(),
        ]);
    }

    /**
     * Show the form for editing the specified agency user.
     */
    public function edit(User $agencyUser)
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency || $agencyUser->agency_id !== $agency->id) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes permisos para editar este usuario.');
        }

        return Inertia::render('Admin/AgencyUsers/Edit', [
            'agencyUser' => $agencyUser,
            'agency' => $agency,
        ]);
    }

    /**
     * Update the specified agency user in storage.
     */
    public function update(Request $request, User $agencyUser)
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency || $agencyUser->agency_id !== $agency->id || !$user->isAgencyAdmin()) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes permisos para actualizar este usuario.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($agencyUser->id),
            ],
            'role' => ['required', 'string', Rule::in(['agency_user', 'agency_admin'])],
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'role' => $validated['role'],
        ];

        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        $agencyUser->update($updateData);

        return redirect()->route('admin.agency-users.index')
            ->with('success', 'Usuario de agencia actualizado correctamente.');
    }

    /**
     * Remove the specified agency user from storage.
     */
    public function destroy(User $agencyUser)
    {
        $user = Auth::user();
        $agency = $user->agency;

        if (!$agency || $agencyUser->agency_id !== $agency->id || !$user->isAgencyAdmin()) {
            return redirect()->route('admin.dashboard')->with('error', 'No tienes permisos para eliminar este usuario.');
        }

        // Prevent deleting yourself
        if ($agencyUser->id === $user->id) {
            return redirect()->route('admin.agency-users.index')
                ->with('error', 'No puedes eliminar tu propio usuario.');
        }

        $agencyUser->delete();

        return redirect()->route('admin.agency-users.index')
            ->with('success', 'Usuario de agencia eliminado correctamente.');
    }
}
