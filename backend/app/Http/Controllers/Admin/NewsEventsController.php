<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\News;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NewsEventsController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $news = News::orderBy('created_at', 'desc')->get();

        // Query for events
        $eventsQuery = Event::query();

        // If user is associated with an agency and is not a superadmin, only show events from that agency
        if ($user->agency_id && $user->role !== 'superadmin') {
            $eventsQuery->where('agency_id', $user->agency_id);
        }

        $events = $eventsQuery->orderBy('start_datetime', 'desc')->get();

        return Inertia::render('Admin/NewsEvents/Index', [
            'news' => $news,
            'events' => $events,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
        ]);
    }

    public function showEvent(Event $event)
    {
        $user = auth()->user();

        // If user is associated with an agency and is not a superadmin, check if they own this event
        if ($user->agency_id && $user->role !== 'superadmin' && $event->agency_id !== $user->agency_id) {
            abort(403, 'Unauthorized action. You can only view your own events.');
        }

        return Inertia::render('Admin/NewsEvents/ShowEvent', [
            'event' => $event,
            'isAgencyUser' => !!$user->agency_id,
            'isSuperAdmin' => $user->role === 'superadmin',
        ]);
    }

    public function showNews(News $news)
    {
        return Inertia::render('Admin/NewsEvents/ShowNews', [
            'news' => $news,
        ]);
    }
}
