<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Location;
use App\Models\StoryTour;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class StoryTourController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get the authenticated user
        $user = $request->user();

        // Build the query
        $query = StoryTour::with(['location', 'agency'])
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc');

        // If user is not a superadmin, only show their agency's story tours
        if ($user->role !== 'superadmin') {
            $query->where('agency_id', $user->agency_id);
        }

        $storyTours = $query->get();

        return Inertia::render('Admin/StoryTours/Index', [
            'storyTours' => $storyTours,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $locations = Location::orderBy('name')->get();

        // Get the authenticated user
        $user = $request->user();

        // Get agencies for superadmin, or just the user's agency for agency users
        $agencies = $user->role === 'superadmin'
            ? Agency::orderBy('name')->get()
            : Agency::where('id', $user->agency_id)->get();

        // Define modality options
        $modalityOptions = ['Juego', 'Testimonio', 'Paisaje', 'Información'];

        // Define type options
        $typeOptions = ['paisaje', 'patrimonio', 'cultura', 'gastronomia', 'naturaleza', 'easy', 'medium'];

        return Inertia::render('Admin/StoryTours/Create', [
            'locations' => $locations,
            'agencies' => $agencies,
            'modalityOptions' => $modalityOptions,
            'typeOptions' => $typeOptions,
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();

        $validationRules = [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|max:2048',
            'type' => 'required|string|max:255',
            'territory' => 'nullable|string|max:255',
            'modality' => 'nullable|string|max:255',
            'has_crafts' => 'boolean',
            'has_artists' => 'boolean',
            'year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'audio_file' => 'nullable|file|mimes:mp3,wav,ogg|max:10240',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi|max:51200',
            'ar_model_file' => 'nullable|file|mimes:glb,gltf|max:20480',
            'location_id' => 'nullable|exists:locations,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ];

        // Only superadmins can change the agency
        if ($user->role === 'superadmin') {
            $validationRules['agency_id'] = 'nullable|exists:agencies,id';
        } else {
            // For agency users, set the agency_id to their own agency
            $request->merge(['agency_id' => $user->agency_id]);
        }

        $validated = $request->validate($validationRules);

        // Handle image upload
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('story_tours', 'public');
            $validated['image'] = $path;
        }

        // Handle audio file upload
        if ($request->hasFile('audio_file')) {
            $path = $request->file('audio_file')->store('story_tours/audio', 'public');
            $validated['audio_file'] = $path;
        }

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            $path = $request->file('video_file')->store('story_tours/video', 'public');
            $validated['video_file'] = $path;
        }

        // Handle AR model file upload
        if ($request->hasFile('ar_model_file')) {
            $path = $request->file('ar_model_file')->store('story_tours/ar_models', 'public');
            $validated['ar_model_file'] = $path;
        }

        StoryTour::create($validated);

        return redirect()->route('admin.story-tours.index')
            ->with('success', 'StoryTour creado correctamente.');
    }

    /**
     * Display the specified resource.
     */
    public function show(StoryTour $storyTour)
    {
        $storyTour->load('location');

        return Inertia::render('Admin/StoryTours/Show', [
            'storyTour' => $storyTour,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, StoryTour $storyTour)
    {
        // Get the authenticated user
        $user = $request->user();

        // Check if user has access to this story tour
        if ($user->role !== 'superadmin' && $storyTour->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para editar este StoryTour.');
        }

        $storyTour->load(['location', 'agency']);
        $locations = Location::orderBy('name')->get();

        // Get agencies for superadmin, or just the user's agency for agency users
        $agencies = $user->role === 'superadmin'
            ? Agency::orderBy('name')->get()
            : Agency::where('id', $user->agency_id)->get();

        // Define modality options
        $modalityOptions = ['Juego', 'Testimonio', 'Paisaje', 'Información'];

        // Define type options
        $typeOptions = ['paisaje', 'patrimonio', 'cultura', 'gastronomia', 'naturaleza', 'easy', 'medium'];

        return Inertia::render('Admin/StoryTours/Edit', [
            'storyTour' => $storyTour,
            'locations' => $locations,
            'agencies' => $agencies,
            'modalityOptions' => $modalityOptions,
            'typeOptions' => $typeOptions,
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StoryTour $storyTour)
    {
        $user = $request->user();

        // Check if user has access to this story tour
        if ($user->role !== 'superadmin' && $storyTour->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para editar este StoryTour.');
        }

        $validationRules = [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|max:2048',
            'type' => 'required|string|max:255',
            'territory' => 'nullable|string|max:255',
            'modality' => 'nullable|string|max:255',
            'has_crafts' => 'boolean',
            'has_artists' => 'boolean',
            'year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'audio_file' => 'nullable|file|mimes:mp3,wav,ogg|max:10240',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi|max:51200',
            'ar_model_file' => 'nullable|file|mimes:glb,gltf|max:20480',
            'location_id' => 'nullable|exists:locations,id',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ];

        // Only superadmins can change the agency
        if ($user->role === 'superadmin') {
            $validationRules['agency_id'] = 'nullable|exists:agencies,id';
        } else {
            // For agency users, ensure agency_id remains their own
            $request->merge(['agency_id' => $user->agency_id]);
        }

        $validated = $request->validate($validationRules);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($storyTour->image) {
                Storage::disk('public')->delete($storyTour->image);
            }

            $path = $request->file('image')->store('story_tours', 'public');
            $validated['image'] = $path;
        }

        // Handle audio file upload
        if ($request->hasFile('audio_file')) {
            // Delete old audio file if it exists
            if ($storyTour->audio_file) {
                Storage::disk('public')->delete($storyTour->audio_file);
            }

            $path = $request->file('audio_file')->store('story_tours/audio', 'public');
            $validated['audio_file'] = $path;
        }

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            // Delete old video file if it exists
            if ($storyTour->video_file) {
                Storage::disk('public')->delete($storyTour->video_file);
            }

            $path = $request->file('video_file')->store('story_tours/video', 'public');
            $validated['video_file'] = $path;
        }

        // Handle AR model file upload
        if ($request->hasFile('ar_model_file')) {
            // Delete old AR model file if it exists
            if ($storyTour->ar_model_file) {
                Storage::disk('public')->delete($storyTour->ar_model_file);
            }

            $path = $request->file('ar_model_file')->store('story_tours/ar_models', 'public');
            $validated['ar_model_file'] = $path;
        }

        $storyTour->update($validated);

        return redirect()->route('admin.story-tours.index')
            ->with('success', 'StoryTour actualizado correctamente.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, StoryTour $storyTour)
    {
        // Get the authenticated user
        $user = $request->user();

        // Check if user has access to this story tour
        if ($user->role !== 'superadmin' && $storyTour->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para eliminar este StoryTour.');
        }
        // Delete associated files
        if ($storyTour->image) {
            Storage::disk('public')->delete($storyTour->image);
        }

        if ($storyTour->audio_file) {
            Storage::disk('public')->delete($storyTour->audio_file);
        }

        if ($storyTour->video_file) {
            Storage::disk('public')->delete($storyTour->video_file);
        }

        if ($storyTour->ar_model_file) {
            Storage::disk('public')->delete($storyTour->ar_model_file);
        }

        $storyTour->delete();

        return redirect()->route('admin.story-tours.index')
            ->with('success', 'StoryTour eliminado correctamente.');
    }
}
