<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\Group;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class GroupController extends Controller
{
    /**
     * Display a listing of the groups.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Group::with(['agency', 'creator'])
            ->withCount('users') // Count the number of users in each group
            ->orderBy('created_at', 'desc');

        // If agency user, only show their agency's groups
        if ($user->isAgencyUser()) {
            $query->where('agency_id', $user->agency_id);
        }

        // Filter by type if provided
        if ($request->has('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }

        // Filter by agency if provided
        if ($request->has('agency_id') && $request->agency_id) {
            $query->where('agency_id', $request->agency_id);
        }

        $groups = $query->paginate(10);

        // Get all agencies for the filter dropdown
        $agencies = Agency::where('is_active', true)->orderBy('name')->get(['id', 'name']);

        // Get group types for the filter dropdown
        $groupTypes = Group::select('type')->distinct()->pluck('type')->toArray();

        return Inertia::render('Admin/Groups/Index', [
            'groups' => $groups,
            'agencies' => $agencies,
            'groupTypes' => $groupTypes,
            'filters' => [
                'type' => $request->input('type', 'all'),
                'agency_id' => $request->input('agency_id'),
            ],
            'isAgencyUser' => $user->isAgencyUser(),
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Show the form for creating a new group.
     */
    public function create()
    {
        $user = Auth::user();

        // Get all agencies for the dropdown
        $agenciesQuery = Agency::where('is_active', true)->orderBy('name');

        // If agency user, only show their agency
        if ($user->isAgencyUser()) {
            $agenciesQuery->where('id', $user->agency_id);
        }

        $agencies = $agenciesQuery->get(['id', 'name']);

        // Get all users for the dropdown
        $usersQuery = User::orderBy('name');

        // If agency user, only show users from their agency
        if ($user->isAgencyUser()) {
            $usersQuery->where('agency_id', $user->agency_id);
        }

        $users = $usersQuery->get(['id', 'name', 'email']);

        // Define group types
        $groupTypes = [
            'couple' => 'Pareja',
            'family' => 'Familia',
            'pets' => 'Con mascotas',
            'mixed' => 'Mixto',
            'other' => 'Otro'
        ];

        return Inertia::render('Admin/Groups/Create', [
            'agencies' => $agencies,
            'users' => $users,
            'groupTypes' => $groupTypes,
            'isAgencyUser' => $user->isAgencyUser(),
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Store a newly created group in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string',
            'number_of_people' => 'required|integer|min:1',
            'group_members' => 'nullable|array',
            'group_members.*' => 'nullable|string|max:255',
            'agency_id' => 'nullable|exists:agencies,id',
            'users' => 'nullable|array',
            'users.*' => 'exists:users,id',
            'guides' => 'nullable|array',
            'guides.*' => 'exists:users,id',
        ]);

        // If agency user, force their agency_id
        if ($user->isAgencyUser()) {
            $validated['agency_id'] = $user->agency_id;
        }

        // Start a transaction
        DB::beginTransaction();

        try {
            // Create the group
            $group = Group::create([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'type' => $validated['type'],
                'number_of_people' => $validated['number_of_people'],
                'group_members' => $validated['group_members'] ?? null,
                'agency_id' => $validated['agency_id'],
                'created_by' => $user->id,
                'is_active' => true,
            ]);

            // Attach users to the group (if any)
            if (!empty($validated['users'])) {
                foreach ($validated['users'] as $userId) {
                    $isGuide = isset($validated['guides']) && in_array($userId, $validated['guides']);
                    $group->users()->attach($userId, ['is_guide' => $isGuide]);
                }
            }

            DB::commit();

            return redirect()->route('admin.groups.show', $group)
                ->with('success', 'Grupo creado correctamente.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with('error', 'Error al crear el grupo: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified group.
     */
    public function show(Group $group)
    {
        $user = Auth::user();

        // Check if the user has access to this group
        if ($user->isAgencyUser() && $group->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para ver este grupo.');
        }

        $group->load(['agency', 'creator', 'users', 'reservations.experience']);

        return Inertia::render('Admin/Groups/Show', [
            'group' => $group,
            'isAgencyUser' => $user->isAgencyUser(),
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Show the form for editing the specified group.
     */
    public function edit(Group $group)
    {
        $user = Auth::user();

        // Check if the user has access to this group
        if ($user->isAgencyUser() && $group->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para editar este grupo.');
        }

        // Get all agencies for the dropdown
        $agenciesQuery = Agency::where('is_active', true)->orderBy('name');

        // If agency user, only show their agency
        if ($user->isAgencyUser()) {
            $agenciesQuery->where('id', $user->agency_id);
        }

        $agencies = $agenciesQuery->get(['id', 'name']);

        // Get all users for the dropdown
        $usersQuery = User::orderBy('name');

        // If agency user, only show users from their agency
        if ($user->isAgencyUser()) {
            $usersQuery->where('agency_id', $user->agency_id);
        }

        $users = $usersQuery->get(['id', 'name', 'email']);

        // Define group types
        $groupTypes = [
            'couple' => 'Pareja',
            'family' => 'Familia',
            'pets' => 'Con mascotas',
            'mixed' => 'Mixto',
            'other' => 'Otro'
        ];

        // Get the IDs of users in the group
        $groupUserIds = $group->users->pluck('id')->toArray();

        // Get the IDs of guides in the group
        $groupGuideIds = $group->guides->pluck('id')->toArray();

        return Inertia::render('Admin/Groups/Edit', [
            'group' => $group,
            'agencies' => $agencies,
            'users' => $users,
            'groupTypes' => $groupTypes,
            'groupUserIds' => $groupUserIds,
            'groupGuideIds' => $groupGuideIds,
            'isAgencyUser' => $user->isAgencyUser(),
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Update the specified group in storage.
     */
    public function update(Request $request, Group $group)
    {
        $user = Auth::user();

        // Check if the user has access to this group
        if ($user->isAgencyUser() && $group->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para editar este grupo.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string',
            'number_of_people' => 'required|integer|min:1',
            'group_members' => 'nullable|array',
            'group_members.*' => 'nullable|string|max:255',
            'agency_id' => 'nullable|exists:agencies,id',
            'users' => 'nullable|array',
            'users.*' => 'exists:users,id',
            'guides' => 'nullable|array',
            'guides.*' => 'exists:users,id',
            'is_active' => 'boolean',
        ]);

        // If agency user, force their agency_id
        if ($user->isAgencyUser()) {
            $validated['agency_id'] = $user->agency_id;
        }

        // Start a transaction
        DB::beginTransaction();

        try {
            // Update the group
            $group->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'type' => $validated['type'],
                'number_of_people' => $validated['number_of_people'],
                'group_members' => $validated['group_members'] ?? null,
                'agency_id' => $validated['agency_id'],
                'is_active' => $request->has('is_active') ? $validated['is_active'] : $group->is_active,
            ]);

            // Sync users to the group (if any)
            $syncData = [];
            if (!empty($validated['users'])) {
                foreach ($validated['users'] as $userId) {
                    $isGuide = isset($validated['guides']) && in_array($userId, $validated['guides']);
                    $syncData[$userId] = ['is_guide' => $isGuide];
                }
            }

            $group->users()->sync($syncData);

            DB::commit();

            return redirect()->route('admin.groups.show', $group)
                ->with('success', 'Grupo actualizado correctamente.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with('error', 'Error al actualizar el grupo: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified group from storage.
     */
    public function destroy(Group $group)
    {
        $user = Auth::user();

        // Check if the user has access to this group
        if ($user->isAgencyUser() && $group->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para eliminar este grupo.');
        }

        // Check if the group has reservations
        if ($group->reservations()->exists()) {
            return back()->with('error', 'No se puede eliminar el grupo porque tiene reservas asociadas.');
        }

        try {
            // Delete the group
            $group->delete();

            return redirect()->route('admin.groups.index')
                ->with('success', 'Grupo eliminado correctamente.');
        } catch (\Exception $e) {
            return back()->with('error', 'Error al eliminar el grupo: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for managing users in the group.
     */
    public function manageUsers(Group $group)
    {
        $user = Auth::user();

        // Check if the user has access to this group
        if ($user->isAgencyUser() && $group->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para gestionar los usuarios de este grupo.');
        }

        $group->load('users');

        // Get all users for the dropdown
        $usersQuery = User::orderBy('name');

        // If agency user, only show users from their agency
        if ($user->isAgencyUser()) {
            $usersQuery->where('agency_id', $user->agency_id);
        }

        $users = $usersQuery->get(['id', 'name', 'email']);

        // Get the IDs of users in the group
        $groupUserIds = $group->users->pluck('id')->toArray();

        // Get the IDs of guides in the group
        $groupGuideIds = $group->guides->pluck('id')->toArray();

        return Inertia::render('Admin/Groups/ManageUsers', [
            'group' => $group,
            'users' => $users,
            'groupUserIds' => $groupUserIds,
            'groupGuideIds' => $groupGuideIds,
            'isAgencyUser' => $user->isAgencyUser(),
            'isSuperAdmin' => $user->isSuperAdmin(),
            'userAgencyId' => $user->agency_id,
        ]);
    }

    /**
     * Update the users in the group.
     */
    public function updateUsers(Request $request, Group $group)
    {
        $user = Auth::user();

        // Check if the user has access to this group
        if ($user->isAgencyUser() && $group->agency_id !== $user->agency_id) {
            abort(403, 'No tienes permiso para gestionar los usuarios de este grupo.');
        }

        $validated = $request->validate([
            'number_of_people' => 'required|integer|min:1',
            'group_members' => 'nullable|array',
            'group_members.*' => 'nullable|string|max:255',
            'users' => 'nullable|array',
            'users.*' => 'exists:users,id',
            'guides' => 'nullable|array',
            'guides.*' => 'exists:users,id',
        ]);

        // Start a transaction
        DB::beginTransaction();

        try {
            // Update group data
            $group->update([
                'number_of_people' => $validated['number_of_people'],
                'group_members' => $validated['group_members'] ?? null,
            ]);

            // Sync users to the group (if any)
            $syncData = [];
            if (!empty($validated['users'])) {
                foreach ($validated['users'] as $userId) {
                    $isGuide = isset($validated['guides']) && in_array($userId, $validated['guides']);
                    $syncData[$userId] = ['is_guide' => $isGuide];
                }
            }

            $group->users()->sync($syncData);

            DB::commit();

            return redirect()->route('admin.groups.show', $group)
                ->with('success', 'Usuarios del grupo actualizados correctamente.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with('error', 'Error al actualizar los usuarios del grupo: ' . $e->getMessage());
        }
    }
}
