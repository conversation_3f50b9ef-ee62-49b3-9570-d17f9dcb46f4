<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UserRoleController extends Controller
{
    /**
     * Check if the authenticated user can manage roles.
     */
    private function checkRoleManagementPermissions()
    {
        $user = Auth::user();
        
        // Only superadmins and agencies can manage roles
        if (!$user || (!$user->hasRole('superadmin') && !$user->hasRole('agency'))) {
            abort(403, 'No tienes permisos para gestionar roles de usuario');
        }
        
        return $user;
    }

    /**
     * Get users that can be managed by the current user.
     */
    public function getManageableUsers(): JsonResponse
    {
        try {
            $currentUser = $this->checkRoleManagementPermissions();
            
            $query = User::query();
            
            // Superadmins can see all users
            if ($currentUser->hasRole('superadmin')) {
                $query->with(['agency']);
            } else {
                // Agencies can only see users in their agency
                $query->where('agency_id', $currentUser->agency_id)
                      ->with(['agency']);
            }
            
            $users = $query->get()->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'roles' => $user->getRoles(),
                    'agency_id' => $user->agency_id,
                    'agency_name' => $user->agency?->name,
                    'created_at' => $user->created_at,
                    'is_guide' => $user->isGuide(),
                ];
            });

            return response()->json(['data' => $users]);
        } catch (\Exception $e) {
            Log::error('Error getting manageable users', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Error al cargar los usuarios'
            ], 500);
        }
    }

    /**
     * Assign a role to a user.
     */
    public function assignRole(Request $request, $userId): JsonResponse
    {
        try {
            $currentUser = $this->checkRoleManagementPermissions();
            
            $validated = $request->validate([
                'role' => 'required|string|in:user,guide,agency,superadmin'
            ]);

            $targetUser = User::findOrFail($userId);

            // Check permissions
            if (!$currentUser->hasRole('superadmin')) {
                // Agencies can only manage users in their agency
                if ($targetUser->agency_id !== $currentUser->agency_id) {
                    return response()->json([
                        'message' => 'No tienes permisos para gestionar este usuario'
                    ], 403);
                }

                // Agencies cannot assign superadmin or agency roles
                if (in_array($validated['role'], ['superadmin', 'agency'])) {
                    return response()->json([
                        'message' => 'No tienes permisos para asignar este rol'
                    ], 403);
                }
            }

            // Assign the role
            $targetUser->addRole($validated['role']);

            Log::info('Role assigned to user', [
                'assigned_by' => $currentUser->id,
                'target_user' => $targetUser->id,
                'role' => $validated['role']
            ]);

            return response()->json([
                'message' => 'Rol asignado correctamente',
                'data' => [
                    'id' => $targetUser->id,
                    'name' => $targetUser->name,
                    'email' => $targetUser->email,
                    'roles' => $targetUser->getRoles(),
                    'is_guide' => $targetUser->isGuide(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error assigning role', [
                'user_id' => Auth::id(),
                'target_user' => $userId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al asignar el rol'
            ], 500);
        }
    }

    /**
     * Remove a role from a user.
     */
    public function removeRole(Request $request, $userId): JsonResponse
    {
        try {
            $currentUser = $this->checkRoleManagementPermissions();
            
            $validated = $request->validate([
                'role' => 'required|string|in:user,guide,agency,superadmin'
            ]);

            $targetUser = User::findOrFail($userId);

            // Check permissions
            if (!$currentUser->hasRole('superadmin')) {
                // Agencies can only manage users in their agency
                if ($targetUser->agency_id !== $currentUser->agency_id) {
                    return response()->json([
                        'message' => 'No tienes permisos para gestionar este usuario'
                    ], 403);
                }

                // Agencies cannot remove superadmin or agency roles
                if (in_array($validated['role'], ['superadmin', 'agency'])) {
                    return response()->json([
                        'message' => 'No tienes permisos para quitar este rol'
                    ], 403);
                }
            }

            // Prevent removing the last role
            $currentRoles = $targetUser->getRoles();
            if (count($currentRoles) <= 1) {
                return response()->json([
                    'message' => 'No se puede quitar el último rol del usuario'
                ], 400);
            }

            // Remove the role
            $targetUser->removeRole($validated['role']);

            Log::info('Role removed from user', [
                'removed_by' => $currentUser->id,
                'target_user' => $targetUser->id,
                'role' => $validated['role']
            ]);

            return response()->json([
                'message' => 'Rol eliminado correctamente',
                'data' => [
                    'id' => $targetUser->id,
                    'name' => $targetUser->name,
                    'email' => $targetUser->email,
                    'roles' => $targetUser->getRoles(),
                    'is_guide' => $targetUser->isGuide(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing role', [
                'user_id' => Auth::id(),
                'target_user' => $userId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al quitar el rol'
            ], 500);
        }
    }

    /**
     * Toggle guide role for a user.
     */
    public function toggleGuideRole(Request $request, $userId): JsonResponse
    {
        try {
            $currentUser = $this->checkRoleManagementPermissions();
            $targetUser = User::findOrFail($userId);

            // Check permissions
            if (!$currentUser->hasRole('superadmin')) {
                // Agencies can only manage users in their agency
                if ($targetUser->agency_id !== $currentUser->agency_id) {
                    return response()->json([
                        'message' => 'No tienes permisos para gestionar este usuario'
                    ], 403);
                }
            }

            $isCurrentlyGuide = $targetUser->hasRole('guide');
            
            if ($isCurrentlyGuide) {
                $targetUser->removeRole('guide');
                $action = 'removed';
                $message = 'Permisos de guía eliminados correctamente';
            } else {
                $targetUser->addRole('guide');
                $action = 'added';
                $message = 'Permisos de guía asignados correctamente';
            }

            Log::info('Guide role toggled', [
                'toggled_by' => $currentUser->id,
                'target_user' => $targetUser->id,
                'action' => $action
            ]);

            return response()->json([
                'message' => $message,
                'data' => [
                    'id' => $targetUser->id,
                    'name' => $targetUser->name,
                    'email' => $targetUser->email,
                    'roles' => $targetUser->getRoles(),
                    'is_guide' => $targetUser->isGuide(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling guide role', [
                'user_id' => Auth::id(),
                'target_user' => $userId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cambiar los permisos de guía'
            ], 500);
        }
    }

    /**
     * Get available roles that can be assigned.
     */
    public function getAvailableRoles(): JsonResponse
    {
        try {
            $currentUser = $this->checkRoleManagementPermissions();
            
            $roles = [
                ['value' => 'user', 'label' => 'Usuario'],
                ['value' => 'guide', 'label' => 'Guía'],
            ];

            // Only superadmins can assign agency and superadmin roles
            if ($currentUser->hasRole('superadmin')) {
                $roles[] = ['value' => 'agency', 'label' => 'Agencia'];
                $roles[] = ['value' => 'superadmin', 'label' => 'Superadministrador'];
            }

            return response()->json(['data' => $roles]);
        } catch (\Exception $e) {
            Log::error('Error getting available roles', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Error al cargar los roles disponibles'
            ], 500);
        }
    }
}
