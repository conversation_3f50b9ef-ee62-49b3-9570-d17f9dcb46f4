<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NewsController extends Controller
{
    /**
     * Display a listing of the news.
     */
    public function index(): JsonResponse
    {
        $news = News::orderBy('datetime', 'desc')->get();

        return response()->json([
            'status' => 'success',
            'data' => $news,
        ]);
    }

    /**
     * Store a newly created news item in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'summary' => 'required|string',
            'datetime' => 'required|date',
            'location' => 'required|string|max:255',
            'image' => 'required|string|max:255',
        ]);

        $news = News::create($validated);

        return response()->json([
            'status' => 'success',
            'message' => 'News created successfully',
            'data' => $news,
        ], 201);
    }

    /**
     * Display the specified news item.
     */
    public function show(News $news): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => $news,
        ]);
    }

    /**
     * Update the specified news item in storage.
     */
    public function update(Request $request, News $news): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'sometimes|required|string|max:255',
            'summary' => 'sometimes|required|string',
            'datetime' => 'sometimes|required|date',
            'location' => 'sometimes|required|string|max:255',
            'image' => 'sometimes|required|string|max:255',
        ]);

        $news->update($validated);

        return response()->json([
            'status' => 'success',
            'message' => 'News updated successfully',
            'data' => $news,
        ]);
    }

    /**
     * Remove the specified news item from storage.
     */
    public function destroy(News $news): JsonResponse
    {
        $news->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'News deleted successfully',
        ]);
    }
}
