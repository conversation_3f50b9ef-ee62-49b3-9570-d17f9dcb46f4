<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckMobileAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Allow unauthenticated access to public mobile routes
        if (!$user) {
            return $next($request);
        }

        // Check if user can access mobile frontend
        if (!$user->canAccessMobile()) {
            return response()->json([
                'message' => 'No tiene permisos para acceder a la aplicación móvil.',
                'access_type' => 'cms_only'
            ], 403);
        }

        return $next($request);
    }
}
