<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckCMSAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Allow unauthenticated access to login routes
        if (!$user && $request->routeIs(['login', 'password.*'])) {
            return $next($request);
        }

        // Require authentication for CMS access
        if (!$user) {
            return redirect()->route('login')->with('error', 'Debe iniciar sesión para acceder al panel de administración.');
        }

        // Check if user can access CMS
        if (!$user->canAccessCMS()) {
            Auth::logout();
            return redirect()->route('login')->with('error', 'No tiene permisos para acceder al panel de administración.');
        }

        return $next($request);
    }
}
