<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        $ziggyData = (new Ziggy)->toArray();
        $ziggyData['location'] = $request->url();

        return array_merge(parent::share($request), [
            'name' => config('app.name'),
            'quote' => ['message' => trim($message), 'author' => trim($author)],
            'auth' => [
                'user' => $request->user(),
            ],
            'notifications' => $this->getNotifications($request),
            'ziggy' => $ziggyData,
            'sidebarOpen' => ! $request->hasCookie('sidebar_state') || $request->cookie('sidebar_state') === 'true',
        ]);
    }

    /**
     * Get notifications safely, checking if the table exists first.
     *
     * @param Request $request
     * @return array
     */
    protected function getNotifications(Request $request): array
    {
        try {
            // Check if the notifications table exists
            if (Schema::hasTable('notifications') && $request->user()) {
                $notifications = $request->user()->notifications()->latest()->take(5)->get()->toArray();
                \Log::debug('Notifications for user: ' . $request->user()->id, $notifications);
                return $notifications;
            }
        } catch (\Exception $e) {
            // If there's any error, return an empty array
            report($e);
            \Log::error('Error getting notifications: ' . $e->getMessage());
        }

        return [];
    }
}
