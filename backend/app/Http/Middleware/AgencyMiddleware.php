<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AgencyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Allow superadmins to access agency routes
        if (!Auth::check() || (!$user->agency_id && $user->role !== 'superadmin')) {
            abort(403, 'Unauthorized action. Only agency users can access this area.');
        }

        return $next($request);
    }
}
