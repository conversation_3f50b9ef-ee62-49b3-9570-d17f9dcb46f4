<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReservationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // We'll handle authorization in the controller
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'experience_id' => 'required|exists:experiences,id',
            'reservation_date' => 'required|date|after_or_equal:today',
            'num_people' => 'required|integer|min:1|max:50',
            'special_requests' => 'nullable|string|max:1000',
            'group_id' => 'nullable|exists:groups,id',
            'force_update' => 'nullable|boolean',
        ];

        // Add custom validation to prevent duplicate reservations
        $userId = auth()->id();

        // Check if this is an update request (has reservation_id)
        if (!$this->has('reservation_id')) {
            $rules['experience_id'] = [
                'required',
                'exists:experiences,id',
                function ($attribute, $value, $fail) use ($userId) {
                    // Only check for duplicates if not forcing an update
                    if (!$this->input('force_update')) {
                        // Check for same day reservation
                        $existingSameDay = \App\Models\Reservation::where('user_id', $userId)
                            ->where('experience_id', $value)
                            ->where('reservation_date', $this->input('reservation_date'))
                            ->exists();

                        if ($existingSameDay) {
                            $fail('Ya tienes una reserva para esta experiencia en esta fecha.');
                        }

                        // Check for any reservation for this experience
                        $existingAnyDay = \App\Models\Reservation::where('user_id', $userId)
                            ->where('experience_id', $value)
                            ->exists();

                        if ($existingAnyDay) {
                            // We'll handle this in the controller by returning a special response
                            // that the frontend can use to ask the user if they want to update
                            // their existing reservation
                        }
                    }
                },
            ];
        }

        // Only require reservation_time for certain experience types
        if ($this->has('experience_id')) {
            $experience = \App\Models\Experience::find($this->experience_id);
            if ($experience && in_array($experience->type, ['restaurant', 'tour'])) {
                $rules['reservation_time'] = 'required|date_format:H:i';
            } else {
                $rules['reservation_time'] = 'nullable|date_format:H:i';
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'experience_id.required' => 'Debe seleccionar una experiencia.',
            'experience_id.exists' => 'La experiencia seleccionada no existe.',
            'reservation_date.required' => 'La fecha de reserva es obligatoria.',
            'reservation_date.date' => 'La fecha de reserva debe ser una fecha válida.',
            'reservation_date.after_or_equal' => 'La fecha de reserva debe ser hoy o una fecha futura.',
            'reservation_time.required' => 'La hora de reserva es obligatoria.',
            'reservation_time.date_format' => 'La hora de reserva debe tener el formato HH:MM.',
            'num_people.required' => 'El número de personas es obligatorio.',
            'num_people.integer' => 'El número de personas debe ser un número entero.',
            'num_people.min' => 'El número de personas debe ser al menos 1.',
            'num_people.max' => 'El número de personas no puede ser mayor a 50.',
            'special_requests.max' => 'Las solicitudes especiales no pueden exceder los 1000 caracteres.',
            'group_id.exists' => 'El grupo seleccionado no existe.',
        ];
    }
}
