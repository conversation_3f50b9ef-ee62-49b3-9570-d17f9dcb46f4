<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StoryTourResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'image' => $this->image ? asset('storage/' . $this->image) : null,
            'type' => $this->type,
            'territory' => $this->territory,
            'modality' => $this->modality,
            'has_crafts' => $this->has_crafts,
            'has_artists' => $this->has_artists,
            'year' => $this->year,
            'audio_file' => $this->audio_file ? asset('storage/' . $this->audio_file) : null,
            'video_file' => $this->video_file ? asset('storage/' . $this->video_file) : null,
            'ar_model_file' => $this->ar_model_file ? asset('storage/' . $this->ar_model_file) : null,
            'location' => $this->whenLoaded('location', function () {
                return [
                    'id' => $this->location->id,
                    'name' => $this->location->name,
                    'latitude' => $this->location->latitude,
                    'longitude' => $this->location->longitude,
                ];
            }),
            'agency' => $this->whenLoaded('agency', function () {
                return [
                    'id' => $this->agency->id,
                    'name' => $this->agency->name,
                ];
            }),
            'agency_id' => $this->agency_id,
            'is_featured' => $this->is_featured,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
