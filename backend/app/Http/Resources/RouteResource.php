<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RouteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'short_description' => $this->short_description,
            'image' => $this->image ? asset('storage/' . $this->image) : null,
            'duration' => $this->duration,
            'distance' => $this->distance,
            'difficulty' => $this->difficulty,
            'agency_id' => $this->agency_id,
            'agency' => $this->whenLoaded('agency', function () {
                return [
                    'id' => $this->agency->id,
                    'name' => $this->agency->name,
                    'logo' => $this->agency->logo ? asset('storage/' . $this->agency->logo) : null,
                ];
            }),
            'points' => RoutePointResource::collection($this->whenLoaded('points')),
            'is_featured' => $this->is_featured,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
