<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExperienceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'short_description' => $this->short_description,
            'location_id' => $this->location_id,
            'location' => $this->whenLoaded('location', function () {
                return [
                    'id' => $this->location->id,
                    'name' => $this->location->name,
                ];
            }),
            'agency_id' => $this->agency_id,
            'agency' => $this->whenLoaded('agency', function () {
                return [
                    'id' => $this->agency->id,
                    'name' => $this->agency->name,
                ];
            }),
            'type' => $this->type,
            'duration' => $this->duration,
            'distance' => $this->distance,
            'difficulty' => $this->difficulty,
            'price' => $this->price,
            'image' => $this->image ? asset('storage/' . $this->image) : null,
            'start_date' => $this->start_date ? $this->start_date->format('Y-m-d') : null,
            'end_date' => $this->end_date ? $this->end_date->format('Y-m-d') : null,
            'is_featured' => $this->is_featured,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),

            // Restaurant-specific fields
            'address' => $this->when($this->type === 'restaurant', $this->address),
            'phone' => $this->when($this->type === 'restaurant', $this->phone),
            'email' => $this->when($this->type === 'restaurant', $this->email),
            'website' => $this->when($this->type === 'restaurant', $this->website),
            'cuisine_type' => $this->when($this->type === 'restaurant', $this->cuisine_type),
            'opening_hours' => $this->when($this->type === 'restaurant', $this->opening_hours),
            'menu_url' => $this->when($this->type === 'restaurant', $this->menu_url),
        ];
    }
}
