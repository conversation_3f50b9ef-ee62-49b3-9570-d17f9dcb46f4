<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'agency_id' => $this->agency_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'groups' => $this->whenLoaded('groups', function () {
                // Check if the groups relationship exists and is loaded
                if (method_exists($this, 'groups') && $this->relationLoaded('groups')) {
                    return $this->groups->map(function ($group) {
                        return [
                            'id' => $group->id,
                            'name' => $group->name,
                            'type' => $group->type,
                            'is_guide' => $group->pivot->is_guide,
                        ];
                    });
                }
                return [];
            }),
        ];
    }
}
