<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'user' => $this->user ? [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
            ] : null,
            'is_group_reservation' => $this->is_group_reservation,
            'experience' => [
                'id' => $this->experience->id,
                'title' => $this->experience->title,
                'type' => $this->experience->type,
                'image' => $this->experience->image ? asset('storage/' . $this->experience->image) : null,
            ],
            'reservation_date' => $this->reservation_date->format('Y-m-d'),
            'reservation_time' => $this->reservation_time ? $this->reservation_time->format('H:i') : null,
            'num_people' => $this->num_people,
            'special_requests' => $this->special_requests,
            'status' => $this->status,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];

        // Add group information if this is a group reservation
        if ($this->group) {
            $data['group'] = [
                'id' => $this->group->id,
                'name' => $this->group->name,
                'type' => $this->group->type,
                'description' => $this->group->description,
            ];
        }

        // Add booking code information if available
        if ($this->bookingCode) {
            $data['booking_code'] = [
                'id' => $this->bookingCode->id,
                'code' => $this->bookingCode->code,
                'expires_at' => $this->bookingCode->expires_at ? $this->bookingCode->expires_at->format('Y-m-d H:i:s') : null,
                'is_active' => $this->bookingCode->is_active,
            ];
        }

        return $data;
    }
}
