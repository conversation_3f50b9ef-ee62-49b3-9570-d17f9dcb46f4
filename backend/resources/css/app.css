@import 'tailwindcss';

@import "tw-animate-css";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';

@custom-variant dark (&:is(.dark *));

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@theme inline {
  --font-sans:
    Poppins, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer utilities {
  body,
  html {
    --font-sans:
      'Poppins', ui-sans-serif, system-ui, sans-serif,
      'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
  }
}

:root {
  /* Cork Experience color palette */
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 3.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 3.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 3.9%);
  
  /* Cork Experience primary colors */
  --primary: hsl(20 70% 55%); /* #DC8960 - Cork orange */
  --primary-foreground: hsl(0 0% 98%);
  
  /* Cork Experience secondary colors */
  --secondary: hsl(30 40% 30%); /* #9A522E - Cork brown */
  --secondary-foreground: hsl(0 0% 98%);
  
  --muted: hsl(20 30% 96%);
  --muted-foreground: hsl(20 30% 45%);
  
  --accent: hsl(20 70% 90%); /* Light cork color */
  --accent-foreground: hsl(20 70% 30%);
  
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  
  --border: hsl(20 20% 90%);
  --input: hsl(20 20% 90%);
  --ring: hsl(20 70% 55%);
  
  /* Chart colors */
  --chart-1: hsl(20 70% 55%); /* Cork orange */
  --chart-2: hsl(30 40% 30%); /* Cork brown */
  --chart-3: hsl(20 30% 45%); /* Medium cork */
  --chart-4: hsl(43 74% 66%); /* Light cork */
  --chart-5: hsl(27 87% 67%); /* Warm orange */
  
  --radius: 0.5rem;
  
  /* Sidebar colors */
  --sidebar-background: hsl(20 30% 96%); /* Light cork texture */
  --sidebar-foreground: hsl(30 40% 30%); /* Cork brown */
  --sidebar-primary: hsl(20 70% 55%); /* Cork orange */
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(20 30% 90%);
  --sidebar-accent-foreground: hsl(30 40% 30%);
  --sidebar-border: hsl(20 20% 90%);
  --sidebar-ring: hsl(20 70% 55%);
}

.dark {
  --background: hsl(30 15% 15%); /* Dark cork */
  --foreground: hsl(20 30% 90%);
  --card: hsl(30 15% 15%);
  --card-foreground: hsl(20 30% 90%);
  --popover: hsl(30 15% 15%);
  --popover-foreground: hsl(20 30% 90%);
  
  --primary: hsl(20 70% 55%); /* Cork orange */
  --primary-foreground: hsl(0 0% 98%);
  
  --secondary: hsl(30 40% 30%); /* Cork brown */
  --secondary-foreground: hsl(0 0% 98%);
  
  --muted: hsl(30 15% 25%);
  --muted-foreground: hsl(20 30% 70%);
  
  --accent: hsl(30 20% 25%);
  --accent-foreground: hsl(20 30% 90%);
  
  --destructive: hsl(0 84% 60%);
  --destructive-foreground: hsl(0 0% 98%);
  
  --border: hsl(30 15% 25%);
  --input: hsl(30 15% 25%);
  --ring: hsl(20 70% 55%);
  
  /* Chart colors */
  --chart-1: hsl(20 70% 55%); /* Cork orange */
  --chart-2: hsl(30 40% 30%); /* Cork brown */
  --chart-3: hsl(20 30% 45%); /* Medium cork */
  --chart-4: hsl(43 74% 66%); /* Light cork */
  --chart-5: hsl(27 87% 67%); /* Warm orange */
  
  /* Sidebar colors */
  --sidebar-background: hsl(30 15% 20%); /* Dark cork texture */
  --sidebar-foreground: hsl(20 30% 90%);
  --sidebar-primary: hsl(20 70% 55%); /* Cork orange */
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(30 15% 25%);
  --sidebar-accent-foreground: hsl(20 30% 90%);
  --sidebar-border: hsl(30 15% 25%);
  --sidebar-ring: hsl(20 70% 55%);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
        font-family: var(--font-sans);
    }
}

/* Custom Cork Experience styles */
.cork-texture-bg {
  background-image: url('/assets/backgrounds/cork-texture-1.png');
  background-size: cover;
  background-repeat: no-repeat;
}

.cork-texture-light-bg {
  background-image: url('/assets/backgrounds/cork-texture-2.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-blend-mode: overlay;
}

.cork-texture-dark-bg {
  background-image: url('/assets/backgrounds/cork-texture-3.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-blend-mode: overlay;
}

/* Fade transition for page changes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
