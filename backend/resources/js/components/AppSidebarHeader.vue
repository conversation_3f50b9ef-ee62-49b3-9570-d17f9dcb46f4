<script setup lang="ts">
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import NotificationBell from '@/components/ui/notification/NotificationBell.vue';
import type { BreadcrumbItemType, SharedData } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

withDefaults(defineProps<{
    breadcrumbs?: BreadcrumbItemType[];
}>(),{
    breadcrumbs:()=>[]
});

const page = usePage<SharedData>();
const notifications = computed(() => page.props.notifications || []);

console.log('AppSidebarHeader - Notifications:', notifications.value);
</script>

<template>
    <header
        class="flex h-16 shrink-0 items-center justify-between border-b border-sidebar-border/70 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4"
    >
        <div class="flex items-center gap-2">
            <SidebarTrigger class="-ml-1" />
            <template v-if="breadcrumbs && breadcrumbs.length > 0">
                <Breadcrumbs :breadcrumbs="breadcrumbs" />
            </template>
        </div>

        <!-- Right side with notification bell -->
        <div class="flex items-center space-x-2">
            <!-- Notification Bell -->
            <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer">
                <NotificationBell :notifications="notifications" />
            </Button>
        </div>
    </header>
</template>
