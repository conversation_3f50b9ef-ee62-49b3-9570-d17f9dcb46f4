<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
// Define the NavItem type directly in this file
interface NavItem {
    title: string;
    href: string;
    icon?: any;
    disabled?: boolean;
    active?: boolean;
    isActive?: boolean;
}
import { Link, usePage } from '@inertiajs/vue3';
import { BookOpen, Building2, Newspaper, MapPin, CalendarDays, Coffee, Info, FileText, Box, HelpCircle, Settings, LayoutGrid, Calendar, Users } from 'lucide-vue-next';
import AppLogo from './AppLogo.vue';

// Use the router directly instead of router.route
const page = usePage();
// Type assertion for page.props
const user = (page.props as any).auth.user;

// Debug user object
console.log('User object:', user);

// Determine which menu items to show based on user role
const isSuperAdmin = user.role === 'superadmin';
const isAgencyUser = !!user.agency_id;

// Debug role flags
console.log('isSuperAdmin:', isSuperAdmin);
console.log('isAgencyUser:', isAgencyUser);

// Dashboard item for all users
const dashboardItem: NavItem = {
    title: 'Panel',
    href: '/dashboard',
    icon: LayoutGrid,
};

// Items for both superadmins and agency users
const sharedItems: NavItem[] = [
    // Empty for now, items moved to role-specific arrays
];

// Agency-specific menu items
const agencyItems: NavItem[] = [
    {
        title: 'Experiencias',
        href: '/dashboard/experiences',
        icon: Coffee,
    },
    {
        title: 'Rutas',
        href: '/dashboard/routes',
        icon: MapPin,
    },
    {
        title: 'Eventos',
        href: '/dashboard/news-events',
        icon: CalendarDays,
    },
    {
        title: 'Reservas',
        href: '/dashboard/reservations',
        icon: Calendar,
    },
];

// Superadmin-only menu items
const superAdminItems: NavItem[] = [
    {
        title: 'Experiencias',
        href: '/dashboard/experiences',
        icon: Coffee,
    },
    {
        title: 'Rutas',
        href: '/dashboard/routes',
        icon: MapPin,
    },
    {
        title: 'Noticias y Eventos',
        href: '/dashboard/news-events',
        icon: Newspaper,
    },
    {
        title: 'Ubicaciones',
        href: '/dashboard/locations',
        icon: MapPin,
    },
    {
        title: 'StoryTours',
        href: '/dashboard/story-tours',
        icon: Box,
    },
    {
        title: 'Operadores y Agencias',
        href: '/dashboard/agencies',
        icon: Building2,
    },
    {
        title: 'Reservas',
        href: '/dashboard/reservations',
        icon: Calendar,
    },
    {
        title: 'Grupos',
        href: '/dashboard/groups',
        icon: Users,
        active: route().current('admin.groups.*'),
    },
    {
        title: 'Sugerencias',
        href: '#',
        icon: Info,
        disabled: true,
    },
    {
        title: 'Informes',
        href: '#',
        icon: FileText,
        disabled: true,
    },
    {
        title: 'Configuración',
        href: '/dashboard/welcome-settings',
        icon: Settings,
    }
];

// Combine the appropriate menu items based on user role
let mainNavItems: NavItem[] = [];

// Always add the dashboard item
mainNavItems.push(dashboardItem);

// Add shared items
mainNavItems = [...mainNavItems, ...sharedItems];

// Add direct link to agency profile for agency users
if (isAgencyUser) {
    mainNavItems.push({
        title: 'Mi Agencia (Editar)',
        href: '/dashboard/my-agency',
        icon: Building2,
        active: route().current('admin.my-agency.edit'),
    });
}

// Add role-specific items
if (isSuperAdmin) {
    // Superadmin sees everything
    mainNavItems = [...mainNavItems, ...superAdminItems];
} else if (isAgencyUser) {
    // Agency users only see agency-specific items
    mainNavItems = [...mainNavItems, ...agencyItems];
}

// Documentation for all users
const docItem: NavItem = {
    title: 'Documentación',
    href: '/dashboard/documentation',
    icon: HelpCircle,
};

// API docs only for superadmins
const apiDocItem: NavItem = {
    title: 'API Docs',
    href: '/api/documentation',
    icon: BookOpen,
};

// Combine footer items based on user role
const footerNavItems: NavItem[] = [
    docItem,
    ...(isSuperAdmin ? [apiDocItem] : []),
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset" class="cork-texture-light-bg">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')" class="flex items-center">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
