<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON>bar<PERSON>ontent, <PERSON>barFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import AppLogo from '@/components/AppLogo.vue';
import NavMain from '@/components/NavMain.vue';
import NavFooter from '@/components/NavFooter.vue';
import NavUser from '@/components/NavUser.vue';
import { Link, usePage, router } from '@inertiajs/vue3';
import { Building2, CalendarDays, Coffee, FileText, LayoutGrid, MapPin, Calendar, Users, UserPlus } from 'lucide-vue-next';
import { type NavItem, type SharedData } from '@/types';

// Use the current URL to determine active state
const currentUrl = window.location.pathname;
const page = usePage<SharedData>();
const user = page.props.auth.user;
const isAgencyAdmin = user.role === 'agency_admin';

// Debug user role
console.log('AgencyUserSidebar - User role:', user.role);
console.log('AgencyUserSidebar - Is agency admin?', isAgencyAdmin);

// Dashboard item for all users
const dashboardItem: NavItem = {
    title: 'Panel',
    href: '/dashboard',
    icon: LayoutGrid,
};

// Agency-specific menu items
const agencyItems: NavItem[] = [
    // Only show Users management to agency admins
    ...(isAgencyAdmin ? [
        {
            title: 'Usuarios',
            href: '/dashboard/agency-user-management',
            icon: UserPlus,
            active: currentUrl.startsWith('/dashboard/agency-user-management'),
        },
    ] : []),
    {
        title: 'Mi Agencia',
        href: '/dashboard/my-agency',
        icon: Building2,
        active: currentUrl === '/dashboard/my-agency',
    },
    {
        title: 'Experiencias',
        href: '/dashboard/experiences',
        icon: Coffee,
    },
    {
        title: 'Rutas',
        href: '/dashboard/routes',
        icon: MapPin,
    },
    {
        title: 'Eventos',
        href: '/dashboard/events',
        icon: CalendarDays,
    },
    {
        title: 'Reservas',
        href: '/dashboard/reservations',
        icon: Calendar,
    },
    {
        title: 'Grupos',
        href: '/dashboard/groups',
        icon: Users,
        active: currentUrl === '/dashboard/groups',
    },
];

// Documentation for all users
const docItem: NavItem = {
    title: 'Documentación',
    href: '/dashboard/documentation',
    icon: FileText,
};

// Combine the appropriate menu items
const mainNavItems: NavItem[] = [dashboardItem, ...agencyItems];
const footerNavItems: NavItem[] = [docItem];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset" class="cork-texture-light-bg">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')" class="flex items-center">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
