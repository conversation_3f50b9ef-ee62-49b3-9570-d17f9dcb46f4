<template>
  <div v-if="links.length > 3" class="flex flex-wrap justify-center gap-1">
    <template v-for="(link, key) in links" :key="key">
      <div
        v-if="link.url === null"
        class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400 rounded-md"
        v-html="link.label"
      ></div>
      <Link
        v-else
        :href="link.url"
        class="px-4 py-2 text-sm rounded-md"
        :class="{
          'bg-primary text-white': link.active,
          'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600': !link.active
        }"
        v-html="link.label"
      ></Link>
    </template>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';

defineProps({
  links: {
    type: Array,
    default: () => [],
  },
});
</script>
