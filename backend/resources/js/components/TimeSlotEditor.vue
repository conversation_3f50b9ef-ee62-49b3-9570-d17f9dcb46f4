<template>
  <div class="time-slot-editor">
    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Configuración de horarios y capacidad</h3>

    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Capacidad por franja horaria
      </label>
      <input
        type="number"
        v-model="capacityPerTimeSlot"
        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 sm:text-sm"
        min="1"
        placeholder="Número máximo de personas por franja horaria"
      />
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Número máximo de personas que pueden reservar en cada franja horaria.
      </p>
    </div>

    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Capacidad máxima diaria
      </label>
      <input
        type="number"
        v-model="maxReservationsPerDay"
        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 sm:text-sm"
        min="0"
        placeholder="Número máximo de personas por día (0 = sin límite)"
      />
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Número máximo de personas que pueden reservar en un día. Dejar en 0 para no establecer límite.
      </p>
    </div>

    <!-- Time Slot Type Selection -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Tipo de horarios
      </label>
      <div class="mt-2 space-y-2">
        <div class="flex items-center">
          <input
            id="specific-slots"
            type="radio"
            :value="false"
            v-model="useRecurringSlots"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-700 dark:bg-gray-800"
          />
          <label for="specific-slots" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Horarios específicos (fechas concretas)
          </label>
        </div>
        <div class="flex items-center">
          <input
            id="recurring-slots"
            type="radio"
            :value="true"
            v-model="useRecurringSlots"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-700 dark:bg-gray-800"
          />
          <label for="recurring-slots" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Horarios recurrentes (por día de la semana)
          </label>
        </div>
      </div>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Selecciona si quieres configurar horarios específicos o recurrentes por día de la semana.
      </p>
    </div>

    <!-- Specific Time Slots (non-recurring) -->
    <div v-if="!useRecurringSlots" class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Franjas horarias disponibles
      </label>

      <div class="flex flex-wrap gap-2 mb-4">
        <div
          v-for="(timeSlot, index) in timeSlots"
          :key="index"
          class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-md px-3 py-1"
        >
          <span class="mr-2 text-gray-800 dark:text-gray-200">{{ timeSlot }}</span>
          <button
            type="button"
            @click="removeTimeSlot(index)"
            class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
          >
            <i class="pi pi-times"></i>
          </button>
        </div>
      </div>

      <div class="flex gap-2">
        <input
          type="time"
          v-model="newTimeSlot"
          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 sm:text-sm"
        />
        <button
          type="button"
          @click="addTimeSlot"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800"
          :disabled="!newTimeSlot"
        >
          Añadir
        </button>
      </div>

      <div class="mt-4">
        <button
          type="button"
          @click="addDefaultTimeSlots"
          class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Añadir horarios predeterminados
        </button>
        <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
          (Basados en el tipo de experiencia)
        </span>
      </div>
    </div>

    <!-- Recurring Time Slots (by day of week) -->
    <div v-if="useRecurringSlots" class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Horarios recurrentes por día de la semana
      </label>

      <div class="space-y-4 mt-2">
        <div v-for="(day, index) in weekDays" :key="index" class="border border-gray-200 dark:border-gray-700 rounded-md p-4">
          <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">{{ day.label }}</h4>

          <div class="flex flex-wrap gap-2 mb-4">
            <div
              v-for="(timeSlot, timeIndex) in getRecurringTimeSlotsForDay(day.value)"
              :key="`${day.value}-${timeIndex}`"
              class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-md px-3 py-1"
            >
              <span class="mr-2 text-gray-800 dark:text-gray-200">{{ timeSlot }}</span>
              <button
                type="button"
                @click="removeRecurringTimeSlot(day.value, timeIndex)"
                class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              >
                <i class="pi pi-times"></i>
              </button>
            </div>
          </div>

          <div class="flex gap-2">
            <input
              type="time"
              v-model="newRecurringTimeSlots[day.value]"
              class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 sm:text-sm"
            />
            <button
              type="button"
              @click="addRecurringTimeSlot(day.value)"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800"
              :disabled="!newRecurringTimeSlots[day.value]"
            >
              Añadir
            </button>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <button
          type="button"
          @click="addDefaultRecurringTimeSlots"
          class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Añadir horarios predeterminados para todos los días
        </button>
      </div>
    </div>

    <div class="flex justify-end">
      <button
        type="button"
        @click="saveTimeSlots"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800"
      >
        Guardar configuración
      </button>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        available_time_slots: [],
        recurring_time_slots: {},
        use_recurring_slots: false,
        capacity_per_time_slot: 10,
        max_reservations_per_day: 0
      })
    },
    experienceType: {
      type: String,
      default: 'activity'
    }
  },

  emits: ['update:model-value'],

  data() {
    return {
      timeSlots: Array.isArray(this.modelValue.available_time_slots) ? [...this.modelValue.available_time_slots] : [],
      recurringTimeSlots: this.modelValue.recurring_time_slots || {},
      useRecurringSlots: this.modelValue.use_recurring_slots || false,
      capacityPerTimeSlot: this.modelValue.capacity_per_time_slot || 10,
      maxReservationsPerDay: this.modelValue.max_reservations_per_day || 0,
      newTimeSlot: '',
      newRecurringTimeSlots: {
        monday: '',
        tuesday: '',
        wednesday: '',
        thursday: '',
        friday: '',
        saturday: '',
        sunday: ''
      } as Record<string, string>,
      weekDays: [
        { value: 'monday', label: 'Lunes' },
        { value: 'tuesday', label: 'Martes' },
        { value: 'wednesday', label: 'Miércoles' },
        { value: 'thursday', label: 'Jueves' },
        { value: 'friday', label: 'Viernes' },
        { value: 'saturday', label: 'Sábado' },
        { value: 'sunday', label: 'Domingo' }
      ]
    };
  },

  mounted() {
    console.log('TimeSlotEditor mounted with modelValue:', this.modelValue);

    // If we have time slots from the model, make sure they're in the correct format
    if (this.modelValue.available_time_slots && Array.isArray(this.modelValue.available_time_slots)) {
      // Make sure we're working with a copy of the array
      this.timeSlots = [...this.modelValue.available_time_slots];
    }

    // Initialize recurring time slots if they exist
    if (this.modelValue.recurring_time_slots) {
      this.recurringTimeSlots = JSON.parse(JSON.stringify(this.modelValue.recurring_time_slots));
    } else {
      // Initialize with empty arrays for each day
      this.weekDays.forEach(day => {
        if (!this.recurringTimeSlots[day.value]) {
          this.recurringTimeSlots[day.value] = [];
        }
      });
    }
  },

  methods: {
    addTimeSlot() {
      if (!this.newTimeSlot) return;

      // Format time to HH:MM
      const formattedTime = this.formatTime(this.newTimeSlot);

      // Check if time slot already exists
      if (!this.timeSlots.includes(formattedTime)) {
        this.timeSlots.push(formattedTime);
        this.sortTimeSlots();
      }

      this.newTimeSlot = '';
    },

    removeTimeSlot(index: number) {
      this.timeSlots.splice(index, 1);
    },

    formatTime(timeString: string) {
      const [hours, minutes] = timeString.split(':');
      return `${hours}:${minutes}`;
    },

    sortTimeSlots() {
      this.timeSlots.sort((a, b) => {
        const timeA = new Date(`2000-01-01T${a}`);
        const timeB = new Date(`2000-01-01T${b}`);
        return timeA.getTime() - timeB.getTime();
      });
    },

    addDefaultTimeSlots() {
      let defaultSlots = [];

      if (this.experienceType === 'restaurant') {
        defaultSlots = [
          '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
          '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00'
        ];
      } else if (this.experienceType === 'tour') {
        defaultSlots = ['09:00', '10:00', '11:00', '16:00', '17:00'];
      } else {
        // Default time slots for other experience types
        defaultSlots = ['09:00', '10:00', '11:00', '12:00', '16:00', '17:00', '18:00'];
      }

      // Add default slots if they don't already exist
      defaultSlots.forEach(slot => {
        if (!this.timeSlots.includes(slot)) {
          this.timeSlots.push(slot);
        }
      });

      this.sortTimeSlots();
    },

    // Methods for regular time slots
    saveTimeSlots() {
      const data = {
        available_time_slots: this.timeSlots,
        recurring_time_slots: this.recurringTimeSlots,
        use_recurring_slots: this.useRecurringSlots,
        capacity_per_time_slot: parseInt(this.capacityPerTimeSlot) || 10,
        max_reservations_per_day: parseInt(this.maxReservationsPerDay) || 0
      };

      console.log('Saving time slots:', data);
      this.$emit('update:model-value', data);

      // Show a success message
      alert('Configuración de horarios guardada. Haga clic en "Actualizar Experiencia" para guardar todos los cambios.');
    },

    // Methods for recurring time slots
    getRecurringTimeSlotsForDay(day: string) {
      return this.recurringTimeSlots[day] || [];
    },

    addRecurringTimeSlot(day: string) {
      if (!this.newRecurringTimeSlots[day]) return;

      // Initialize the day array if it doesn't exist
      if (!this.recurringTimeSlots[day]) {
        this.recurringTimeSlots[day] = [];
      }

      // Add the time slot if it doesn't already exist
      const formattedTime = this.formatTime(this.newRecurringTimeSlots[day]);
      if (!this.recurringTimeSlots[day].includes(formattedTime)) {
        this.recurringTimeSlots[day].push(formattedTime);
        // Sort time slots
        this.recurringTimeSlots[day].sort();
      }

      // Clear the input
      this.newRecurringTimeSlots[day] = '';
    },

    removeRecurringTimeSlot(day: string, index: number) {
      if (this.recurringTimeSlots[day]) {
        this.recurringTimeSlots[day].splice(index, 1);
      }
    },

    addDefaultRecurringTimeSlots() {
      // Default time slots based on experience type
      let defaultSlots = [];

      switch (this.experienceType) {
        case 'restaurant':
          defaultSlots = ['13:00', '14:00', '20:00', '21:00'];
          break;
        case 'tour':
          defaultSlots = ['09:00', '11:00', '16:00', '18:00'];
          break;
        case 'activity':
          defaultSlots = ['10:00', '12:00', '15:00', '17:00'];
          break;
        case 'accommodation':
          defaultSlots = ['12:00', '14:00', '16:00', '18:00'];
          break;
        default:
          defaultSlots = ['10:00', '12:00', '15:00', '17:00'];
      }

      // Add default slots to each day
      this.weekDays.forEach(day => {
        if (!this.recurringTimeSlots[day.value]) {
          this.recurringTimeSlots[day.value] = [];
        }

        defaultSlots.forEach(slot => {
          if (!this.recurringTimeSlots[day.value].includes(slot)) {
            this.recurringTimeSlots[day.value].push(slot);
          }
        });

        // Sort time slots
        this.recurringTimeSlots[day.value].sort();
      });
    }
  }
};
</script>

<style scoped>
.time-slot-editor {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

@media (prefers-color-scheme: dark) {
  .time-slot-editor {
    background-color: #1e293b; /* dark:bg-slate-800 */
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  }
}
</style>
