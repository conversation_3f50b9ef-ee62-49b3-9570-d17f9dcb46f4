<script setup lang="ts">
// No need for AppLogoIcon as we'll use the Cork Experience logo directly
</script>

<template>
    <div class="flex items-center">
        <img src="/assets/logos/CorkExpLogoBlack.png" alt="Cork Experience Logo" class="h-8 w-auto dark:hidden" />
        <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience Logo" class="h-8 w-auto hidden dark:block" />
        <div class="ml-2 grid flex-1 text-left text-sm">
            <span class="mb-0.5 truncate font-semibold leading-none">Cork Experience</span>
        </div>
    </div>
</template>
