<script setup lang="ts">
import { computed } from 'vue';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { router } from '@inertiajs/vue3';
import { Bell, Calendar, CheckCircle, AlertCircle, Info } from 'lucide-vue-next';

const props = defineProps<{
  notification: {
    id: string;
    data: any;
    read_at: string | null;
    created_at: string;
  };
}>();

console.log('NotificationItem - Notification:', props.notification);

const isRead = computed(() => {
  try {
    console.log('NotificationItem - Checking if notification is read:', props.notification);
    return !!props.notification.read_at;
  } catch (error) {
    console.error('Error checking if notification is read:', error);
    return false;
  }
});

const formattedDate = computed(() => {
  try {
    if (!props.notification.created_at) return 'Fecha desconocida';
    const date = new Date(props.notification.created_at);
    return formatDistanceToNow(date, { addSuffix: true, locale: es });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Fecha desconocida';
  }
});

// Helper function to parse notification data
const getNotificationData = (notification: any) => {
  if (!notification.data) {
    return { type: 'unknown' };
  }

  if (typeof notification.data === 'string') {
    try {
      return JSON.parse(notification.data);
    } catch (e) {
      console.error('Failed to parse notification data:', e);
      return { type: 'unknown' };
    }
  }

  return notification.data;
};

const notificationIcon = computed(() => {
  try {
    console.log('NotificationItem - Getting icon for notification:', props.notification);

    const data = getNotificationData(props.notification);
    const type = data.type;

    console.log('NotificationItem - Notification type for icon:', type);

    switch (type) {
      case 'reservation_created':
      case 'reservation_updated':
        return Calendar;
      case 'new_agency_user':
        return Bell;
      case 'system':
        if (data.importance === 'success') {
          return CheckCircle;
        } else if (data.importance === 'error' || data.importance === 'warning') {
          return AlertCircle;
        } else {
          return Info;
        }
      default:
        return Bell;
    }
  } catch (error) {
    console.error('Error getting notification icon:', error);
    return Bell;
  }
});

const notificationTitle = computed(() => {
  try {
    console.log('NotificationItem - Getting title for notification:', props.notification);

    const data = getNotificationData(props.notification);
    const type = data.type;

    console.log('NotificationItem - Notification type for title:', type);

    switch (type) {
      case 'reservation_created':
        return `Nueva reserva: ${data.experience_title || 'Sin título'}`;
      case 'reservation_updated':
        return `Reserva actualizada: ${data.experience_title || 'Sin título'}`;
      case 'new_agency_user':
        return `Nuevo usuario: ${data.user_name || 'Sin nombre'}`;
      case 'system':
        return data.title || 'Notificación del sistema';
      default:
        return 'Notificación';
    }
  } catch (error) {
    console.error('Error getting notification title:', error);
    return 'Notificación';
  }
});

const notificationDescription = computed(() => {
  try {
    console.log('NotificationItem - Getting description for notification:', props.notification);

    const data = getNotificationData(props.notification);
    const type = data.type;

    console.log('NotificationItem - Notification type for description:', type);

    switch (type) {
      case 'reservation_created':
        return `${data.user_name || 'Usuario'} ha reservado para ${data.num_people || '?'} personas el ${data.reservation_date || 'fecha no especificada'}`;
      case 'reservation_updated':
        return `Estado: ${data.status || 'No especificado'}`;
      case 'new_agency_user':
        return `${data.user_email || 'Usuario'} se ha unido a ${data.agency_name || 'la agencia'}`;
      case 'system':
        return data.message || '';
      default:
        return '';
    }
  } catch (error) {
    console.error('Error getting notification description:', error);
    return '';
  }
});

const notificationUrl = computed(() => {
  try {
    console.log('NotificationItem - Getting URL for notification:', props.notification);

    const data = getNotificationData(props.notification);
    const type = data.type;

    console.log('NotificationItem - Notification type for URL:', type);

    switch (type) {
      case 'reservation_created':
      case 'reservation_updated':
        return data.reservation_id ? `/dashboard/reservations/${data.reservation_id}` : null;
      case 'new_agency_user':
        return '/dashboard/agency-user-management';
      case 'system':
        return data.action_url || null;
      default:
        return null;
    }
  } catch (error) {
    console.error('Error getting notification URL:', error);
    return null;
  }
});

const markAsRead = () => {
  try {
    console.log('NotificationItem - Marking notification as read:', props.notification);

    if (isRead.value) {
      console.log('NotificationItem - Notification already marked as read');
      return;
    }

    if (!props.notification.id) {
      console.error('Cannot mark notification as read: missing ID');
      return;
    }

    const url = `/dashboard/notifications/${props.notification.id}/mark-as-read`;
    console.log('NotificationItem - Marking as read URL:', url);

    router.post(url, {}, {
      preserveScroll: true,
      onSuccess: () => {
        // The notification will be updated via the Inertia response
        console.log('Notification marked as read successfully');
      },
      onError: (errors) => {
        console.error('Error marking notification as read:', errors);
      }
    });
  } catch (error) {
    console.error('Error in markAsRead:', error);
  }
};

const handleClick = () => {
  try {
    console.log('NotificationItem - Handling click on notification:', props.notification);
    markAsRead();

    if (notificationUrl.value) {
      console.log('NotificationItem - Navigating to URL:', notificationUrl.value);
      router.visit(notificationUrl.value);
    } else {
      console.log('NotificationItem - No URL to navigate to');
    }
  } catch (error) {
    console.error('Error in handleClick:', error);
  }
};
</script>

<template>
  <div
    :class="[
      'flex items-start gap-3 p-3 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 transition-colors',
      { 'bg-blue-50/50': !isRead }
    ]"
    @click="handleClick"
  >
    <div
      :class="[
        'flex-shrink-0 rounded-full p-2',
        isRead ? 'bg-gray-100' : 'bg-blue-100'
      ]"
    >
      <component
        :is="notificationIcon"
        class="h-4 w-4"
        :class="isRead ? 'text-gray-500' : 'text-blue-500'"
      />
    </div>

    <div class="flex-1 min-w-0">
      <h4
        :class="[
          'text-sm font-medium truncate',
          isRead ? 'text-gray-700' : 'text-gray-900'
        ]"
      >
        {{ notificationTitle }}
      </h4>
      <p
        :class="[
          'text-xs mt-0.5 line-clamp-2',
          isRead ? 'text-gray-500' : 'text-gray-700'
        ]"
      >
        {{ notificationDescription }}
      </p>
      <span class="text-xs text-gray-400 mt-1 block">
        {{ formattedDate }}
      </span>
    </div>

    <div v-if="!isRead" class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
  </div>
</template>
