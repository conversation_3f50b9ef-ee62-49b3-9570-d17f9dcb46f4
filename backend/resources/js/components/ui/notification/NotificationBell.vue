<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { Bell } from 'lucide-vue-next';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import NotificationItem from '@/components/ui/notification/NotificationItem.vue';
import { router } from '@inertiajs/vue3';

interface Notification {
  id: string;
  data: any;
  read_at: string | null;
  created_at: string;
}

const props = defineProps<{
  notifications: Array<Notification> | any;
}>();

const normalizedNotifications = computed(() => {
  // Handle case where notifications is not an array
  if (!Array.isArray(props.notifications)) {
    console.warn('NotificationBell - Notifications is not an array:', props.notifications);
    return [];
  }
  return props.notifications;
});

onMounted(() => {
  console.log('NotificationBell - Component mounted');
  console.log('NotificationBell - Props:', props);
  console.log('NotificationBell - Notifications:', normalizedNotifications.value);
});

const unreadCount = computed(() => {
  try {
    return normalizedNotifications.value.filter(notification => !notification.read_at).length;
  } catch (error) {
    console.error('Error calculating unread count:', error);
    return 0;
  }
});

const hasUnread = computed(() => unreadCount.value > 0);

const markAllAsRead = () => {
  try {
    router.post('/dashboard/notifications/mark-all-as-read', {}, {
      preserveScroll: true,
      onSuccess: () => {
        console.log('All notifications marked as read');
      },
      onError: (errors) => {
        console.error('Error marking all notifications as read:', errors);
      }
    });
  } catch (error) {
    console.error('Error in markAllAsRead:', error);
  }
};

const viewAllNotifications = () => {
  try {
    router.get('/dashboard/notifications');
  } catch (error) {
    console.error('Error in viewAllNotifications:', error);
  }
};
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger class="relative">
      <Bell class="h-5 w-5" />
      <span v-if="hasUnread" class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
        {{ unreadCount > 9 ? '9+' : unreadCount }}
      </span>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-80">
      <div class="flex items-center justify-between px-4 py-2 border-b">
        <h3 class="font-medium">Notificaciones</h3>
        <button
          v-if="hasUnread"
          @click="markAllAsRead"
          class="text-xs text-blue-600 hover:text-blue-800"
        >
          Marcar todas como leídas
        </button>
      </div>

      <div class="max-h-[300px] overflow-y-auto">
        <!-- Debug info -->
        <div v-if="normalizedNotifications.length === 0" class="py-4 px-4 text-center text-gray-500">
          No tienes notificaciones
        </div>

        <!-- Notification items -->
        <template v-if="normalizedNotifications.length > 0">
          <NotificationItem
            v-for="notification in normalizedNotifications.slice(0, 5)"
            :key="notification.id"
            :notification="notification"
          />
        </template>
      </div>

      <div class="border-t p-2 text-center">
        <button
          @click="viewAllNotifications"
          class="text-sm text-blue-600 hover:text-blue-800 w-full py-1"
        >
          Ver todas las notificaciones
        </button>
      </div>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
