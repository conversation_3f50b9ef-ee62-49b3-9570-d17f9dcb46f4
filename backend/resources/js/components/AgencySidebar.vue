<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage, router } from '@inertiajs/vue3';
const route = router.route;
import { BookOpen, Folder, LayoutGrid, Building2, Newspaper, MapPin, CalendarDays, Coffee, Info, FileText, Box, HelpCircle, Calendar, Users, UserPlus } from 'lucide-vue-next';
import AppLogo from './AppLogo.vue';

const page = usePage();
const user = page.props.auth.user;
const isAgencyAdmin = user.role === 'agency_admin';

// Debug user role
console.log('User role:', user.role);
console.log('Is agency admin?', isAgencyAdmin);

// Agency users only see items relevant to them
const mainNavItems: NavItem[] = [
    {
        title: 'Panel',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Mi Agencia',
        href: '/dashboard/my-agency',
        icon: Building2,
        active: route().current('admin.my-agency.edit'),
    },
    {
        title: 'Eventos',
        href: '/dashboard/events',
        icon: CalendarDays,
    },
    {
        title: 'Experiencias',
        href: '/dashboard/experiences',
        icon: Coffee,
    },
    {
        title: 'Rutas',
        href: '/dashboard/routes',
        icon: MapPin,
    },

    {
        title: 'Reservas',
        href: '/dashboard/reservations',
        icon: Calendar,
    },
    {
        title: 'Grupos',
        href: '/dashboard/groups',
        icon: Users,
        active: route().current('admin.groups.*'),
    },
    // Only show Users management to agency admins
    ...(isAgencyAdmin ? [
        {
            title: 'Usuarios',
            href: '/dashboard/agency-user-management',
            icon: UserPlus,
            active: route().current('admin.agency-user-management.*'),
        },
    ] : []),
];

const footerNavItems: NavItem[] = [
    {
        title: 'Documentación',
        href: '/dashboard/documentation',
        icon: HelpCircle,
    },
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset" class="cork-texture-light-bg">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')" class="flex items-center">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
