<template>
  <Head title="Editar Configuración" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Editar Configuración</h1>
        <Link
          href="/dashboard/app-settings"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Volver
        </Link>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
          <form @submit.prevent="submit">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label for="key" class="mb-1">Clave</Label>
                <Input
                  id="key"
                  v-model="form.key"
                  type="text"
                  class="w-full"
                  placeholder="welcome_title"
                />
                <div v-if="form.errors.key" class="text-red-500 text-sm mt-1">{{ form.errors.key }}</div>
              </div>
              
              <div>
                <Label for="label" class="mb-1">Etiqueta</Label>
                <Input
                  id="label"
                  v-model="form.label"
                  type="text"
                  class="w-full"
                  placeholder="Título de Bienvenida"
                />
                <div v-if="form.errors.label" class="text-red-500 text-sm mt-1">{{ form.errors.label }}</div>
              </div>
              
              <div>
                <Label for="type" class="mb-1">Tipo</Label>
                <select
                  id="type"
                  v-model="form.type"
                  class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 shadow-sm"
                >
                  <option value="text">Texto</option>
                  <option value="textarea">Texto Largo</option>
                  <option value="image">Imagen</option>
                  <option value="video">Video</option>
                  <option value="boolean">Booleano</option>
                  <option value="number">Número</option>
                </select>
                <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
              </div>
              
              <div>
                <Label for="group" class="mb-1">Grupo</Label>
                <Input
                  id="group"
                  v-model="form.group"
                  type="text"
                  class="w-full"
                  placeholder="welcome"
                />
                <div v-if="form.errors.group" class="text-red-500 text-sm mt-1">{{ form.errors.group }}</div>
              </div>
              
              <div class="md:col-span-2">
                <Label for="description" class="mb-1">Descripción</Label>
                <Textarea
                  id="description"
                  v-model="form.description"
                  class="w-full"
                  placeholder="Descripción de la configuración..."
                />
                <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
              </div>
              
              <div class="md:col-span-2">
                <Label for="value" class="mb-1">Valor</Label>
                <Textarea
                  v-if="form.type === 'textarea'"
                  id="value"
                  v-model="form.value"
                  class="w-full"
                  placeholder="Valor de la configuración..."
                />
                <Input
                  v-else-if="form.type === 'text' || form.type === 'number'"
                  id="value"
                  v-model="form.value"
                  :type="form.type === 'number' ? 'number' : 'text'"
                  class="w-full"
                  placeholder="Valor de la configuración..."
                />
                <div class="flex items-center" v-else-if="form.type === 'boolean'">
                  <input
                    id="value"
                    v-model="form.value"
                    type="checkbox"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="value" class="ml-2 text-sm text-gray-600 dark:text-gray-400">Activado</label>
                </div>
                <div v-else-if="form.type === 'image' || form.type === 'video'">
                  <div class="flex items-center space-x-4 mb-2">
                    <div v-if="appSetting.value && form.type === 'image'" class="w-24 h-24 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                      <img :src="getImageUrl(appSetting.value)" alt="Image" class="max-w-full max-h-full object-contain" />
                    </div>
                    <div v-if="appSetting.value && form.type === 'video'" class="flex items-center">
                      <span class="text-sm text-gray-500">Archivo actual: </span>
                      <a :href="getImageUrl(appSetting.value)" target="_blank" class="text-blue-500 hover:underline ml-2">Ver archivo</a>
                    </div>
                  </div>
                  <Input
                    id="file"
                    type="file"
                    :accept="form.type === 'image' ? 'image/*' : 'video/*'"
                    @change="handleFileChange"
                  />
                </div>
                <div v-if="form.errors.value" class="text-red-500 text-sm mt-1">{{ form.errors.value }}</div>
              </div>
              
              <div>
                <div class="flex items-center">
                  <input
                    id="is_public"
                    v-model="form.is_public"
                    type="checkbox"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="is_public" class="ml-2 text-sm text-gray-600 dark:text-gray-400">Público (accesible desde la API)</label>
                </div>
                <div v-if="form.errors.is_public" class="text-red-500 text-sm mt-1">{{ form.errors.is_public }}</div>
              </div>
            </div>
            
            <div class="mt-6 flex justify-end">
              <Button type="submit" :disabled="form.processing">
                {{ form.processing ? 'Guardando...' : 'Actualizar' }}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';

const props = defineProps<{
  appSetting: {
    id: number;
    key: string;
    value: string | null;
    type: string;
    group: string;
    label: string;
    description: string | null;
    is_public: boolean;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Configuración',
    href: '/dashboard/app-settings',
  },
  {
    title: 'Editar',
    href: `/dashboard/app-settings/${props.appSetting.id}/edit`,
  },
];

const form = useForm({
  key: props.appSetting.key,
  value: props.appSetting.value,
  type: props.appSetting.type,
  group: props.appSetting.group,
  label: props.appSetting.label,
  description: props.appSetting.description,
  is_public: props.appSetting.is_public,
  file: null as File | null,
});

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form.file = input.files[0];
  }
};

const getImageUrl = (path: string | null) => {
  if (!path) return '';
  if (path.startsWith('http')) return path;
  return `/storage/${path}`;
};

const submit = () => {
  form.post(`/dashboard/app-settings/${props.appSetting.id}?_method=PUT`, {
    preserveScroll: true,
    forceFormData: true,
  });
};
</script>
