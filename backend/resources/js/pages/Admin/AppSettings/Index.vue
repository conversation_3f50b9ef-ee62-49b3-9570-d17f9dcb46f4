<template>
  <Head title="Configuración de la Aplicación" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Configuración de la Aplicación</h1>
        <div class="flex space-x-2">
          <Link
            href="/dashboard/app-settings/create"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Nueva Configuración
          </Link>
          <Link
            href="/dashboard/welcome-settings"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Pantalla de Bienvenida
          </Link>
        </div>
      </div>

      <div v-if="Object.keys(settingsByGroup).length === 0" class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <p class="text-center text-gray-500 dark:text-gray-400">No hay configuraciones disponibles.</p>
      </div>

      <div v-else class="space-y-6">
        <div v-for="(settings, group) in settingsByGroup" :key="group" class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div class="bg-gray-100 dark:bg-gray-700 px-6 py-3 border-b border-gray-200 dark:border-gray-600">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white capitalize">{{ group }}</h2>
          </div>
          
          <div class="p-6">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Clave</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Etiqueta</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tipo</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Valor</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Público</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Acciones</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  <tr v-for="setting in settings" :key="setting.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ setting.key }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ setting.label }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 capitalize">{{ setting.type }}</td>
                    <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <div v-if="setting.type === 'image'" class="w-12 h-12 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                        <img v-if="setting.value" :src="getImageUrl(setting.value)" alt="Image" class="max-w-full max-h-full object-contain" />
                        <span v-else class="text-xs text-gray-400">Sin imagen</span>
                      </div>
                      <div v-else-if="setting.type === 'video'" class="flex items-center">
                        <span v-if="setting.value">
                          <a :href="getImageUrl(setting.value)" target="_blank" class="text-blue-500 hover:underline">Ver video</a>
                        </span>
                        <span v-else class="text-xs text-gray-400">Sin video</span>
                      </div>
                      <div v-else-if="setting.type === 'textarea'" class="max-w-xs truncate">
                        {{ setting.value || '-' }}
                      </div>
                      <div v-else>
                        {{ setting.value || '-' }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <span v-if="setting.is_public" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Sí</span>
                      <span v-else class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">No</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link :href="`/dashboard/app-settings/${setting.id}/edit`" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3">Editar</Link>
                      <button @click="confirmDelete(setting)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">Eliminar</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  settingsByGroup: Record<string, Array<{
    id: number;
    key: string;
    value: string | null;
    type: string;
    group: string;
    label: string;
    description: string | null;
    is_public: boolean;
  }>>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Configuración',
    href: '/dashboard/app-settings',
  },
];

const getImageUrl = (path: string | null) => {
  if (!path) return '';
  if (path.startsWith('http')) return path;
  return `/storage/${path}`;
};

const confirmDelete = (setting: any) => {
  if (confirm(`¿Estás seguro de que deseas eliminar la configuración "${setting.label}"?`)) {
    router.delete(`/dashboard/app-settings/${setting.id}`);
  }
};
</script>
