<template>
  <Head title="Configuración de Pantalla de Bienvenida" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Configuración de Pantalla de Bienvenida</h1>
        <Link
          href="/dashboard/app-settings"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Volver
        </Link>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
          <form @submit.prevent="submit" enctype="multipart/form-data">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Text Settings -->
              <div class="space-y-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Textos</h2>

                <div>
                  <Label for="welcome_title" class="mb-1">Título de Bienvenida</Label>
                  <Input
                    id="welcome_title"
                    v-model="form.welcome_title"
                    type="text"
                    class="w-full"
                    placeholder="Bienvenido | Cork Experience"
                  />
                  <p class="text-sm text-gray-500 mt-1">Usa el símbolo | para separar partes del título</p>
                </div>

                <div>
                  <Label for="video_title" class="mb-1">Título del Video (opcional)</Label>
                  <Input
                    id="video_title"
                    v-model="form.video_title"
                    type="text"
                    class="w-full"
                    placeholder="Cork Experience"
                  />
                  <p class="text-sm text-gray-500 mt-1">Dejar en blanco para no mostrar título</p>
                </div>

                <div>
                  <Label for="video_description" class="mb-1">Descripción del Video (opcional)</Label>
                  <Textarea
                    id="video_description"
                    v-model="form.video_description"
                    class="w-full h-40"
                    placeholder="Descripción que aparece debajo del video..."
                  />
                  <p class="text-sm text-gray-500 mt-1">Dejar en blanco para usar el texto por defecto</p>
                </div>
              </div>

              <!-- Media Settings -->
              <div class="space-y-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Multimedia</h2>

                <div>
                  <Label for="welcome_logo" class="mb-1">Logo de Bienvenida</Label>
                  <div class="flex items-center space-x-4">
                    <div v-if="settings.welcome_logo" class="w-24 h-24 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                      <img :src="getImageUrl(settings.welcome_logo)" alt="Logo de Bienvenida" class="max-w-full max-h-full object-contain" />
                    </div>
                    <Input
                      id="welcome_logo"
                      type="file"
                      accept="image/*"
                      @change="handleFileChange($event, 'welcome_logo')"
                    />
                  </div>
                </div>

                <div>
                  <Label for="video_placeholder" class="mb-1">Imagen de Placeholder del Video</Label>
                  <div class="flex items-center space-x-4">
                    <div v-if="settings.video_placeholder" class="w-24 h-24 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                      <img :src="getImageUrl(settings.video_placeholder)" alt="Placeholder del Video" class="max-w-full max-h-full object-contain" />
                    </div>
                    <Input
                      id="video_placeholder"
                      type="file"
                      accept="image/*"
                      @change="handleFileChange($event, 'video_placeholder')"
                    />
                  </div>
                </div>

                <div>
                  <Label for="video_file" class="mb-1">Video de Bienvenida</Label>
                  <div class="flex items-center space-x-4">
                    <div v-if="settings.welcome_video" class="flex items-center">
                      <span class="text-sm text-gray-500">Video actual: </span>
                      <a :href="getImageUrl(settings.welcome_video)" target="_blank" class="text-blue-500 hover:underline ml-2">Ver video</a>
                    </div>
                    <Input
                      id="video_file"
                      type="file"
                      accept="video/*"
                      @change="handleFileChange($event, 'video_file')"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-6 flex justify-end">
              <Button type="submit" :disabled="form.processing">
                {{ form.processing ? 'Guardando...' : 'Guardar Cambios' }}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps<{
  settings: Record<string, string | null>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Configuración',
    href: '/dashboard/app-settings',
  },
  {
    title: 'Pantalla de Bienvenida',
    href: '/dashboard/welcome-settings',
  },
];

const form = useForm({
  welcome_title: props.settings.welcome_title || 'Bienvenido | Cork Experience',
  video_title: props.settings.video_title || '',
  video_description: props.settings.video_description || '',
  welcome_logo: null as File | null,
  video_placeholder: null as File | null,
  video_file: null as File | null,
});

const handleFileChange = (event: Event, field: string) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form[field] = input.files[0];
  }
};

const getImageUrl = (path: string | null) => {
  if (!path) return '';
  if (path.startsWith('http')) return path;
  return `/storage/${path}`;
};

const submit = () => {
  // Debug message
  console.log('Submitting form with data:', {
    welcome_title: form.welcome_title,
    video_title: form.video_title,
    video_description: form.video_description,
    has_welcome_logo: !!form.welcome_logo,
    has_video_placeholder: !!form.video_placeholder,
    has_video_file: !!form.video_file,
  });

  // Use FormData to ensure files are properly uploaded
  form.post('/dashboard/welcome-settings', {
    preserveScroll: true,
    forceFormData: true,
    onSuccess: (page) => {
      // Reset file inputs
      form.welcome_logo = null;
      form.video_placeholder = null;
      form.video_file = null;

      // Show success message
      alert('Configuración guardada correctamente');
      console.log('Settings saved successfully', page);
    },
    onError: (errors) => {
      console.error('Error saving settings:', errors);
      alert('Error al guardar la configuración. Por favor, revise los campos e intente de nuevo.');
    }
  });
};
</script>
