<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps<{
  experiences: Array<{
    id: number;
    title: string;
    short_description: string | null;
    type: string;
    price: number | null;
    is_featured: boolean;
    is_active: boolean;
    location: {
      id: number;
      name: string;
    } | null;
    agency: {
      id: number;
      name: string;
    } | null;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Experiencias',
    href: '/dashboard/experiences',
  },
];

const searchQuery = ref('');
const filteredExperiences = computed(() => {
  if (!searchQuery.value) return props.experiences;
  
  const query = searchQuery.value.toLowerCase();
  return props.experiences.filter(experience => 
    experience.title.toLowerCase().includes(query) || 
    (experience.short_description && experience.short_description.toLowerCase().includes(query)) ||
    experience.type.toLowerCase().includes(query) ||
    (experience.location && experience.location.name.toLowerCase().includes(query)) ||
    (experience.agency && experience.agency.name.toLowerCase().includes(query))
  );
});

const getTypeLabel = (type: string) => {
  const types: Record<string, string> = {
    'activity': 'Actividad',
    'tour': 'Tour',
    'workshop': 'Taller',
    'hotel': 'Alojamiento',
    'restaurant': 'Restaurante',
    'museum': 'Museo',
    'park': 'Parque',
  };
  
  return types[type] || type;
};

const formatPrice = (price: number | null) => {
  if (price === null) return 'Gratis';
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(price);
};

const confirmDelete = (id: number) => {
  if (confirm('¿Estás seguro de que quieres eliminar esta experiencia?')) {
    useForm({}).delete(`/dashboard/experiences/${id}`);
  }
};
</script>

<template>
  <Head title="Experiencias" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Experiencias</h1>
        <Link
          href="/dashboard/experiences/create"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Crear Experiencia
        </Link>
      </div>

      <!-- Search Bar -->
      <div class="mb-4">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Buscar experiencias..."
          class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Título
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tipo
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ubicación
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Agencia
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Precio
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Estado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Destacado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="experience in filteredExperiences" :key="experience.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ experience.title }}
                  </div>
                  <div v-if="experience.short_description" class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                    {{ experience.short_description }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                    {{ getTypeLabel(experience.type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ experience.location ? experience.location.name : 'No especificada' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ experience.agency ? experience.agency.name : 'No especificada' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatPrice(experience.price) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${experience.is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`">
                    {{ experience.is_active ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <span :class="`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${experience.is_featured ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`">
                    {{ experience.is_featured ? 'Destacado' : 'Normal' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <Link :href="`/dashboard/experiences/${experience.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                      Ver
                    </Link>
                    <Link :href="`/dashboard/experiences/${experience.id}/edit`" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                      Editar
                    </Link>
                    <button @click="confirmDelete(experience.id)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      Eliminar
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredExperiences.length === 0">
                <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No se encontraron experiencias
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
