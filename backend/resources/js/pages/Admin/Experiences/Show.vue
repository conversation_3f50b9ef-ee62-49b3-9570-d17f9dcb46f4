<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';

const props = defineProps<{
  experience: {
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    location: {
      id: number;
      name: string;
    } | null;
    agency: {
      id: number;
      name: string;
    } | null;
    type: string;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    price: number | null;
    image: string | null;
    start_date: string | null;
    end_date: string | null;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    // Restaurant-specific fields
    address?: string | null;
    phone?: string | null;
    email?: string | null;
    website?: string | null;
    cuisine_type?: string | null;
    opening_hours?: string | null;
    menu_url?: string | null;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Experiencias',
    href: '/dashboard/experiences',
  },
  {
    title: 'Ver',
    href: `/dashboard/experiences/${props.experience.id}`,
  },
];

const getTypeLabel = (type: string) => {
  const types: Record<string, string> = {
    'activity': 'Actividad',
    'tour': 'Tour',
    'workshop': 'Taller',
    'hotel': 'Alojamiento',
    'restaurant': 'Restaurante',
    'museum': 'Museo',
    'park': 'Parque',
  };

  return types[type] || type;
};

const getDifficultyLabel = (difficulty: string | null) => {
  if (!difficulty) return 'No especificada';

  const levels: Record<string, string> = {
    'easy': 'Fácil',
    'moderate': 'Moderado',
    'hard': 'Difícil',
    'expert': 'Experto',
  };

  return levels[difficulty] || difficulty;
};

const formatPrice = (price: number | null) => {
  if (price === null) return 'Gratis';
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(price);
};

const formatDate = (date: string | null) => {
  if (!date) return 'No especificada';
  return new Date(date).toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit', year: 'numeric' });
};

const confirmDelete = () => {
  if (confirm('¿Estás seguro de que quieres eliminar esta experiencia?')) {
    useForm({}).delete(`/dashboard/experiences/${props.experience.id}`);
  }
};
</script>

<template>
  <Head :title="experience.title" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">{{ experience.title }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/experiences/${experience.id}/edit`"
            class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
          >
            Editar
          </Link>
          <button
            @click="confirmDelete"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Eliminar
          </button>
          <Link
            href="/dashboard/experiences"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Left column: Image and basic info -->
            <div class="md:col-span-1">
              <div v-if="experience.image" class="mb-4">
                <img :src="experience.image" :alt="experience.title" class="w-full h-auto rounded-lg shadow-md" />
              </div>
              <div v-else class="mb-4 bg-gray-200 dark:bg-gray-700 rounded-lg h-48 flex items-center justify-center">
                <span class="text-gray-500 dark:text-gray-400">Sin imagen</span>
              </div>

              <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mt-4">
                <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Información Básica</h3>

                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Tipo:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ getTypeLabel(experience.type) }}</span>
                </div>

                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Precio:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatPrice(experience.price) }}</span>
                </div>

                <div class="mb-2" v-if="experience.duration">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Duración:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ experience.duration }}</span>
                </div>

                <div class="mb-2" v-if="experience.distance">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Distancia:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ experience.distance }}</span>
                </div>

                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Dificultad:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ getDifficultyLabel(experience.difficulty) }}</span>
                </div>

                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Estado:</span>
                  <span :class="`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${experience.is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`">
                    {{ experience.is_active ? 'Activo' : 'Inactivo' }}
                  </span>
                </div>

                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Destacado:</span>
                  <span :class="`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${experience.is_featured ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`">
                    {{ experience.is_featured ? 'Destacado' : 'Normal' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Right column: Description and details -->
            <div class="md:col-span-2">
              <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Descripción</h3>
                <div v-if="experience.short_description" class="mb-2 text-sm italic text-gray-600 dark:text-gray-300">
                  {{ experience.short_description }}
                </div>
                <div class="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                  {{ experience.description }}
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Ubicación y Agencia</h3>

                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Ubicación:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">
                      {{ experience.location ? experience.location.name : 'No especificada' }}
                    </span>
                  </div>

                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Agencia:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">
                      {{ experience.agency ? experience.agency.name : 'No especificada' }}
                    </span>
                  </div>
                </div>

                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Fechas</h3>

                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Fecha de inicio:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatDate(experience.start_date) }}</span>
                  </div>

                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Fecha de fin:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatDate(experience.end_date) }}</span>
                  </div>

                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Creado:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ new Date(experience.created_at).toLocaleString('es-ES') }}</span>
                  </div>

                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Actualizado:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ new Date(experience.updated_at).toLocaleString('es-ES') }}</span>
                  </div>
                </div>
              </div>

              <!-- Restaurant-specific information -->
              <div v-if="experience.type === 'restaurant'" class="md:col-span-2 mt-6">
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Información del Restaurante</h3>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-if="experience.address" class="mb-2">
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Dirección:</span>
                      <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ experience.address }}</span>
                    </div>

                    <div v-if="experience.phone" class="mb-2">
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Teléfono:</span>
                      <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ experience.phone }}</span>
                    </div>

                    <div v-if="experience.email" class="mb-2">
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Email:</span>
                      <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ experience.email }}</span>
                    </div>

                    <div v-if="experience.website" class="mb-2">
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Sitio Web:</span>
                      <a :href="experience.website" target="_blank" class="ml-2 text-sm text-blue-500 hover:underline">{{ experience.website }}</a>
                    </div>

                    <div v-if="experience.cuisine_type" class="mb-2">
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Tipo de Cocina:</span>
                      <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ experience.cuisine_type }}</span>
                    </div>

                    <div v-if="experience.menu_url" class="mb-2">
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">URL del Menú:</span>
                      <a :href="experience.menu_url" target="_blank" class="ml-2 text-sm text-blue-500 hover:underline">Ver Menú</a>
                    </div>
                  </div>

                  <div v-if="experience.opening_hours" class="mt-4">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Horario de Apertura:</span>
                    <div class="mt-1 text-sm text-gray-800 dark:text-white whitespace-pre-line">{{ experience.opening_hours }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
