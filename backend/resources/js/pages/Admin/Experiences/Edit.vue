<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import TimeSlotEditor from '@/components/TimeSlotEditor.vue';

const props = defineProps<{
  experience: {
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    location_id: number | null;
    agency_id: number | null;
    type: string;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    price: number | null;
    image: string | null;
    start_date: string | null;
    end_date: string | null;
    is_featured: boolean;
    is_active: boolean;
    // Restaurant-specific fields
    address?: string | null;
    phone?: string | null;
    email?: string | null;
    website?: string | null;
    cuisine_type?: string | null;
    opening_hours?: string | null;
    menu_url?: string | null;
  };
  locations: Array<{
    id: number;
    name: string;
  }>;
  agencies: Array<{
    id: number;
    name: string;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Experiencias',
    href: '/dashboard/experiences',
  },
  {
    title: 'Editar',
    href: `/dashboard/experiences/${props.experience.id}/edit`,
  },
];

// Format date to YYYY-MM-DD for HTML date input
const formatDateForInput = (dateString: string | null): string => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  } catch (e) {
    console.error('Error formatting date:', e, dateString);
    return '';
  }
};

const form = useForm({
  title: props.experience.title,
  description: props.experience.description,
  short_description: props.experience.short_description,
  location_id: props.experience.location_id,
  agency_id: props.experience.agency_id,
  type: props.experience.type,
  duration: props.experience.duration,
  distance: props.experience.distance,
  difficulty: props.experience.difficulty,
  price: props.experience.price,
  image: null as File | null,
  _method: 'PUT', // Required for file uploads with PUT method
  start_date: formatDateForInput(props.experience.start_date),
  end_date: formatDateForInput(props.experience.end_date),
  is_featured: props.experience.is_featured,
  is_active: props.experience.is_active,
  // Restaurant-specific fields
  address: props.experience.address || '',
  phone: props.experience.phone || '',
  email: props.experience.email || '',
  website: props.experience.website || '',
  cuisine_type: props.experience.cuisine_type || '',
  opening_hours: props.experience.opening_hours || '',
  menu_url: props.experience.menu_url || '',
  // Time slot and capacity fields
  available_time_slots: props.experience.available_time_slots || [],
  capacity_per_time_slot: props.experience.capacity_per_time_slot || 10,
  max_reservations_per_day: props.experience.max_reservations_per_day || 0,
});

// Current image preview
const currentImage = ref(props.experience.image);

const handleImageChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    form.image = target.files[0];

    // Create a preview URL for the selected image
    const reader = new FileReader();
    reader.onload = (e) => {
      currentImage.value = e.target?.result as string;
    };
    reader.readAsDataURL(target.files[0]);
  }
};

const submit = () => {
  form.post(`/dashboard/experiences/${props.experience.id}`, {
    forceFormData: true,
  });
};

const experienceTypes = [
  { value: 'activity', label: 'Actividad' },
  { value: 'tour', label: 'Tour' },
  { value: 'workshop', label: 'Taller' },
  { value: 'hotel', label: 'Alojamiento' },
  { value: 'restaurant', label: 'Restaurante' },
  { value: 'museum', label: 'Museo' },
  { value: 'park', label: 'Parque' },
];

const difficultyLevels = [
  { value: 'easy', label: 'Fácil' },
  { value: 'moderate', label: 'Moderado' },
  { value: 'hard', label: 'Difícil' },
  { value: 'expert', label: 'Experto' },
];
</script>

<template>
  <Head title="Editar Experiencia" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Editar Experiencia</h1>
        <Link
          href="/dashboard/experiences"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Volver a Experiencias
        </Link>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Título *</label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
            </div>

            <div>
              <label for="short_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción Corta</label>
              <input
                id="short_description"
                v-model="form.short_description"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.short_description" class="text-red-500 text-sm mt-1">{{ form.errors.short_description }}</div>
            </div>

            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción *</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="5"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>

            <div>
              <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo *</label>
              <select
                id="type"
                v-model="form.type"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option v-for="type in experienceTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </option>
              </select>
              <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
            </div>

            <div>
              <label for="location_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ubicación</label>
              <select
                id="location_id"
                v-model="form.location_id"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option :value="null">Seleccionar ubicación</option>
                <option v-for="location in locations" :key="location.id" :value="location.id">
                  {{ location.name }}
                </option>
              </select>
              <div v-if="form.errors.location_id" class="text-red-500 text-sm mt-1">{{ form.errors.location_id }}</div>
            </div>

            <div>
              <label for="agency_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Agencia</label>
              <select
                id="agency_id"
                v-model="form.agency_id"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option :value="null">Seleccionar agencia</option>
                <option v-for="agency in agencies" :key="agency.id" :value="agency.id">
                  {{ agency.name }}
                </option>
              </select>
              <div v-if="form.errors.agency_id" class="text-red-500 text-sm mt-1">{{ form.errors.agency_id }}</div>
            </div>

            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Precio (€)</label>
              <input
                id="price"
                v-model="form.price"
                type="number"
                step="0.01"
                min="0"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.price" class="text-red-500 text-sm mt-1">{{ form.errors.price }}</div>
            </div>

            <div>
              <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagen</label>
              <div v-if="currentImage" class="mb-2">
                <img :src="currentImage" alt="Vista previa" class="h-32 w-auto object-cover rounded-md" />
              </div>
              <input
                id="image"
                type="file"
                accept="image/*"
                @change="handleImageChange"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image }}</div>
              <p class="text-sm text-gray-500 mt-1">Formatos aceptados: JPG, PNG, GIF. Tamaño máximo: 2MB</p>
            </div>

            <div>
              <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Duración</label>
              <input
                id="duration"
                v-model="form.duration"
                type="text"
                placeholder="Ej: 2 horas, 3 días"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.duration" class="text-red-500 text-sm mt-1">{{ form.errors.duration }}</div>
            </div>

            <div>
              <label for="distance" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Distancia</label>
              <input
                id="distance"
                v-model="form.distance"
                type="text"
                placeholder="Ej: 5 km"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.distance" class="text-red-500 text-sm mt-1">{{ form.errors.distance }}</div>
            </div>

            <div>
              <label for="difficulty" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dificultad</label>
              <select
                id="difficulty"
                v-model="form.difficulty"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="">Seleccionar dificultad</option>
                <option v-for="level in difficultyLevels" :key="level.value" :value="level.value">
                  {{ level.label }}
                </option>
              </select>
              <div v-if="form.errors.difficulty" class="text-red-500 text-sm mt-1">{{ form.errors.difficulty }}</div>
            </div>

            <div>
              <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Fecha de inicio</label>
              <input
                id="start_date"
                v-model="form.start_date"
                type="date"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.start_date" class="text-red-500 text-sm mt-1">{{ form.errors.start_date }}</div>
            </div>

            <div>
              <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Fecha de fin</label>
              <input
                id="end_date"
                v-model="form.end_date"
                type="date"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.end_date" class="text-red-500 text-sm mt-1">{{ form.errors.end_date }}</div>
            </div>

            <div class="flex items-center">
              <input
                id="is_featured"
                v-model="form.is_featured"
                type="checkbox"
                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600"
              />
              <label for="is_featured" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">Destacado</label>
              <div v-if="form.errors.is_featured" class="text-red-500 text-sm mt-1">{{ form.errors.is_featured }}</div>
            </div>

            <div class="flex items-center">
              <input
                id="is_active"
                v-model="form.is_active"
                type="checkbox"
                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600"
              />
              <label for="is_active" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">Activo</label>
              <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">{{ form.errors.is_active }}</div>
            </div>

            <!-- Restaurant-specific fields -->
            <template v-if="form.type === 'restaurant'">
              <div class="md:col-span-2 mt-6 mb-2">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Información específica del restaurante</h3>
              </div>

              <div>
                <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dirección</label>
                <input
                  id="address"
                  v-model="form.address"
                  type="text"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <div v-if="form.errors.address" class="text-red-500 text-sm mt-1">{{ form.errors.address }}</div>
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Teléfono</label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="text"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <div v-if="form.errors.phone" class="text-red-500 text-sm mt-1">{{ form.errors.phone }}</div>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</div>
              </div>

              <div>
                <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sitio Web</label>
                <input
                  id="website"
                  v-model="form.website"
                  type="url"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <div v-if="form.errors.website" class="text-red-500 text-sm mt-1">{{ form.errors.website }}</div>
              </div>

              <div>
                <label for="cuisine_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo de Cocina</label>
                <input
                  id="cuisine_type"
                  v-model="form.cuisine_type"
                  type="text"
                  placeholder="Ej: Mediterránea, Tradicional, Fusión..."
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <div v-if="form.errors.cuisine_type" class="text-red-500 text-sm mt-1">{{ form.errors.cuisine_type }}</div>
              </div>

              <div>
                <label for="menu_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">URL del Menú</label>
                <input
                  id="menu_url"
                  v-model="form.menu_url"
                  type="url"
                  placeholder="https://ejemplo.com/menu"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <div v-if="form.errors.menu_url" class="text-red-500 text-sm mt-1">{{ form.errors.menu_url }}</div>
              </div>

              <div class="md:col-span-2">
                <label for="opening_hours" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Horario de Apertura</label>
                <textarea
                  id="opening_hours"
                  v-model="form.opening_hours"
                  rows="3"
                  placeholder="Ej: Lunes a Viernes: 13:00-16:00, 20:00-23:00. Sábados y Domingos: 13:00-23:00"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                ></textarea>
                <div v-if="form.errors.opening_hours" class="text-red-500 text-sm mt-1">{{ form.errors.opening_hours }}</div>
              </div>
            </template>
          </div>

          <!-- Time Slot Editor -->
          <div class="mt-8 p-6 bg-white dark:bg-gray-800 shadow rounded-lg">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Configuración de horarios y capacidad</h3>
            <TimeSlotEditor
              :model-value="{
                available_time_slots: form.available_time_slots,
                capacity_per_time_slot: form.capacity_per_time_slot,
                max_reservations_per_day: form.max_reservations_per_day
              }"
              :experience-type="form.type"
              @update:model-value="(value) => {
                form.available_time_slots = value.available_time_slots;
                form.capacity_per_time_slot = value.capacity_per_time_slot;
                form.max_reservations_per_day = value.max_reservations_per_day;
              }"
            />
          </div>

          <div class="flex justify-end mt-6">
            <button
              type="button"
              class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 mr-2"
              @click="$inertia.visit('/dashboard/experiences')"
            >
              Cancelar
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              :disabled="form.processing"
            >
              {{ form.processing ? 'Guardando...' : 'Actualizar Experiencia' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
