<template>
  <Head title="Editar Reserva" />

  <AppLayout>
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Editar Reserva #{{ reservation.id }}</h1>
        <Link
          :href="route('admin.reservations.index')"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Volver
        </Link>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <form @submit.prevent="submit">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- User -->
            <div>
              <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Usuario <span class="text-red-500">*</span></label>
              <select
                id="user_id"
                v-model="form.user_id"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="">Seleccionar usuario</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.name }} ({{ user.email }})
                </option>
              </select>
              <div v-if="form.errors.user_id" class="text-red-500 text-sm mt-1">{{ form.errors.user_id }}</div>
            </div>

            <!-- Experience -->
            <div>
              <label for="experience_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Experiencia <span class="text-red-500">*</span></label>
              <select
                id="experience_id"
                v-model="form.experience_id"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                @change="handleExperienceChange"
              >
                <option value="">Seleccionar experiencia</option>
                <option v-for="experience in experiences" :key="experience.id" :value="experience.id">
                  {{ experience.title }}
                </option>
              </select>
              <div v-if="form.errors.experience_id" class="text-red-500 text-sm mt-1">{{ form.errors.experience_id }}</div>
            </div>

            <!-- Reservation Date -->
            <div>
              <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Fecha de Reserva <span class="text-red-500">*</span></label>
              <input
                type="date"
                id="reservation_date"
                v-model="form.reservation_date"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.reservation_date" class="text-red-500 text-sm mt-1">{{ form.errors.reservation_date }}</div>
            </div>

            <!-- Reservation Time -->
            <div>
              <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Hora de Reserva
                <span v-if="isTimeRequired" class="text-red-500">*</span>
              </label>
              <input
                type="time"
                id="reservation_time"
                v-model="form.reservation_time"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.reservation_time" class="text-red-500 text-sm mt-1">{{ form.errors.reservation_time }}</div>
            </div>

            <!-- Number of People -->
            <div>
              <label for="num_people" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Número de Personas <span class="text-red-500">*</span></label>
              <input
                type="number"
                id="num_people"
                v-model="form.num_people"
                min="1"
                max="50"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.num_people" class="text-red-500 text-sm mt-1">{{ form.errors.num_people }}</div>
            </div>

            <!-- Status -->
            <div>
              <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Estado <span class="text-red-500">*</span></label>
              <select
                id="status"
                v-model="form.status"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option v-for="(label, value) in statuses" :key="value" :value="value">
                  {{ label }}
                </option>
              </select>
              <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">{{ form.errors.status }}</div>
            </div>

            <!-- Special Requests -->
            <div class="md:col-span-2">
              <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Solicitudes Especiales</label>
              <textarea
                id="special_requests"
                v-model="form.special_requests"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              ></textarea>
              <div v-if="form.errors.special_requests" class="text-red-500 text-sm mt-1">{{ form.errors.special_requests }}</div>
            </div>

            <!-- Admin Notes -->
            <div class="md:col-span-2">
              <label for="admin_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notas Administrativas</label>
              <textarea
                id="admin_notes"
                v-model="form.admin_notes"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              ></textarea>
              <div v-if="form.errors.admin_notes" class="text-red-500 text-sm mt-1">{{ form.errors.admin_notes }}</div>
            </div>
          </div>

          <div class="flex justify-end mt-6">
            <button
              type="submit"
              class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
              :disabled="form.processing"
            >
              Actualizar Reserva
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';

// Define props
const props = defineProps({
  reservation: Object,
  experiences: Array,
  users: Array,
  statuses: Object,
});

// Format date for input
const formatDateForInput = (dateString) => {
  const date = new Date(dateString);
  return date.toISOString().split('T')[0];
};

// Form
const form = useForm({
  user_id: props.reservation.user_id,
  experience_id: props.reservation.experience_id,
  reservation_date: formatDateForInput(props.reservation.reservation_date),
  reservation_time: props.reservation.reservation_time || '',
  num_people: props.reservation.num_people,
  special_requests: props.reservation.special_requests || '',
  status: props.reservation.status,
  admin_notes: props.reservation.admin_notes || '',
});

// Selected experience type
const selectedExperienceType = ref(props.reservation.experience.type);

// Check if time is required based on experience type
const isTimeRequired = computed(() => {
  return ['restaurant', 'tour'].includes(selectedExperienceType.value);
});

// Handle experience change
const handleExperienceChange = () => {
  const experienceId = form.experience_id;
  if (experienceId) {
    const experience = props.experiences.find(exp => exp.id === parseInt(experienceId));
    if (experience) {
      selectedExperienceType.value = experience.type;
    } else {
      selectedExperienceType.value = '';
    }
  } else {
    selectedExperienceType.value = '';
  }
};

// Submit form
const submit = () => {
  form.put(route('admin.reservations.update', props.reservation.id));
};
</script>
