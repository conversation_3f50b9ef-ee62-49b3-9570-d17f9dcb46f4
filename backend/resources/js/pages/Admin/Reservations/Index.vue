<template>
  <Head title="Reservas" />

  <AppLayout>
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Reservas</h1>
        <Link
          :href="route('admin.reservations.create')"
          class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
        >
          Nueva Reserva
        </Link>
      </div>

      <!-- Filters -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-4 mb-4">
        <form @submit.prevent="applyFilters" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Estado</label>
            <select
              id="status"
              v-model="filters.status"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">Todos</option>
              <option value="pending">Pendiente</option>
              <option value="confirmed">Confirmada</option>
              <option value="cancelled">Cancelada</option>
              <option value="completed">Completada</option>
            </select>
          </div>

          <div>
            <label for="experience_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Experiencia</label>
            <select
              id="experience_id"
              v-model="filters.experience_id"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="">Todas</option>
              <option v-for="experience in experiences" :key="experience.id" :value="experience.id">
                {{ experience.title }}
              </option>
            </select>
          </div>

          <div v-if="isSuperAdmin">
            <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Usuario</label>
            <select
              id="user_id"
              v-model="filters.user_id"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="">Todos</option>
              <option v-for="user in users" :key="user.id" :value="user.id">
                {{ user.name }} ({{ user.email }})
              </option>
            </select>
          </div>

          <div>
            <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Fecha desde</label>
            <input
              type="date"
              id="date_from"
              v-model="filters.date_from"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Fecha hasta</label>
            <input
              type="date"
              id="date_to"
              v-model="filters.date_to"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div class="flex items-end">
            <button
              type="submit"
              class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark mr-2"
            >
              Filtrar
            </button>
            <button
              type="button"
              class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500"
              @click="resetFilters"
            >
              Limpiar
            </button>
          </div>
        </form>
      </div>

      <!-- Reservations Table -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Usuario</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Experiencia</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Fecha</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Personas</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Estado</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Acciones</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-if="reservations.data.length === 0">
                <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No hay reservas que coincidan con los filtros.
                </td>
              </tr>
              <tr v-for="reservation in reservations.data" :key="reservation.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ reservation.id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ reservation.user.name }}<br>
                  <span class="text-xs text-gray-500 dark:text-gray-400">{{ reservation.user.email }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ reservation.experience.title }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ formatDate(reservation.reservation_date) }}<br>
                  <span v-if="reservation.reservation_time" class="text-xs text-gray-500 dark:text-gray-400">
                    {{ reservation.reservation_time }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ reservation.num_people }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <span :class="getStatusClass(reservation.status)" class="px-2 py-1 rounded-full text-xs">
                    {{ getStatusText(reservation.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  <div class="flex space-x-2">
                    <Link :href="route('admin.reservations.show', reservation.id)" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                      Ver
                    </Link>
                    <Link :href="route('admin.reservations.edit', reservation.id)" class="text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-300">
                      Editar
                    </Link>
                    <button @click="confirmDelete(reservation)" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                      Eliminar
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <Pagination :links="reservations.links" />
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <Modal :show="showDeleteModal" @close="showDeleteModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Confirmar eliminación</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          ¿Estás seguro de que deseas eliminar esta reserva? Esta acción no se puede deshacer.
        </p>
        <div class="mt-6 flex justify-end space-x-2">
          <button
            type="button"
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500"
            @click="showDeleteModal = false"
          >
            Cancelar
          </button>
          <button
            type="button"
            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            @click="deleteReservation"
          >
            Eliminar
          </button>
        </div>
      </div>
    </Modal>
  </AppLayout>
</template>

<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import Pagination from '@/components/Pagination.vue';
import Modal from '@/components/Modal.vue';
import { ref, computed } from 'vue';

// Define props
const props = defineProps({
  reservations: Object,
  experiences: Array,
  users: Array,
  filters: Object,
  statuses: Object,
});

// Filters
const filters = ref({
  status: props.filters?.status || 'all',
  date_from: props.filters?.date_from || '',
  date_to: props.filters?.date_to || '',
  experience_id: props.filters?.experience_id || '',
  user_id: props.filters?.user_id || '',
});

// Check if user is superadmin
const isSuperAdmin = computed(() => {
  return window.Laravel?.user?.role === 'superadmin';
});

// Delete confirmation
const showDeleteModal = ref(false);
const reservationToDelete = ref(null);

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// Get status text
const getStatusText = (status) => {
  return props.statuses[status] || status;
};

// Get status class
const getStatusClass = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'confirmed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'completed':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

// Apply filters
const applyFilters = () => {
  router.get(route('admin.reservations.index'), {
    status: filters.value.status,
    date_from: filters.value.date_from,
    date_to: filters.value.date_to,
    experience_id: filters.value.experience_id,
    user_id: filters.value.user_id,
  }, {
    preserveState: true,
    replace: true,
  });
};

// Reset filters
const resetFilters = () => {
  filters.value = {
    status: 'all',
    date_from: '',
    date_to: '',
    experience_id: '',
    user_id: '',
  };
  applyFilters();
};

// Confirm delete
const confirmDelete = (reservation) => {
  reservationToDelete.value = reservation;
  showDeleteModal.value = true;
};

// Delete reservation
const deleteReservation = () => {
  if (reservationToDelete.value) {
    // Use post method with _method=DELETE instead of direct delete
    router.post(route('admin.reservations.destroy', reservationToDelete.value.id), {
      _method: 'DELETE'
    }, {
      onSuccess: () => {
        showDeleteModal.value = false;
        reservationToDelete.value = null;
      },
      onError: (errors) => {
        console.error('Error deleting reservation:', errors);
      }
    });
  }
};
</script>
