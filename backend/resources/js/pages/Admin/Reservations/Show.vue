<template>
  <Head title="Ver Reserva" />

  <AppLayout>
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Reserva #{{ reservation.id }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="route('admin.reservations.edit', reservation.id)"
            class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Editar
          </Link>
          <Link
            :href="route('admin.reservations.index')"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Información de la Reserva</h2>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Estado</p>
              <p class="mt-1">
                <span v-if="reservation && reservation.status" :class="getStatusClass(reservation.status)" class="px-2 py-1 rounded-full text-xs">
                  {{ getStatusText(reservation.status) }}
                </span>
                <span v-else class="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                  Desconocido
                </span>
              </p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Fecha de Reserva</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">
                {{ reservation && reservation.reservation_date ? formatDate(reservation.reservation_date) : 'No disponible' }}
              </p>
            </div>

            <div v-if="reservation && reservation.reservation_time" class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Hora de Reserva</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation.reservation_time }}</p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Número de Personas</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation && reservation.num_people ? reservation.num_people : 'No disponible' }}</p>
            </div>

            <div v-if="reservation && reservation.special_requests" class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Solicitudes Especiales</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation.special_requests }}</p>
            </div>

            <div v-if="reservation && reservation.admin_notes" class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Notas Administrativas</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation.admin_notes }}</p>
            </div>
          </div>

          <div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Información del Usuario</h2>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Nombre</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation && reservation.user ? reservation.user.name : 'No disponible' }}</p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation && reservation.user ? reservation.user.email : 'No disponible' }}</p>
            </div>

            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mt-6 mb-4">Información de la Experiencia</h2>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Título</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation && reservation.experience ? reservation.experience.title : 'No disponible' }}</p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Tipo</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation && reservation.experience ? getExperienceTypeText(reservation.experience.type) : 'No disponible' }}</p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Agencia</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ reservation && reservation.experience && reservation.experience.agency ? reservation.experience.agency.name : 'No disponible' }}</p>
            </div>
          </div>
        </div>

        <div class="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Acciones</h2>

          <div class="flex space-x-2">
            <button
              v-if="reservation && reservation.status && ['pending', 'confirmed'].includes(reservation.status)"
              @click="updateStatus('completed')"
              class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Marcar como Completada
            </button>

            <button
              v-if="reservation && reservation.status === 'pending'"
              @click="updateStatus('confirmed')"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Confirmar Reserva
            </button>

            <button
              v-if="reservation && reservation.status && ['pending', 'confirmed'].includes(reservation.status)"
              @click="updateStatus('cancelled')"
              class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Cancelar Reserva
            </button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';

// Define props
const props = defineProps({
  reservation: Object,
  statuses: Object,
});

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// Get status text
const getStatusText = (status) => {
  if (!status) {
    return 'Desconocido';
  }
  return props.statuses[status] || status;
};

// Get status class
const getStatusClass = (status) => {
  // If status is undefined or null, return default class
  if (!status) {
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }

  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'confirmed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'completed':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

// Get experience type text
const getExperienceTypeText = (type) => {
  if (!type) {
    return 'Desconocido';
  }

  const types = {
    'activity': 'Actividad',
    'tour': 'Tour',
    'workshop': 'Taller',
    'hotel': 'Alojamiento',
    'restaurant': 'Restaurante',
    'museum': 'Museo',
    'park': 'Parque',
  };

  return types[type] || type;
};

// Update status
const updateStatus = (status) => {
  if (!props.reservation || !props.reservation.id) {
    console.error('Cannot update status: Reservation ID is missing');
    return;
  }

  router.post(route('admin.reservations.update-status', props.reservation.id), {
    status: status,
  });
};
</script>
