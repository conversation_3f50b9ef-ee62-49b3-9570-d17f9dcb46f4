<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { MapPin, Video, FileAudio, Box, Star, Eye, EyeOff, Calendar, Clock } from 'lucide-vue-next';

const props = defineProps<{
  storyTour: {
    id: number;
    title: string;
    description: string;
    image: string | null;
    type: string;
    audio_file: string | null;
    video_file: string | null;
    ar_model_file: string | null;
    location: {
      id: number;
      name: string;
      latitude: number;
      longitude: number;
    } | null;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'StoryTours',
    href: '/dashboard/story-tours',
  },
  {
    title: props.storyTour.title,
    href: `/dashboard/story-tours/${props.storyTour.id}`,
  },
];

const getTypeLabel = (type: string) => {
  const types = {
    'paisaje': 'Paisaje',
    'patrimonio': 'Patrimonio',
    'cultura': 'Cultura',
    'gastronomia': 'Gastronomía',
    'naturaleza': 'Naturaleza'
  };
  
  return types[type as keyof typeof types] || type;
};

const getTypeColor = (type: string) => {
  const colors = {
    'paisaje': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
    'patrimonio': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
    'cultura': 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100',
    'gastronomia': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
    'naturaleza': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-800 dark:text-emerald-100'
  };
  
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<template>
  <Head :title="storyTour.title" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">{{ storyTour.title }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/story-tours/${storyTour.id}/edit`"
            class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Editar
          </Link>
          <Link
            href="/dashboard/story-tours"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Volver
          </Link>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Left Column - Image and Basic Info -->
        <div class="md:col-span-1">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div class="relative">
              <img
                v-if="storyTour.image"
                :src="storyTour.image"
                :alt="storyTour.title"
                class="w-full h-64 object-cover"
              />
              <div
                v-else
                class="w-full h-64 bg-gray-200 dark:bg-gray-700 flex items-center justify-center"
              >
                <Box class="h-16 w-16 text-gray-400" />
              </div>
              
              <!-- Status Badges -->
              <div class="absolute top-2 right-2 flex flex-col space-y-2">
                <span
                  class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="storyTour.is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'"
                >
                  <span class="flex items-center">
                    <span v-if="storyTour.is_active" class="mr-1"><Eye class="h-3 w-3" /></span>
                    <span v-else class="mr-1"><EyeOff class="h-3 w-3" /></span>
                    {{ storyTour.is_active ? 'Activo' : 'Inactivo' }}
                  </span>
                </span>
                <span
                  v-if="storyTour.is_featured"
                  class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                >
                  <span class="flex items-center">
                    <Star class="h-3 w-3 mr-1" />
                    Destacado
                  </span>
                </span>
              </div>
            </div>
            
            <div class="p-4">
              <div class="mb-4">
                <span
                  class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getTypeColor(storyTour.type)"
                >
                  {{ getTypeLabel(storyTour.type) }}
                </span>
              </div>
              
              <div v-if="storyTour.location" class="mb-4">
                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Ubicación</h3>
                <div class="flex items-center text-gray-800 dark:text-gray-200">
                  <MapPin class="h-4 w-4 mr-1 text-primary" />
                  {{ storyTour.location.name }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Lat: {{ storyTour.location.latitude }}, Lng: {{ storyTour.location.longitude }}
                </div>
              </div>
              
              <div class="mb-4">
                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Fechas</h3>
                <div class="flex items-center text-gray-800 dark:text-gray-200 text-sm">
                  <Calendar class="h-4 w-4 mr-1 text-primary" />
                  <span>Creado: {{ formatDate(storyTour.created_at) }}</span>
                </div>
                <div class="flex items-center text-gray-800 dark:text-gray-200 text-sm mt-1">
                  <Clock class="h-4 w-4 mr-1 text-primary" />
                  <span>Actualizado: {{ formatDate(storyTour.updated_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Middle Column - Description and Files -->
        <div class="md:col-span-2">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Descripción</h2>
            <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">{{ storyTour.description }}</p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Archivos Multimedia</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- Audio File -->
              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex items-center mb-2">
                  <FileAudio class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                  <h3 class="font-medium text-gray-800 dark:text-white">Audio</h3>
                </div>
                <div v-if="storyTour.audio_file" class="mt-2">
                  <audio controls class="w-full">
                    <source :src="storyTour.audio_file" type="audio/mpeg">
                    Tu navegador no soporta el elemento de audio.
                  </audio>
                </div>
                <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
                  No hay archivo de audio
                </div>
              </div>
              
              <!-- Video File -->
              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex items-center mb-2">
                  <Video class="h-5 w-5 text-purple-600 dark:text-purple-400 mr-2" />
                  <h3 class="font-medium text-gray-800 dark:text-white">Video</h3>
                </div>
                <div v-if="storyTour.video_file" class="mt-2">
                  <a 
                    :href="storyTour.video_file" 
                    target="_blank" 
                    class="text-primary hover:underline"
                  >
                    Ver video
                  </a>
                </div>
                <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
                  No hay archivo de video
                </div>
              </div>
              
              <!-- AR Model File -->
              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex items-center mb-2">
                  <Box class="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                  <h3 class="font-medium text-gray-800 dark:text-white">Modelo AR</h3>
                </div>
                <div v-if="storyTour.ar_model_file" class="mt-2">
                  <a 
                    :href="storyTour.ar_model_file" 
                    target="_blank" 
                    class="text-primary hover:underline"
                  >
                    Ver modelo 3D
                  </a>
                </div>
                <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
                  No hay archivo de modelo AR
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
