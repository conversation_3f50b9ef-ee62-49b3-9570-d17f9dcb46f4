<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { MapPin, Video, FileAudio, Box, Star, Eye, EyeOff } from 'lucide-vue-next';

const props = defineProps<{
  storyTours: Array<{
    id: number;
    title: string;
    description: string;
    image: string | null;
    type: string;
    audio_file: string | null;
    video_file: string | null;
    ar_model_file: string | null;
    location: {
      id: number;
      name: string;
    } | null;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'StoryTours',
    href: '/dashboard/story-tours',
  },
];

const searchQuery = ref('');
const typeFilter = ref('all');

const filteredStoryTours = computed(() => {
  let filtered = props.storyTours;
  
  // Apply type filter
  if (typeFilter.value !== 'all') {
    filtered = filtered.filter(item => item.type === typeFilter.value);
  }
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(item => 
      item.title.toLowerCase().includes(query) || 
      item.description.toLowerCase().includes(query) ||
      (item.location && item.location.name.toLowerCase().includes(query))
    );
  }
  
  return filtered;
});

const getTypeLabel = (type: string) => {
  const types = {
    'paisaje': 'Paisaje',
    'patrimonio': 'Patrimonio',
    'cultura': 'Cultura',
    'gastronomia': 'Gastronomía',
    'naturaleza': 'Naturaleza'
  };
  
  return types[type as keyof typeof types] || type;
};

const getTypeColor = (type: string) => {
  const colors = {
    'paisaje': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
    'patrimonio': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
    'cultura': 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100',
    'gastronomia': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
    'naturaleza': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-800 dark:text-emerald-100'
  };
  
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
};

const confirmDelete = (id: number) => {
  if (confirm('¿Estás seguro de que quieres eliminar este StoryTour?')) {
    window.location.href = `/dashboard/story-tours/${id}/delete`;
  }
};
</script>

<template>
  <Head title="StoryTours" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">StoryTours</h1>
        <Link
          href="/dashboard/story-tours/create"
          class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
        >
          Crear StoryTour
        </Link>
      </div>

      <!-- Filters -->
      <div class="flex flex-col md:flex-row gap-4 mb-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Buscar StoryTours..."
            class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div class="w-full md:w-64">
          <select
            v-model="typeFilter"
            class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="all">Todos los tipos</option>
            <option value="paisaje">Paisaje</option>
            <option value="patrimonio">Patrimonio</option>
            <option value="cultura">Cultura</option>
            <option value="gastronomia">Gastronomía</option>
            <option value="naturaleza">Naturaleza</option>
          </select>
        </div>
      </div>

      <!-- StoryTours Table -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Título
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tipo
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ubicación
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Archivos
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Estado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="item in filteredStoryTours" :key="item.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img
                        v-if="item.image"
                        :src="item.image"
                        :alt="item.title"
                        class="h-10 w-10 rounded-full object-cover"
                      />
                      <div
                        v-else
                        class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center"
                      >
                        <Box class="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ item.title }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ item.description.length > 50 ? item.description.substring(0, 50) + '...' : item.description }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="getTypeColor(item.type)"
                  >
                    {{ getTypeLabel(item.type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <div v-if="item.location" class="flex items-center">
                    <MapPin class="h-4 w-4 mr-1" />
                    {{ item.location.name }}
                  </div>
                  <div v-else>No especificada</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex space-x-2">
                    <div v-if="item.audio_file" class="text-blue-600 dark:text-blue-400">
                      <FileAudio class="h-4 w-4" />
                    </div>
                    <div v-if="item.video_file" class="text-purple-600 dark:text-purple-400">
                      <Video class="h-4 w-4" />
                    </div>
                    <div v-if="item.ar_model_file" class="text-green-600 dark:text-green-400">
                      <Box class="h-4 w-4" />
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col space-y-1">
                    <span
                      class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                      :class="item.is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'"
                    >
                      <span class="flex items-center">
                        <span v-if="item.is_active" class="mr-1"><Eye class="h-3 w-3" /></span>
                        <span v-else class="mr-1"><EyeOff class="h-3 w-3" /></span>
                        {{ item.is_active ? 'Activo' : 'Inactivo' }}
                      </span>
                    </span>
                    <span
                      v-if="item.is_featured"
                      class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                    >
                      <span class="flex items-center">
                        <Star class="h-3 w-3 mr-1" />
                        Destacado
                      </span>
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <Link :href="`/dashboard/story-tours/${item.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                      Ver
                    </Link>
                    <Link :href="`/dashboard/story-tours/${item.id}/edit`" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                      Editar
                    </Link>
                    <button @click="confirmDelete(item.id)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      Eliminar
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredStoryTours.length === 0">
                <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No se encontraron StoryTours
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
