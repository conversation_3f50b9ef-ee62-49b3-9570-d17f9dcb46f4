<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  locations: Array<{
    id: number;
    name: string;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'StoryTours',
    href: '/dashboard/story-tours',
  },
  {
    title: 'Crear',
    href: '/dashboard/story-tours/create',
  },
];

const form = useForm({
  title: '',
  description: '',
  image: null as File | null,
  type: 'paisaje',
  audio_file: null as File | null,
  video_file: null as File | null,
  ar_model_file: null as File | null,
  location_id: '' as string | number,
  is_featured: false,
  is_active: true,
});

const imagePreview = ref<string | null>(null);
const audioFileName = ref<string | null>(null);
const videoFileName = ref<string | null>(null);
const arModelFileName = ref<string | null>(null);

const handleImageChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    form.image = input.files[0];
    
    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(input.files[0]);
  }
};

const handleAudioChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    form.audio_file = input.files[0];
    audioFileName.value = input.files[0].name;
  }
};

const handleVideoChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    form.video_file = input.files[0];
    videoFileName.value = input.files[0].name;
  }
};

const handleArModelChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    form.ar_model_file = input.files[0];
    arModelFileName.value = input.files[0].name;
  }
};

const submit = () => {
  form.post('/dashboard/story-tours', {
    preserveScroll: true,
    onSuccess: () => {
      form.reset();
      imagePreview.value = null;
      audioFileName.value = null;
      videoFileName.value = null;
      arModelFileName.value = null;
    },
  });
};
</script>

<template>
  <Head title="Crear StoryTour" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">Crear StoryTour</h1>
      </div>

      <form @submit.prevent="submit" class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Left Column -->
          <div class="space-y-6">
            <!-- Title -->
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Título</label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
              />
              <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
            </div>

            <!-- Type -->
            <div>
              <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo</label>
              <select
                id="type"
                v-model="form.type"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
              >
                <option value="paisaje">Paisaje</option>
                <option value="patrimonio">Patrimonio</option>
                <option value="cultura">Cultura</option>
                <option value="gastronomia">Gastronomía</option>
                <option value="naturaleza">Naturaleza</option>
              </select>
              <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
            </div>

            <!-- Location -->
            <div>
              <label for="location_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ubicación</label>
              <select
                id="location_id"
                v-model="form.location_id"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="">Seleccionar ubicación</option>
                <option v-for="location in locations" :key="location.id" :value="location.id">{{ location.name }}</option>
              </select>
              <div v-if="form.errors.location_id" class="text-red-500 text-sm mt-1">{{ form.errors.location_id }}</div>
            </div>

            <!-- Image -->
            <div>
              <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagen</label>
              <div class="mt-1 flex items-center">
                <div v-if="imagePreview" class="mr-4">
                  <img :src="imagePreview" alt="Preview" class="h-24 w-24 object-cover rounded-md" />
                </div>
                <label class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600">
                  <span>Seleccionar imagen</span>
                  <input
                    id="image"
                    type="file"
                    class="sr-only"
                    accept="image/*"
                    @change="handleImageChange"
                  />
                </label>
              </div>
              <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image }}</div>
            </div>

            <!-- Status Toggles -->
            <div class="flex flex-col space-y-4">
              <div class="flex items-center">
                <input
                  id="is_featured"
                  v-model="form.is_featured"
                  type="checkbox"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
                />
                <label for="is_featured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Destacado</label>
              </div>
              <div class="flex items-center">
                <input
                  id="is_active"
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
                />
                <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Activo</label>
              </div>
            </div>
          </div>

          <!-- Right Column -->
          <div class="space-y-6">
            <!-- Description -->
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="5"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>

            <!-- Audio File -->
            <div>
              <label for="audio_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Archivo de Audio</label>
              <div class="mt-1 flex items-center">
                <label class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600">
                  <span>Seleccionar audio</span>
                  <input
                    id="audio_file"
                    type="file"
                    class="sr-only"
                    accept="audio/*"
                    @change="handleAudioChange"
                  />
                </label>
                <span v-if="audioFileName" class="ml-3 text-sm text-gray-500 dark:text-gray-400">{{ audioFileName }}</span>
              </div>
              <div v-if="form.errors.audio_file" class="text-red-500 text-sm mt-1">{{ form.errors.audio_file }}</div>
            </div>

            <!-- Video File -->
            <div>
              <label for="video_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Archivo de Video</label>
              <div class="mt-1 flex items-center">
                <label class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600">
                  <span>Seleccionar video</span>
                  <input
                    id="video_file"
                    type="file"
                    class="sr-only"
                    accept="video/*"
                    @change="handleVideoChange"
                  />
                </label>
                <span v-if="videoFileName" class="ml-3 text-sm text-gray-500 dark:text-gray-400">{{ videoFileName }}</span>
              </div>
              <div v-if="form.errors.video_file" class="text-red-500 text-sm mt-1">{{ form.errors.video_file }}</div>
            </div>

            <!-- AR Model File -->
            <div>
              <label for="ar_model_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Archivo de Modelo AR</label>
              <div class="mt-1 flex items-center">
                <label class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600">
                  <span>Seleccionar modelo</span>
                  <input
                    id="ar_model_file"
                    type="file"
                    class="sr-only"
                    accept=".glb,.gltf"
                    @change="handleArModelChange"
                  />
                </label>
                <span v-if="arModelFileName" class="ml-3 text-sm text-gray-500 dark:text-gray-400">{{ arModelFileName }}</span>
              </div>
              <div v-if="form.errors.ar_model_file" class="text-red-500 text-sm mt-1">{{ form.errors.ar_model_file }}</div>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-8 flex justify-end">
          <button
            type="submit"
            class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
            :disabled="form.processing"
          >
            {{ form.processing ? 'Guardando...' : 'Guardar StoryTour' }}
          </button>
        </div>
      </form>
    </div>
  </AppLayout>
</template>
