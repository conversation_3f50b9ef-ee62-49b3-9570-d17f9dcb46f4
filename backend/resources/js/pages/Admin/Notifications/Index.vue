<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { Bell, Calendar, CheckCircle, AlertCircle, Info, Trash2 } from 'lucide-vue-next';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';

interface Notification {
  id: string;
  type: string;
  data: any;
  read_at: string | null;
  created_at: string;
}

interface PaginationLinks {
  first: string | null;
  last: string | null;
  prev: string | null;
  next: string | null;
}

interface PaginationMeta {
  current_page: number;
  from: number;
  last_page: number;
  links: Array<{url: string | null; label: string; active: boolean}>;
  path: string;
  per_page: number;
  to: number;
  total: number;
}

interface NotificationsPagination {
  data: Array<Notification>;
  links: PaginationLinks;
  meta: PaginationMeta;
}

const props = defineProps<{
  notifications: NotificationsPagination;
}>();

onMounted(() => {
  console.log('Notifications page mounted');
  console.log('Notifications data:', props.notifications);
});

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Notificaciones',
    href: '/dashboard/notifications',
  },
];

// Helper function to parse notification data
const getNotificationData = (notification: any) => {
  if (!notification.data) {
    return { type: 'unknown' };
  }

  if (typeof notification.data === 'string') {
    try {
      return JSON.parse(notification.data);
    } catch (e) {
      console.error('Failed to parse notification data:', e);
      return { type: 'unknown' };
    }
  }

  return notification.data;
};

const getNotificationIcon = (notification: any) => {
  try {
    const data = getNotificationData(notification);
    const type = data.type;

    console.log('Notification type for icon:', type);

    switch (type) {
      case 'reservation_created':
      case 'reservation_updated':
        return Calendar;
      case 'new_agency_user':
        return Bell;
      case 'system':
        if (data.importance === 'success') {
          return CheckCircle;
        } else if (data.importance === 'error' || data.importance === 'warning') {
          return AlertCircle;
        } else {
          return Info;
        }
      default:
        return Bell;
    }
  } catch (error) {
    console.error('Error getting notification icon:', error);
    return Bell;
  }
};

const getNotificationTitle = (notification: any) => {
  try {
    const data = getNotificationData(notification);
    const type = data.type;

    console.log('Notification type for title:', type);

    switch (type) {
      case 'reservation_created':
        return `Nueva reserva: ${data.experience_title || 'Sin título'}`;
      case 'reservation_updated':
        return `Reserva actualizada: ${data.experience_title || 'Sin título'}`;
      case 'new_agency_user':
        return `Nuevo usuario: ${data.user_name || 'Sin nombre'}`;
      case 'system':
        return data.title || 'Notificación del sistema';
      default:
        return 'Notificación';
    }
  } catch (error) {
    console.error('Error getting notification title:', error);
    return 'Notificación';
  }
};

const getNotificationDescription = (notification: any) => {
  try {
    const data = getNotificationData(notification);
    const type = data.type;

    console.log('Notification type for description:', type);

    switch (type) {
      case 'reservation_created':
        return `${data.user_name || 'Usuario'} ha reservado para ${data.num_people || '?'} personas el ${data.reservation_date || 'fecha no especificada'}`;
      case 'reservation_updated':
        return `Estado: ${data.status || 'No especificado'}`;
      case 'new_agency_user':
        return `${data.user_email || 'Usuario'} se ha unido a ${data.agency_name || 'la agencia'}`;
      case 'system':
        return data.message || '';
      default:
        return '';
    }
  } catch (error) {
    console.error('Error getting notification description:', error);
    return '';
  }
};

const getNotificationUrl = (notification: any) => {
  try {
    const data = getNotificationData(notification);
    const type = data.type;

    console.log('Notification type for URL:', type);

    switch (type) {
      case 'reservation_created':
      case 'reservation_updated':
        return data.reservation_id ? `/dashboard/reservations/${data.reservation_id}` : null;
      case 'new_agency_user':
        return '/dashboard/agency-user-management';
      case 'system':
        return data.action_url || null;
      default:
        return null;
    }
  } catch (error) {
    console.error('Error getting notification URL:', error);
    return null;
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return formatDistanceToNow(date, { addSuffix: true, locale: es });
};

const markAllAsRead = () => {
  router.post('/dashboard/notifications/mark-all-as-read', {}, {
    preserveScroll: true,
  });
};

// State for delete confirmation dialog
const showDeleteConfirmation = ref(false);

const deleteAllNotifications = () => {
  // Show the custom confirmation dialog instead of the browser's confirm
  showDeleteConfirmation.value = true;
};

const confirmDeleteAll = () => {
  // Close the dialog
  showDeleteConfirmation.value = false;

  // Perform the delete action
  router.delete('/dashboard/notifications', {
    preserveScroll: true,
  });
};

const cancelDeleteAll = () => {
  // Just close the dialog without taking action
  showDeleteConfirmation.value = false;
};

const deleteNotification = (id: string) => {
  router.delete(`/dashboard/notifications/${id}`, {
    preserveScroll: true,
  });
};

const markAsRead = (id: string) => {
  router.post(`/dashboard/notifications/${id}/mark-as-read`, {}, {
    preserveScroll: true,
  });
};

const navigateTo = (notification: any) => {
  markAsRead(notification.id);

  const url = getNotificationUrl(notification);
  if (url) {
    router.visit(url);
  }
};

const hasUnreadNotifications = computed(() => {
  try {
    return props.notifications.data.some(notification => !notification.read_at);
  } catch (error) {
    console.error('Error checking for unread notifications:', error);
    return false;
  }
});
</script>

<template>
  <Head title="Notificaciones" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <template #header>
      <h2 class="text-xl font-semibold leading-tight text-gray-800">Notificaciones</h2>
    </template>

    <div class="py-6">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white dark:bg-gray-800 shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-medium dark:text-white">Todas las notificaciones</h3>
              <div class="flex gap-2">
                <button
                  v-if="hasUnreadNotifications"
                  @click="markAllAsRead"
                  class="px-3 py-1 text-sm text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-blue-900/30 transition-colors"
                >
                  Marcar todas como leídas
                </button>
                <button
                  v-if="notifications.data.length > 0"
                  @click="deleteAllNotifications"
                  class="px-3 py-1 text-sm text-red-600 border border-red-600 rounded-md hover:bg-red-50 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-900/30 transition-colors"
                >
                  Eliminar todas
                </button>
              </div>
            </div>

            <div v-if="notifications.data.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
              No tienes notificaciones
            </div>

            <div v-else class="border rounded-md divide-y dark:border-gray-700 dark:divide-gray-700">
              <div
                v-for="notification in notifications.data"
                :key="notification.id"
                class="flex items-start p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
              >
                <div
                  :class="[
                    'flex-shrink-0 rounded-full p-2 mr-3',
                    notification.read_at ? 'bg-gray-100 dark:bg-gray-700' : 'bg-blue-100 dark:bg-blue-900/50'
                  ]"
                >
                  <component
                    :is="getNotificationIcon(notification)"
                    class="h-5 w-5"
                    :class="notification.read_at ? 'text-gray-500 dark:text-gray-400' : 'text-blue-500 dark:text-blue-400'"
                  />
                </div>

                <div class="flex-1 min-w-0 cursor-pointer" @click="navigateTo(notification)">
                  <div class="flex justify-between">
                    <h4
                      :class="[
                        'text-sm font-medium',
                        notification.read_at ? 'text-gray-700 dark:text-gray-300' : 'text-gray-900 dark:text-white'
                      ]"
                    >
                      {{ getNotificationTitle(notification) }}
                    </h4>
                    <span class="text-xs text-gray-400 dark:text-gray-500">
                      {{ formatDate(notification.created_at) }}
                    </span>
                  </div>
                  <p
                    :class="[
                      'text-sm mt-1',
                      notification.read_at ? 'text-gray-500 dark:text-gray-400' : 'text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    {{ getNotificationDescription(notification) }}
                  </p>
                </div>

                <div class="flex-shrink-0 ml-4">
                  <button
                    @click.stop="deleteNotification(notification.id)"
                    class="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors"
                    title="Eliminar notificación"
                  >
                    <Trash2 class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            <!-- Pagination -->
            <div v-if="notifications.meta && notifications.meta.last_page > 1" class="mt-6">
              <nav class="flex items-center justify-between">
                <div class="flex justify-between flex-1 sm:hidden">
                  <Link
                    v-if="notifications.meta.current_page > 1 && notifications.links.prev"
                    :href="notifications.links.prev || ''"
                    class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Anterior
                  </Link>
                  <Link
                    v-if="notifications.meta.current_page < notifications.meta.last_page && notifications.links.next"
                    :href="notifications.links.next || ''"
                    class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Siguiente
                  </Link>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Mostrando
                      <span class="font-medium">{{ notifications.meta.from || 0 }}</span>
                      a
                      <span class="font-medium">{{ notifications.meta.to || 0 }}</span>
                      de
                      <span class="font-medium">{{ notifications.meta.total || 0 }}</span>
                      resultados
                    </p>
                  </div>
                  <div v-if="notifications.meta.links && notifications.meta.links.length > 3">
                    <nav class="inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      <Link
                        v-if="notifications.meta.current_page > 1 && notifications.links.prev"
                        :href="notifications.links.prev || ''"
                        class="relative inline-flex items-center px-2 py-2 text-gray-400 rounded-l-md border border-gray-300 bg-white hover:bg-gray-50"
                      >
                        <span class="sr-only">Anterior</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </Link>
                      <template v-for="(link, i) in notifications.meta.links.slice(1, -1)" :key="i">
                        <Link
                          v-if="link.url"
                          :href="link.url"
                          :class="[
                            'relative inline-flex items-center px-4 py-2 text-sm font-medium border',
                            link.active
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          ]"
                        >
                          {{ link.label }}
                        </Link>
                        <span
                          v-else
                          class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300"
                        >
                          {{ link.label }}
                        </span>
                      </template>
                      <Link
                        v-if="notifications.meta.current_page < notifications.meta.last_page && notifications.links.next"
                        :href="notifications.links.next || ''"
                        class="relative inline-flex items-center px-2 py-2 text-gray-400 rounded-r-md border border-gray-300 bg-white hover:bg-gray-50"
                      >
                        <span class="sr-only">Siguiente</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                      </Link>
                    </nav>
                  </div>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Delete Confirmation Dialog -->
    <Dialog :open="showDeleteConfirmation" @update:open="showDeleteConfirmation = $event">
      <DialogContent class="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle class="dark:text-white">Eliminar todas las notificaciones</DialogTitle>
          <DialogDescription class="dark:text-gray-300">
            ¿Estás seguro de que quieres eliminar todas las notificaciones? Esta acción no se puede deshacer.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter class="flex space-x-2 justify-end">
          <button
            @click="cancelDeleteAll"
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500 transition-colors"
          >
            Cancelar
          </button>
          <button
            @click="confirmDeleteAll"
            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 transition-colors"
          >
            Eliminar
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>
