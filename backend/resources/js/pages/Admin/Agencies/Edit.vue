<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

const props = defineProps<{
  agency: {
    id: number;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    address: string | null;
    city: string | null;
    state: string | null;
    country: string | null;
    postal_code: string | null;
    phone: string | null;
    email: string | null;
    website: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
  isAgencyUser?: boolean;
  isSuperAdmin?: boolean;
  userAgencyId?: number;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  // Only show the agencies index link for superadmins
  ...(props.isSuperAdmin ? [
    {
      title: 'Operador/Operadores y Agencias',
      href: '/dashboard/agencies',
    }
  ] : []),
  {
    title: props.isAgencyUser ? 'Mi Agencia' : props.agency.name,
    href: `/dashboard/agencies/${props.agency.id}`,
  },
  {
    title: 'Editar',
    href: `/dashboard/agencies/${props.agency.id}/edit`,
  },
];

// Create form with only fields that agency users can edit if isAgencyUser is true
const form = useForm(props.isAgencyUser ? {
  description: props.agency.description || '',
  logo: null as File | null,
  address: props.agency.address || '',
  city: props.agency.city || '',
  state: props.agency.state || '',
  country: props.agency.country || '',
  postal_code: props.agency.postal_code || '',
  phone: props.agency.phone || '',
  email: props.agency.email || '',
  website: props.agency.website || '',
  _method: 'PUT',
} : {
  name: props.agency.name,
  slug: props.agency.slug || '',
  description: props.agency.description || '',
  logo: null as File | null,
  address: props.agency.address || '',
  city: props.agency.city || '',
  state: props.agency.state || '',
  country: props.agency.country || '',
  postal_code: props.agency.postal_code || '',
  phone: props.agency.phone || '',
  email: props.agency.email || '',
  website: props.agency.website || '',
  is_active: props.agency.is_active,
  _method: 'PUT',
});

const logoPreview = ref<string | null>(null);

onMounted(() => {
  if (props.agency.logo) {
    logoPreview.value = `/storage/${props.agency.logo}`;
  }
});

const handleLogoChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];
    form.logo = file;

    // Create preview URL
    logoPreview.value = URL.createObjectURL(file);
  }
};

const submit = () => {
  // Use different endpoints based on user role
  if (props.isAgencyUser) {
    form.post('/dashboard/my-agency', {
      preserveScroll: true,
    });
  } else {
    form.post(`/dashboard/agencies/${props.agency.id}`, {
      preserveScroll: true,
    });
  }
};

const generateSlug = () => {
  if (form.name && !form.slug) {
    form.slug = form.name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
};
</script>

<template>
  <Head :title="isAgencyUser ? 'Editar Mi Agencia' : 'Editar Operador/Agencia'" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">{{ isAgencyUser ? 'Editar Mi Agencia' : 'Editar Operador/Agencia' }}</h1>
        <div class="flex space-x-2">
          <Link
            v-if="!isAgencyUser"
            :href="`/dashboard/agencies/${agency.id}`"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Ver Detalles
          </Link>
          <Link
            v-if="isSuperAdmin"
            href="/dashboard/agencies"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver a Operador/Operadores y Agencias
          </Link>
          <Link
            v-else
            href="/dashboard"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver al Panel
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nombre *</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
                @blur="generateSlug"
                :disabled="!isSuperAdmin"
              />
              <div v-if="!isSuperAdmin" class="text-xs text-gray-500 mt-1">Solo los superadmins pueden cambiar el nombre de la agencia.</div>
              <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</div>
            </div>

            <div>
              <label for="slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Slug</label>
              <input
                id="slug"
                v-model="form.slug"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                :disabled="!isSuperAdmin"
              />
              <div v-if="!isSuperAdmin" class="text-xs text-gray-500 mt-1">Solo los superadmins pueden cambiar el slug de la agencia.</div>
              <div v-if="form.errors.slug" class="text-red-500 text-sm mt-1">{{ form.errors.slug }}</div>
            </div>

            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="3"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>

            <div>
              <label for="logo" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Logo</label>
              <input
                id="logo"
                type="file"
                class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:text-gray-300 dark:file:bg-gray-700 dark:file:text-gray-300"
                accept="image/*"
                @change="handleLogoChange"
              />
              <div v-if="form.errors.logo" class="text-red-500 text-sm mt-1">{{ form.errors.logo }}</div>
              <div v-if="logoPreview" class="mt-2">
                <img :src="logoPreview" alt="Logo Preview" class="h-20 w-20 object-cover rounded-md" />
              </div>
            </div>

            <div>
              <label for="is_active" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Estado</label>
              <select
                id="is_active"
                v-model="form.is_active"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option :value="true">Activo</option>
                <option :value="false">Inactivo</option>
              </select>
              <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">{{ form.errors.is_active }}</div>
            </div>
          </div>

          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mt-6">Información de Contacto</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Correo Electrónico</label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</div>
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Teléfono</label>
              <input
                id="phone"
                v-model="form.phone"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.phone" class="text-red-500 text-sm mt-1">{{ form.errors.phone }}</div>
            </div>

            <div>
              <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sitio Web</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.website" class="text-red-500 text-sm mt-1">{{ form.errors.website }}</div>
            </div>
          </div>

          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mt-6">Dirección</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="md:col-span-2">
              <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Calle y Número</label>
              <input
                id="address"
                v-model="form.address"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.address" class="text-red-500 text-sm mt-1">{{ form.errors.address }}</div>
            </div>

            <div>
              <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ciudad</label>
              <input
                id="city"
                v-model="form.city"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.city" class="text-red-500 text-sm mt-1">{{ form.errors.city }}</div>
            </div>

            <div>
              <label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Estado/Provincia</label>
              <input
                id="state"
                v-model="form.state"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.state" class="text-red-500 text-sm mt-1">{{ form.errors.state }}</div>
            </div>

            <div>
              <label for="postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Código Postal</label>
              <input
                id="postal_code"
                v-model="form.postal_code"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.postal_code" class="text-red-500 text-sm mt-1">{{ form.errors.postal_code }}</div>
            </div>

            <div>
              <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300">País</label>
              <input
                id="country"
                v-model="form.country"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.country" class="text-red-500 text-sm mt-1">{{ form.errors.country }}</div>
            </div>
          </div>

          <div class="flex justify-end mt-6">
            <button
              type="button"
              class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 mr-2"
              @click="$inertia.visit(isAgencyUser ? '/dashboard' : (isSuperAdmin ? '/dashboard/agencies' : `/dashboard/agencies/${agency.id}`))"
            >
              Cancelar
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              :disabled="form.processing"
            >
              {{ form.processing ? 'Guardando...' : (isAgencyUser ? 'Actualizar Mi Agencia' : 'Actualizar Operador/Agencia') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
