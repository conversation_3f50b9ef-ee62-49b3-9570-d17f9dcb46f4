<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps<{
  agency: {
    id: number;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    address: string | null;
    city: string | null;
    state: string | null;
    country: string | null;
    postal_code: string | null;
    phone: string | null;
    email: string | null;
    website: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    users: Array<{
      id: number;
      name: string;
      email: string;
      role: string;
    }>;
  };
  isAgencyUser?: boolean;
  isSuperAdmin?: boolean;
  userAgencyId?: number;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  // Only show the agencies index link for superadmins
  ...(props.isSuperAdmin ? [
    {
      title: 'Operador/Operadores y Agencias',
      href: '/dashboard/agencies',
    }
  ] : []),
  {
    title: props.isAgencyUser ? 'Mi Agencia' : props.agency.name,
    href: `/dashboard/agencies/${props.agency.id}`,
  },
];

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};
</script>

<template>
  <Head :title="`Operador/Agencia: ${agency.name}`" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Detalles de la Operador/Agencia</h1>
        <div class="flex space-x-2">
          <!-- Only superadmins can edit agencies -->
          <Link
            v-if="isSuperAdmin"
            :href="`/dashboard/agencies/${agency.id}/edit`"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Editar
          </Link>
          <!-- Only superadmins can manage users -->
          <Link
            v-if="isSuperAdmin"
            :href="`/dashboard/agencies/${agency.id}/manage-users`"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Gestionar Usuarios
          </Link>
          <!-- Back button - different for superadmins and agency users -->
          <Link
            v-if="isSuperAdmin"
            href="/dashboard/agencies"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver a Operador/Operadores y Agencias
          </Link>
          <Link
            v-else
            href="/dashboard"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver al Panel
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
          <div class="flex flex-col md:flex-row md:items-start">
            <div class="md:w-1/3 mb-6 md:mb-0 md:pr-6">
              <div v-if="agency.logo" class="mb-4">
                <img
                  :src="`/storage/${agency.logo}`"
                  :alt="agency.name"
                  class="w-full h-auto rounded-lg shadow-md"
                />
              </div>
              <div v-else class="w-full h-48 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-4">
                <span class="text-gray-500 dark:text-gray-400 text-4xl">{{ agency.name.charAt(0) }}</span>
              </div>

              <div class="mt-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ agency.name }}</h3>
                <p class="text-gray-600 dark:text-gray-300">
                  {{ agency.city }}{{ agency.city && agency.country ? ', ' : '' }}{{ agency.country }}
                </p>
              </div>

              <div class="mt-4">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                  :class="agency.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ agency.is_active ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>

            <div class="md:w-2/3">
              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="font-semibold text-lg mb-2 text-gray-900 dark:text-white">Descripción</h4>
                <p class="text-gray-700 dark:text-gray-300">{{ agency.description || 'No hay descripción disponible.' }}</p>
              </div>

              <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-lg mb-2 text-gray-900 dark:text-white">Información de Contacto</h4>
                  <ul class="space-y-2">
                    <li v-if="agency.email" class="flex items-start">
                      <span class="text-gray-600 dark:text-gray-400 mr-2">Correo:</span>
                      <a
                        :href="`mailto:${agency.email}`"
                        class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        {{ agency.email }}
                      </a>
                    </li>
                    <li v-if="agency.phone" class="flex items-start">
                      <span class="text-gray-600 dark:text-gray-400 mr-2">Teléfono:</span>
                      <a
                        :href="`tel:${agency.phone}`"
                        class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        {{ agency.phone }}
                      </a>
                    </li>
                    <li v-if="agency.website" class="flex items-start">
                      <span class="text-gray-600 dark:text-gray-400 mr-2">Sitio Web:</span>
                      <a
                        :href="agency.website"
                        target="_blank"
                        class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        {{ agency.website }}
                      </a>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 class="font-semibold text-lg mb-2 text-gray-900 dark:text-white">Dirección</h4>
                  <address class="not-italic text-gray-700 dark:text-gray-300">
                    <div v-if="agency.address">{{ agency.address }}</div>
                    <div>
                      {{ agency.city }}{{ agency.city && agency.state ? ', ' : '' }}{{ agency.state }}
                      {{ agency.postal_code }}
                    </div>
                    <div v-if="agency.country">{{ agency.country }}</div>
                  </address>
                </div>
              </div>

              <div class="mt-6">
                <h4 class="font-semibold text-lg mb-2 text-gray-900 dark:text-white">
                  Usuarios ({{ agency.users.length }})
                </h4>
                <div v-if="agency.users.length > 0" class="overflow-x-auto">
                  <table class="min-w-full bg-white dark:bg-gray-800">
                    <thead>
                      <tr>
                        <th
                          class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Nombre
                        </th>
                        <th
                          class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Correo
                        </th>
                        <th
                          class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Rol
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="user in agency.users" :key="user.id">
                        <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white">
                          {{ user.name }}
                        </td>
                        <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white">
                          {{ user.email }}
                        </td>
                        <td class="py-2 px-4 border-b border-gray-200 dark:border-gray-700">
                          <span
                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                            :class="
                              user.role === 'superadmin'
                                ? 'bg-purple-100 text-purple-800'
                                : user.role === 'admin'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                            "
                          >
                            {{ user.role.charAt(0).toUpperCase() + user.role.slice(1) }}
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <p v-else class="text-gray-500 dark:text-gray-400">No hay usuarios asignados a esta operador/agencia.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
