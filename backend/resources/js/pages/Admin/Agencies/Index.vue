<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  agencies: {
    data: Array<{
      id: number;
      name: string;
      slug: string;
      description: string | null;
      logo: string | null;
      address: string | null;
      city: string | null;
      state: string | null;
      country: string | null;
      postal_code: string | null;
      phone: string | null;
      email: string | null;
      website: string | null;
      is_active: boolean;
      created_at: string;
      updated_at: string;
      users_count: number;
    }>;
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Operadores y Agencias',
    href: '/dashboard/agencies',
  },
];
</script>

<template>
  <Head title="Gestión de Operadores y Agencias" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Gestión de Operadores y Agencias</h1>
        <Link
          href="/dashboard/agencies/create"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Crear Operador/Agencia
        </Link>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Nombre
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Ubicación
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Usuarios
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Estado
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Acciones
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="agency in agencies.data" :key="agency.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img
                      v-if="agency.logo"
                      :src="`/storage/${agency.logo}`"
                      :alt="agency.name"
                      class="h-10 w-10 rounded-full object-cover"
                    />
                    <div
                      v-else
                      class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center"
                    >
                      <span class="text-gray-500 dark:text-gray-300 text-lg">{{ agency.name.charAt(0) }}</span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ agency.name }}</div>
                    <div v-if="agency.email" class="text-sm text-gray-500 dark:text-gray-300">{{ agency.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-300">
                  {{ agency.city }}{{ agency.city && agency.country ? ', ' : '' }}{{ agency.country }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500 dark:text-gray-300">{{ agency.users_count }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="agency.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ agency.is_active ? 'Activo' : 'Inactivo' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <Link
                  :href="`/dashboard/agencies/${agency.id}`"
                  class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                >
                  Ver
                </Link>
                <Link
                  :href="`/dashboard/agencies/${agency.id}/edit`"
                  class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3"
                >
                  Editar
                </Link>
                <Link
                  :href="`/dashboard/agencies/${agency.id}/manage-users`"
                  class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"
                >
                  Usuarios
                </Link>
                <Link
                  :href="`/dashboard/agencies/${agency.id}`"
                  method="delete"
                  as="button"
                  type="button"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  @click.prevent="
                    $inertia.delete(`/dashboard/agencies/${agency.id}`, {
                      onBefore: () => confirm('¿Estás seguro de que quieres eliminar esta agencia?'),
                    })
                  "
                >
                  Eliminar
                </Link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center" v-if="agencies.last_page > 1">
        <div class="text-sm text-gray-500 dark:text-gray-300">
          Mostrando {{ agencies.data.length }} de {{ agencies.total }} operadores/agencias
        </div>
        <div class="flex space-x-2">
          <Link
            v-for="page in agencies.last_page"
            :key="page"
            :href="`/dashboard/agencies?page=${page}`"
            class="px-3 py-1 rounded"
            :class="
              page === agencies.current_page
                ? 'bg-blue-500 text-white'
                : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
            "
          >
            {{ page }}
          </Link>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
