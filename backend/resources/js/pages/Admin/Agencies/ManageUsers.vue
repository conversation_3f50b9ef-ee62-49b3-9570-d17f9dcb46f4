<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  agency: {
    id: number;
    name: string;
    users: Array<{
      id: number;
      name: string;
      email: string;
      role: string;
    }>;
  };
  availableUsers: Array<{
    id: number;
    name: string;
    email: string;
    role: string;
  }>;
  isAgencyUser?: boolean;
  isSuperAdmin?: boolean;
  isAgencyAdmin?: boolean;
  canAddUsers?: boolean;
  userAgencyId?: number;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  // Only show the agencies index link for superadmins
  ...(props.isSuperAdmin ? [
    {
      title: 'Operador/Operadores y Agencias',
      href: '/dashboard/agencies',
    }
  ] : []),
  {
    title: props.isAgencyUser ? 'Mi Agencia' : props.agency.name,
    href: `/dashboard/agencies/${props.agency.id}`,
  },
  {
    title: 'Gestionar Usuarios',
    href: `/dashboard/agencies/${props.agency.id}/manage-users`,
  },
];

const form = useForm({
  user_ids: [] as number[],
});

const selectedUsers = ref<number[]>([]);

const submit = () => {
  form.user_ids = selectedUsers.value;
  form.post(`/dashboard/agencies/${props.agency.id}/add-users`, {
    preserveScroll: true,
    onSuccess: () => {
      selectedUsers.value = [];
    },
  });
};

const removeUser = (userId: number) => {
  if (confirm('¿Estás seguro de que quieres eliminar este usuario de la operador/agencia?')) {
    useForm({}).delete(`/dashboard/agencies/${props.agency.id}/users/${userId}`, {
      preserveScroll: true,
    });
  }
};
</script>

<template>
  <Head title="Gestionar Usuarios de la Operador/Agencia" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Gestionar Usuarios de {{ agency.name }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/agencies/${agency.id}`"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Detalles de la Operador/Agencia
          </Link>
          <Link
            href="/dashboard/agencies"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver a Operador/Operadores y Agencias
          </Link>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h2 class="text-xl font-semibold mb-4 text-white">Usuarios Actuales ({{ agency.users.length }})</h2>
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div v-if="agency.users.length > 0">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Nombre
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Correo
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Rol
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  <tr v-for="user in agency.users" :key="user.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {{ user.name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {{ user.email }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                        :class="
                          user.role === 'superadmin'
                            ? 'bg-purple-100 text-purple-800'
                            : user.role === 'admin'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        "
                      >
                        {{ user.role.charAt(0).toUpperCase() + user.role.slice(1) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        v-if="canAddUsers"
                        @click="removeUser(user.id)"
                        class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        Eliminar
                      </button>
                      <span v-else class="text-gray-400">-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="p-6 text-gray-500 dark:text-gray-400">
              No hay usuarios asignados a esta operador/agencia.
            </div>
          </div>
        </div>

        <div v-if="canAddUsers">
          <h2 class="text-xl font-semibold mb-4 text-white">Añadir Usuarios a la Operador/Agencia</h2>
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
            <div v-if="availableUsers.length > 0">
              <form @submit.prevent="submit">
                <div class="mb-4">
                  <label for="user_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Seleccionar Usuarios
                  </label>
                  <select
                    v-model="selectedUsers"
                    multiple
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    size="10"
                  >
                    <option v-for="user in availableUsers" :key="user.id" :value="user.id">
                      {{ user.name }} ({{ user.email }}) - {{ user.role.charAt(0).toUpperCase() + user.role.slice(1) }}
                    </option>
                  </select>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Mantener pulsado Ctrl (Windows) o Command (Mac) para seleccionar varios usuarios
                  </p>
                </div>

                <div class="flex justify-end">
                  <button
                    type="submit"
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    :disabled="selectedUsers.length === 0 || form.processing"
                  >
                    {{ form.processing ? 'Añadiendo...' : 'Añadir Usuarios Seleccionados' }}
                  </button>
                </div>
              </form>
            </div>
            <div v-else class="text-gray-500 dark:text-gray-400">
              No hay usuarios disponibles para añadir.
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
