<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

const props = defineProps<{
  agency: {
    id: number;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    address: string | null;
    city: string | null;
    state: string | null;
    country: string | null;
    postal_code: string | null;
    phone: string | null;
    email: string | null;
    website: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Mi Agencia',
    href: `/dashboard/my-agency`,
  },
];

const form = useForm({
  description: props.agency.description || '',
  logo: null as File | null,
  address: props.agency.address || '',
  city: props.agency.city || '',
  state: props.agency.state || '',
  country: props.agency.country || '',
  postal_code: props.agency.postal_code || '',
  phone: props.agency.phone || '',
  email: props.agency.email || '',
  website: props.agency.website || '',
});

const logoPreview = ref<string | null>(null);
const logoInput = ref<HTMLInputElement | null>(null);

onMounted(() => {
  if (props.agency.logo) {
    logoPreview.value = `/storage/${props.agency.logo}`;
  }
});

const handleLogoChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    form.logo = file;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      logoPreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
};

const clearLogo = () => {
  form.logo = null;
  logoPreview.value = null;
  if (logoInput.value) {
    logoInput.value.value = '';
  }
};

const submit = () => {
  form.put(route('admin.my-agency.update'));
};
</script>

<template>
  <Head title="Editar Mi Agencia" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Editar Mi Agencia</h1>
        <div class="flex space-x-2">
          <Link
            href="/dashboard"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver al Panel
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Agency Name (Read-only) -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nombre de la Agencia</label>
              <div class="mt-1 p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
                {{ agency.name }}
              </div>
              <p class="text-xs text-gray-500 mt-1">El nombre de la agencia no se puede cambiar.</p>
            </div>

            <!-- Logo -->
            <div>
              <label for="logo" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Logo</label>
              <div class="mt-1 flex items-center">
                <div v-if="logoPreview" class="mr-3">
                  <img :src="logoPreview" alt="Logo Preview" class="h-20 w-20 object-cover rounded-md" />
                </div>
                <div class="flex flex-col space-y-2">
                  <label
                    for="logo-upload"
                    class="cursor-pointer px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Seleccionar Logo
                  </label>
                  <input
                    id="logo-upload"
                    ref="logoInput"
                    type="file"
                    class="hidden"
                    accept="image/*"
                    @change="handleLogoChange"
                  />
                  <button
                    v-if="logoPreview"
                    type="button"
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                    @click="clearLogo"
                  >
                    Eliminar Logo
                  </button>
                </div>
              </div>
              <div v-if="form.errors.logo" class="text-red-500 text-sm mt-1">{{ form.errors.logo }}</div>
            </div>

            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="4"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>

            <div>
              <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dirección</label>
              <input
                id="address"
                v-model="form.address"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.address" class="text-red-500 text-sm mt-1">{{ form.errors.address }}</div>
            </div>

            <div>
              <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ciudad</label>
              <input
                id="city"
                v-model="form.city"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.city" class="text-red-500 text-sm mt-1">{{ form.errors.city }}</div>
            </div>

            <div>
              <label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Estado/Provincia</label>
              <input
                id="state"
                v-model="form.state"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.state" class="text-red-500 text-sm mt-1">{{ form.errors.state }}</div>
            </div>

            <div>
              <label for="postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Código Postal</label>
              <input
                id="postal_code"
                v-model="form.postal_code"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.postal_code" class="text-red-500 text-sm mt-1">{{ form.errors.postal_code }}</div>
            </div>

            <div>
              <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300">País</label>
              <input
                id="country"
                v-model="form.country"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.country" class="text-red-500 text-sm mt-1">{{ form.errors.country }}</div>
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Teléfono</label>
              <input
                id="phone"
                v-model="form.phone"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.phone" class="text-red-500 text-sm mt-1">{{ form.errors.phone }}</div>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</div>
            </div>

            <div>
              <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sitio Web</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.website" class="text-red-500 text-sm mt-1">{{ form.errors.website }}</div>
            </div>
          </div>

          <div class="flex justify-end mt-6">
            <button
              type="button"
              class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 mr-2"
              @click="$inertia.visit('/dashboard')"
            >
              Cancelar
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              :disabled="form.processing"
            >
              {{ form.processing ? 'Guardando...' : 'Actualizar Mi Agencia' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
