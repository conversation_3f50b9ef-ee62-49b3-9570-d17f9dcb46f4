<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Eventos',
    href: '/dashboard/events',
  },
  {
    title: 'Crear',
    href: '/dashboard/events/create',
  },
];

const form = useForm({
  title: '',
  description: '',
  start_datetime: '',
  end_datetime: '',
  location: '',
  image: null as File | null,
  rating: null as number | null,
  is_featured: false,
});

const handleImageChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    form.image = target.files[0];
  }
};

const submit = () => {
  form.post('/dashboard/events', {
    forceFormData: true,
  });
};
</script>

<template>
  <Head title="Crear Evento" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Crear Evento</h1>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit">
          <div class="mb-4">
            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title</label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
            <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">
              {{ form.errors.title }}
            </div>
          </div>

          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            ></textarea>
            <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">
              {{ form.errors.description }}
            </div>
          </div>

          <div class="mb-4">
            <label for="start_datetime" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date & Time</label>
            <input
              id="start_datetime"
              v-model="form.start_datetime"
              type="datetime-local"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
            <div v-if="form.errors.start_datetime" class="text-red-500 text-sm mt-1">
              {{ form.errors.start_datetime }}
            </div>
          </div>

          <div class="mb-4">
            <label for="end_datetime" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date & Time</label>
            <input
              id="end_datetime"
              v-model="form.end_datetime"
              type="datetime-local"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <div v-if="form.errors.end_datetime" class="text-red-500 text-sm mt-1">
              {{ form.errors.end_datetime }}
            </div>
          </div>

          <div class="mb-4">
            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
            <input
              id="location"
              v-model="form.location"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
            <div v-if="form.errors.location" class="text-red-500 text-sm mt-1">
              {{ form.errors.location }}
            </div>
          </div>

          <div class="mb-4">
            <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagen</label>
            <input
              id="image"
              type="file"
              accept="image/*"
              @change="handleImageChange"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">
              {{ form.errors.image }}
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Formatos aceptados: JPG, PNG, GIF. Tamaño máximo: 2MB</p>
          </div>

          <div class="mb-4">
            <label for="rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Rating (1-5)</label>
            <input
              id="rating"
              v-model="form.rating"
              type="number"
              min="1"
              max="5"
              step="0.1"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <div v-if="form.errors.rating" class="text-red-500 text-sm mt-1">
              {{ form.errors.rating }}
            </div>
          </div>

          <div class="mb-4">
            <div class="flex items-center">
              <input
                id="is_featured"
                v-model="form.is_featured"
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <label for="is_featured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Featured Event</label>
            </div>
            <div v-if="form.errors.is_featured" class="text-red-500 text-sm mt-1">
              {{ form.errors.is_featured }}
            </div>
          </div>

          <div class="flex justify-end">
            <button
              type="button"
              class="mr-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
              @click="$inertia.visit('/dashboard/events')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800"
              :disabled="form.processing"
            >
              Crear Evento
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
