<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  event: {
    id: number;
    title: string;
    description: string;
    start_datetime: string;
    end_datetime: string | null;
    location: string;
    image: string | null;
    rating: number | null;
    is_featured: boolean;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Eventos',
    href: '/dashboard/events',
  },
  {
    title: 'Editar',
    href: `/dashboard/events/${props.event.id}/edit`,
  },
];

// Format datetime for input fields
const formatDateTimeForInput = (dateString: string | null) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
};

const form = useForm({
  title: props.event.title,
  description: props.event.description,
  start_datetime: formatDateTimeForInput(props.event.start_datetime),
  end_datetime: formatDateTimeForInput(props.event.end_datetime),
  location: props.event.location,
  image: null as File | null,
  _method: 'PUT', // Required for file uploads with PUT method
  rating: props.event.rating,
  is_featured: props.event.is_featured,
});

// Current image preview
const currentImage = ref(props.event.image);

const handleImageChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    form.image = target.files[0];

    // Create a preview URL for the selected image
    const reader = new FileReader();
    reader.onload = (e) => {
      currentImage.value = e.target?.result as string;
    };
    reader.readAsDataURL(target.files[0]);
  }
};

const submit = () => {
  form.post(`/dashboard/events/${props.event.id}`, {
    forceFormData: true,
  });
};
</script>

<template>
  <Head title="Editar Evento" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Editar Evento</h1>
      </div>

      <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit">
          <div class="mb-4">
            <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">
              {{ form.errors.title }}
            </div>
          </div>

          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            ></textarea>
            <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">
              {{ form.errors.description }}
            </div>
          </div>

          <div class="mb-4">
            <label for="start_datetime" class="block text-sm font-medium text-gray-700">Start Date & Time</label>
            <input
              id="start_datetime"
              v-model="form.start_datetime"
              type="datetime-local"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <div v-if="form.errors.start_datetime" class="text-red-500 text-sm mt-1">
              {{ form.errors.start_datetime }}
            </div>
          </div>

          <div class="mb-4">
            <label for="end_datetime" class="block text-sm font-medium text-gray-700">End Date & Time</label>
            <input
              id="end_datetime"
              v-model="form.end_datetime"
              type="datetime-local"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <div v-if="form.errors.end_datetime" class="text-red-500 text-sm mt-1">
              {{ form.errors.end_datetime }}
            </div>
          </div>

          <div class="mb-4">
            <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
            <input
              id="location"
              v-model="form.location"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <div v-if="form.errors.location" class="text-red-500 text-sm mt-1">
              {{ form.errors.location }}
            </div>
          </div>

          <div class="mb-4">
            <label for="image" class="block text-sm font-medium text-gray-700">Imagen</label>
            <div v-if="currentImage" class="mb-2">
              <img
                :src="typeof currentImage === 'string' && currentImage.startsWith('/') ? currentImage : `/storage/${currentImage}`"
                alt="Vista previa"
                class="h-32 w-auto object-cover rounded-md"
              />
            </div>
            <input
              id="image"
              type="file"
              accept="image/*"
              @change="handleImageChange"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">
              {{ form.errors.image }}
            </div>
            <p class="text-sm text-gray-500 mt-1">Formatos aceptados: JPG, PNG, GIF. Tamaño máximo: 2MB</p>
          </div>

          <div class="mb-4">
            <label for="rating" class="block text-sm font-medium text-gray-700">Rating (1-5)</label>
            <input
              id="rating"
              v-model="form.rating"
              type="number"
              min="1"
              max="5"
              step="0.1"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <div v-if="form.errors.rating" class="text-red-500 text-sm mt-1">
              {{ form.errors.rating }}
            </div>
          </div>

          <div class="mb-4">
            <div class="flex items-center">
              <input
                id="is_featured"
                v-model="form.is_featured"
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label for="is_featured" class="ml-2 block text-sm text-gray-700">Featured Event</label>
            </div>
            <div v-if="form.errors.is_featured" class="text-red-500 text-sm mt-1">
              {{ form.errors.is_featured }}
            </div>
          </div>

          <div class="flex justify-end">
            <button
              type="button"
              class="mr-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              @click="$inertia.visit('/dashboard/events')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              :disabled="form.processing"
            >
              Actualizar Evento
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
