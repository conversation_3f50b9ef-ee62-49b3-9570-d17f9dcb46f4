<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps<{
  event: {
    id: number;
    title: string;
    description: string;
    start_datetime: string;
    end_datetime: string | null;
    location: string;
    image: string | null;
    rating: number | null;
    is_featured: boolean;
    created_at: string;
    updated_at: string;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Eventos',
    href: '/dashboard/events',
  },
  {
    title: 'Ver',
    href: `/dashboard/events/${props.event.id}`,
  },
];

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const confirmDelete = () => {
  if (confirm('¿Estás seguro de que quieres eliminar este evento?')) {
    useForm({}).delete(`/dashboard/events/${props.event.id}`);
  }
};
</script>

<template>
  <Head :title="event.title" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">{{ event.title }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/events/${event.id}/edit`"
            class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
          >
            Editar
          </Link>
          <button
            @click="confirmDelete"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Eliminar
          </button>
          <Link
            href="/dashboard/events"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Left column: Image and basic info -->
            <div class="md:col-span-1">
              <div v-if="event.image" class="mb-4">
                <img 
                  :src="event.image.startsWith('/') ? event.image : `/storage/${event.image}`" 
                  :alt="event.title" 
                  class="w-full h-auto rounded-lg shadow-md" 
                />
              </div>
              <div v-else class="mb-4 bg-gray-200 dark:bg-gray-700 rounded-lg h-48 flex items-center justify-center">
                <span class="text-gray-500 dark:text-gray-400">Sin imagen</span>
              </div>

              <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mt-4">
                <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Información Básica</h3>
                
                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Ubicación:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ event.location }}</span>
                </div>
                
                <div class="mb-2" v-if="event.rating">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Valoración:</span>
                  <span class="ml-2 text-sm text-gray-800 dark:text-white">
                    {{ event.rating }} / 5
                    <span class="text-yellow-500">
                      <i v-for="i in Math.floor(event.rating)" :key="i" class="pi pi-star-fill mr-1"></i>
                      <i v-if="event.rating % 1 > 0" class="pi pi-star-half mr-1"></i>
                    </span>
                  </span>
                </div>
                
                <div class="mb-2">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Destacado:</span>
                  <span :class="`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${event.is_featured ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`">
                    {{ event.is_featured ? 'Sí' : 'No' }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- Right column: Description and details -->
            <div class="md:col-span-2">
              <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Descripción</h3>
                <div class="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                  {{ event.description }}
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Fechas</h3>
                  
                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Fecha de inicio:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatDate(event.start_datetime) }}</span>
                  </div>
                  
                  <div class="mb-2" v-if="event.end_datetime">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Fecha de fin:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatDate(event.end_datetime) }}</span>
                  </div>
                </div>
                
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Información Adicional</h3>
                  
                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Creado:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatDate(event.created_at) }}</span>
                  </div>
                  
                  <div class="mb-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Actualizado:</span>
                    <span class="ml-2 text-sm text-gray-800 dark:text-white">{{ formatDate(event.updated_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
