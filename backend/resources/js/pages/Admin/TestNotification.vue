<template>
  <AppLayout title="Test Notifications">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        Test Notifications
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900 dark:text-gray-100">
            <h3 class="text-lg font-medium mb-4">Send Test Notification</h3>
            
            <div class="mb-4">
              <label for="notification-type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notification Type
              </label>
              <select
                id="notification-type"
                v-model="notificationType"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600"
              >
                <option value="system">System Notification</option>
                <option value="reservation">Reservation Notification</option>
                <option value="agency">Agency Notification</option>
              </select>
            </div>
            
            <div class="mb-4">
              <label for="recipient-type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Recipient
              </label>
              <select
                id="recipient-type"
                v-model="recipientType"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600"
              >
                <option value="superadmin">Superadmins</option>
                <option value="agency_admin">Agency Admins</option>
                <option value="all">All Users</option>
              </select>
            </div>
            
            <div v-if="recipientType === 'agency_admin'" class="mb-4">
              <label for="agency-id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Agency ID
              </label>
              <input
                id="agency-id"
                v-model="agencyId"
                type="number"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600"
                placeholder="Enter agency ID"
              />
            </div>
            
            <div class="mb-4">
              <label for="notification-title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notification Title
              </label>
              <input
                id="notification-title"
                v-model="title"
                type="text"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600"
                placeholder="Enter notification title"
              />
            </div>
            
            <div class="mb-4">
              <label for="notification-message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notification Message
              </label>
              <textarea
                id="notification-message"
                v-model="message"
                rows="3"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600"
                placeholder="Enter notification message"
              ></textarea>
            </div>
            
            <div class="flex justify-end">
              <button
                @click="sendTestNotification"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                :disabled="loading"
              >
                <span v-if="loading">Sending...</span>
                <span v-else>Send Test Notification</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const notificationType = ref('system');
const recipientType = ref('superadmin');
const agencyId = ref<number | null>(null);
const title = ref('Test Notification');
const message = ref('This is a test notification.');
const loading = ref(false);

const sendTestNotification = () => {
  loading.value = true;
  
  const data = {
    type: notificationType.value,
    recipient: recipientType.value,
    agency_id: agencyId.value,
    title: title.value,
    message: message.value,
  };
  
  router.post(route('admin.notifications.test'), data, {
    onSuccess: () => {
      loading.value = false;
      title.value = 'Test Notification';
      message.value = 'This is a test notification.';
    },
    onError: () => {
      loading.value = false;
    },
  });
};
</script>
