<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps<{
  news: {
    id: number;
    title: string;
    summary: string;
    datetime: string;
    location: string;
    image: string | null;
    created_at: string;
    updated_at: string;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'News',
    href: '/dashboard/news',
  },
  {
    title: props.news.title,
    href: `/dashboard/news/${props.news.id}`,
  },
];

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};
</script>

<template>
  <Head :title="`News - ${news.title}`" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold">{{ news.title }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/news/${news.id}/edit`"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Edit
          </Link>
          <Link
            :href="`/dashboard/news`"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Back to List
          </Link>
        </div>
      </div>

      <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-2">Details</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-sm text-gray-500 mb-1">Date & Time</p>
              <p class="font-medium">{{ formatDate(news.datetime) }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500 mb-1">Location</p>
              <p class="font-medium">{{ news.location }}</p>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-2">Summary</h2>
          <p class="text-gray-700 whitespace-pre-line">{{ news.summary }}</p>
        </div>

        <div v-if="news.image">
          <h2 class="text-lg font-semibold mb-2">Image</h2>
          <img :src="news.image" alt="News image" class="max-w-full h-auto rounded-lg" />
        </div>
      </div>
    </div>
  </AppLayout>
</template>
