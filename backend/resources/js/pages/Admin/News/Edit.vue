<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  news: {
    id: number;
    title: string;
    summary: string;
    datetime: string;
    location: string;
    image: string | null;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'News',
    href: '/dashboard/news',
  },
  {
    title: 'Edit',
    href: `/dashboard/news/${props.news.id}/edit`,
  },
];

// Format datetime for input fields
const formatDateTimeForInput = (dateString: string | null) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
};

const form = useForm({
  title: props.news.title,
  summary: props.news.summary,
  datetime: formatDateTimeForInput(props.news.datetime),
  location: props.news.location,
  image: props.news.image || '',
});

const submit = () => {
  form.put(`/dashboard/news/${props.news.id}`);
};
</script>

<template>
  <Head title="Edit News Item" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold">Edit News Item</h1>
      </div>

      <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit">
          <div class="mb-4">
            <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">
              {{ form.errors.title }}
            </div>
          </div>

          <div class="mb-4">
            <label for="summary" class="block text-sm font-medium text-gray-700">Summary</label>
            <textarea
              id="summary"
              v-model="form.summary"
              rows="4"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            ></textarea>
            <div v-if="form.errors.summary" class="text-red-500 text-sm mt-1">
              {{ form.errors.summary }}
            </div>
          </div>

          <div class="mb-4">
            <label for="datetime" class="block text-sm font-medium text-gray-700">Date & Time</label>
            <input
              id="datetime"
              v-model="form.datetime"
              type="datetime-local"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <div v-if="form.errors.datetime" class="text-red-500 text-sm mt-1">
              {{ form.errors.datetime }}
            </div>
          </div>

          <div class="mb-4">
            <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
            <input
              id="location"
              v-model="form.location"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <div v-if="form.errors.location" class="text-red-500 text-sm mt-1">
              {{ form.errors.location }}
            </div>
          </div>

          <div class="mb-4">
            <label for="image" class="block text-sm font-medium text-gray-700">Image</label>
            <input
              id="image"
              v-model="form.image"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="e.g., CorkExpEvent.jpeg"
            />
            <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">
              {{ form.errors.image }}
            </div>
          </div>

          <div class="flex justify-end">
            <button
              type="button"
              class="mr-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              @click="$inertia.visit('/dashboard/news')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              :disabled="form.processing"
            >
              Update News Item
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
