<template>
  <AppLayout>
    <Head title="Gestión de Usuarios de Agencia" />

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900 dark:text-gray-100">
            <div class="flex justify-between items-center mb-6">
              <h1 class="text-2xl font-semibold">Usuarios de {{ agency.name }}</h1>
              <Link
                v-if="isAgencyAdmin"
                :href="route('admin.agency-users.create')"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Crear Usuario
              </Link>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Nombre
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Email
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Rol
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  <tr v-for="user in users" :key="user.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {{ user.name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {{ user.email }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      <span
                        v-if="user.role === 'agency_admin'"
                        class="px-2 py-1 text-xs font-semibold bg-purple-100 text-purple-800 rounded-full dark:bg-purple-900 dark:text-purple-200"
                      >
                        Administrador
                      </span>
                      <span v-else class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200">
                        Usuario
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <Link
                          :href="route('admin.agency-users.show', user.id)"
                          class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                        >
                          Ver
                        </Link>
                        <Link
                          v-if="isAgencyAdmin"
                          :href="route('admin.agency-users.edit', user.id)"
                          class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        >
                          Editar
                        </Link>
                        <button
                          v-if="isAgencyAdmin && $page.props.auth.user.id !== user.id"
                          @click="confirmDelete(user)"
                          class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          Eliminar
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="users.length === 0">
                    <td colspan="4" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No hay usuarios registrados para esta agencia.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <Modal :show="showDeleteModal" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          ¿Estás seguro de que quieres eliminar este usuario?
        </h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Esta acción no se puede deshacer. El usuario perderá acceso al sistema.
        </p>
        <div class="mt-6 flex justify-end">
          <button
            type="button"
            class="mr-3 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-25 transition ease-in-out duration-150"
            @click="closeModal"
          >
            Cancelar
          </button>
          <Link
            :href="route('admin.agency-users.destroy', userToDelete?.id || '')"
            method="delete"
            as="button"
            class="px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
          >
            Eliminar Usuario
          </Link>
        </div>
      </div>
    </Modal>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import Modal from '@/components/Modal.vue';

const props = defineProps<{
  users: Array<{
    id: number;
    name: string;
    email: string;
    role: string;
  }>;
  agency: {
    id: number;
    name: string;
  };
  isAgencyAdmin: boolean;
}>();

const showDeleteModal = ref(false);
const userToDelete = ref<{ id: number; name: string } | null>(null);

const confirmDelete = (user: { id: number; name: string }) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

const closeModal = () => {
  showDeleteModal.value = false;
  userToDelete.value = null;
};
</script>
