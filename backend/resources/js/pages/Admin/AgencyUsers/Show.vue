<template>
  <AppLayout>
    <Head :title="`Usuario: ${agencyUser.name}`" />

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900 dark:text-gray-100">
            <div class="flex justify-between items-center mb-6">
              <h1 class="text-2xl font-semibold">Detalles del Usuario</h1>
              <div class="flex space-x-2">
                <Link
                  :href="route('admin.agency-users.index')"
                  class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  Volver
                </Link>
                <Link
                  v-if="isAgencyAdmin"
                  :href="route('admin.agency-users.edit', agencyUser.id)"
                  class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Editar
                </Link>
              </div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h2 class="text-lg font-semibold mb-4">Información del Usuario</h2>
                  <div class="space-y-4">
                    <div>
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Nombre:</span>
                      <p class="text-gray-900 dark:text-white">{{ agencyUser.name }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Email:</span>
                      <p class="text-gray-900 dark:text-white">{{ agencyUser.email }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Rol:</span>
                      <p class="text-gray-900 dark:text-white">
                        <span
                          v-if="agencyUser.role === 'agency_admin'"
                          class="px-2 py-1 text-xs font-semibold bg-purple-100 text-purple-800 rounded-full dark:bg-purple-900 dark:text-purple-200"
                        >
                          Administrador
                        </span>
                        <span
                          v-else
                          class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200"
                        >
                          Usuario
                        </span>
                      </p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Agencia:</span>
                      <p class="text-gray-900 dark:text-white">{{ agency.name }}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 class="text-lg font-semibold mb-4">Permisos</h2>
                  <div class="space-y-2">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-4 h-4 rounded-full mr-2',
                          agencyUser.role === 'agency_admin' ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600',
                        ]"
                      ></div>
                      <span>Gestionar usuarios de la agencia</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-4 h-4 rounded-full mr-2 bg-green-500"></div>
                      <span>Gestionar experiencias</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-4 h-4 rounded-full mr-2 bg-green-500"></div>
                      <span>Gestionar reservas</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-4 h-4 rounded-full mr-2 bg-green-500"></div>
                      <span>Gestionar grupos</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-4 h-4 rounded-full mr-2 bg-green-500"></div>
                      <span>Ver estadísticas</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps<{
  agencyUser: {
    id: number;
    name: string;
    email: string;
    role: string;
  };
  agency: {
    id: number;
    name: string;
  };
  isAgencyAdmin: boolean;
}>();
</script>
