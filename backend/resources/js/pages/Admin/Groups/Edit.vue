<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

const props = defineProps<{
  group: {
    id: number;
    name: string;
    description: string | null;
    type: string;
    number_of_people: number;
    group_members: string[] | null;
    agency_id: number | null;
    created_by: number;
    is_active: boolean;
  };
  agencies: Array<{
    id: number;
    name: string;
  }>;
  users: Array<{
    id: number;
    name: string;
    email: string;
  }>;
  groupTypes: Record<string, string>;
  groupUserIds: number[];
  groupGuideIds: number[];
  isAgencyUser: boolean;
  isSuperAdmin: boolean;
  userAgencyId: number | null;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Grupos',
    href: '/dashboard/groups',
  },
  {
    title: 'Editar Grupo',
    href: `/dashboard/groups/${props.group.id}/edit`,
  },
];

const form = useForm({
  name: props.group.name,
  description: props.group.description || '',
  type: props.group.type,
  number_of_people: props.group.number_of_people || 1,
  group_members: props.group.group_members || [],
  agency_id: props.group.agency_id,
  users: props.groupUserIds,
  guides: props.groupGuideIds,
  is_active: props.group.is_active,
  _method: 'PUT',
});

const selectedUsers = ref<number[]>(props.groupUserIds);
const selectedGuides = ref<number[]>(props.groupGuideIds);
const groupMembers = ref<string[]>(props.group.group_members || []);
const newMember = ref('');

// Update form values when selections change
const updateFormUsers = () => {
  form.users = selectedUsers.value;
};

const updateFormGuides = () => {
  form.guides = selectedGuides.value;
};

const addGroupMember = () => {
  if (newMember.value.trim()) {
    groupMembers.value.push(newMember.value.trim());
    form.group_members = groupMembers.value;
    newMember.value = '';
  }
};

const removeGroupMember = (index: number) => {
  groupMembers.value.splice(index, 1);
  form.group_members = groupMembers.value;
};

const submit = () => {
  // Make sure users and guides are updated
  updateFormUsers();
  updateFormGuides();

  form.post(`/dashboard/groups/${props.group.id}`, {
    preserveScroll: true,
  });
};
</script>

<template>
  <Head title="Editar Grupo" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Editar Grupo</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/groups/${group.id}`"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Ver Detalles
          </Link>
          <Link
            href="/dashboard/groups"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver a Grupos
          </Link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Group Name -->
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Nombre del Grupo *
              </label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</div>
            </div>

            <!-- Group Type -->
            <div>
              <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tipo de Grupo *
              </label>
              <select
                id="type"
                v-model="form.type"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option v-for="(label, value) in groupTypes" :key="value" :value="value">
                  {{ label }}
                </option>
              </select>
              <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
            </div>

            <!-- Number of People -->
            <div>
              <label for="number_of_people" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Número de Personas *
              </label>
              <input
                id="number_of_people"
                v-model="form.number_of_people"
                type="number"
                min="1"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <div v-if="form.errors.number_of_people" class="text-red-500 text-sm mt-1">{{ form.errors.number_of_people }}</div>
            </div>

            <!-- Agency (only for superadmins) -->
            <div v-if="isSuperAdmin">
              <label for="agency_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Operador/Agencia
              </label>
              <select
                id="agency_id"
                v-model="form.agency_id"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option :value="null">Sin agencia</option>
                <option v-for="agency in agencies" :key="agency.id" :value="agency.id">
                  {{ agency.name }}
                </option>
              </select>
              <div v-if="form.errors.agency_id" class="text-red-500 text-sm mt-1">{{ form.errors.agency_id }}</div>
            </div>

            <!-- Active Status -->
            <div>
              <div class="flex items-center">
                <input
                  id="is_active"
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Grupo Activo
                </label>
              </div>
              <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">{{ form.errors.is_active }}</div>
            </div>

            <!-- Description -->
            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Descripción
              </label>
              <textarea
                id="description"
                v-model="form.description"
                rows="4"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>

            <!-- Group Members -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Miembros del Grupo (sin cuenta de usuario)
              </label>
              <div class="border border-gray-300 dark:border-gray-600 rounded-md p-4">
                <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                  Añade los nombres de las personas que formarán parte del grupo pero no tienen cuenta de usuario:
                </p>

                <div class="flex mb-2">
                  <input
                    v-model="newMember"
                    type="text"
                    placeholder="Nombre del miembro"
                    class="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    @keyup.enter="addGroupMember"
                  />
                  <button
                    type="button"
                    @click="addGroupMember"
                    class="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  >
                    Añadir
                  </button>
                </div>

                <div v-if="groupMembers.length > 0" class="mt-3">
                  <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Miembros añadidos:</p>
                  <ul class="space-y-1">
                    <li v-for="(member, index) in groupMembers" :key="index" class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded">
                      <span class="text-gray-800 dark:text-gray-200">{{ member }}</span>
                      <button
                        type="button"
                        @click="removeGroupMember(index)"
                        class="text-red-500 hover:text-red-700 dark:hover:text-red-400 focus:outline-none"
                      >
                        Eliminar
                      </button>
                    </li>
                  </ul>
                </div>

                <div v-else class="mt-3 text-sm text-gray-500 dark:text-gray-400 italic">
                  No hay miembros añadidos
                </div>
              </div>
              <div v-if="form.errors.group_members" class="text-red-500 text-sm mt-1">{{ form.errors.group_members }}</div>
            </div>

            <!-- Users Selection -->
            <div class="md:col-span-2">
              <label for="users" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Usuarios del Grupo (opcional)
              </label>
              <div class="border border-gray-300 dark:border-gray-600 rounded-md p-4">
                <div class="mb-4">
                  <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    Selecciona los usuarios que formarán parte de este grupo:
                  </p>
                  <select
                    id="users"
                    v-model="selectedUsers"
                    multiple
                    class="w-full h-40 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    @change="updateFormUsers"
                  >
                    <option v-for="user in users" :key="user.id" :value="user.id">
                      {{ user.name }} ({{ user.email }})
                    </option>
                  </select>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Mantener pulsado Ctrl (Windows) o Command (Mac) para seleccionar varios usuarios
                  </p>
                </div>

                <div>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    De los usuarios seleccionados, ¿quiénes serán guías?
                  </p>
                  <select
                    id="guides"
                    v-model="selectedGuides"
                    multiple
                    class="w-full h-40 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    @change="updateFormGuides"
                  >
                    <option
                      v-for="user in users.filter(u => selectedUsers.includes(u.id))"
                      :key="user.id"
                      :value="user.id"
                    >
                      {{ user.name }} ({{ user.email }})
                    </option>
                  </select>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Los guías tienen permisos especiales dentro del grupo
                  </p>
                </div>
              </div>
              <div v-if="form.errors.users" class="text-red-500 text-sm mt-1">{{ form.errors.users }}</div>
              <div v-if="form.errors.guides" class="text-red-500 text-sm mt-1">{{ form.errors.guides }}</div>
            </div>
          </div>

          <div class="flex justify-end mt-6">
            <button
              type="button"
              class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 mr-2"
              @click="$inertia.visit('/dashboard/groups')"
            >
              Cancelar
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              :disabled="form.processing"
            >
              {{ form.processing ? 'Guardando...' : 'Actualizar Grupo' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
