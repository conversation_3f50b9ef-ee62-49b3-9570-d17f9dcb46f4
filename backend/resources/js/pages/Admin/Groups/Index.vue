<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import { Users, Calendar, Building2, User, UserCircle } from 'lucide-vue-next';

const props = defineProps<{
  groups: {
    data: Array<{
      id: number;
      name: string;
      description: string | null;
      type: string;
      number_of_people: number;
      agency: {
        id: number;
        name: string;
      } | null;
      creator: {
        id: number;
        name: string;
      } | null;
      users_count: number;
      is_active: boolean;
      created_at: string;
    }>;
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    meta: {
      current_page: number;
      from: number;
      last_page: number;
      links: Array<{
        url: string | null;
        label: string;
        active: boolean;
      }>;
      path: string;
      per_page: number;
      to: number;
      total: number;
    };
  };
  agencies: Array<{
    id: number;
    name: string;
  }>;
  groupTypes: Array<string>;
  filters: {
    type: string;
    agency_id: number | null;
  };
  isAgencyUser: boolean;
  isSuperAdmin: boolean;
  userAgencyId: number | null;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Grupos',
    href: '/dashboard/groups',
  },
];

// Debug: Log the groups data to see what's being received
onMounted(() => {
  console.log('Groups data:', props.groups.data);
  // Check if users_count is present in the data
  if (props.groups.data.length > 0) {
    console.log('First group users_count:', props.groups.data[0].users_count);
    console.log('First group:', props.groups.data[0]);
  }
});

// Group type translations
const groupTypeLabels: Record<string, string> = {
  'couple': 'Pareja',
  'family': 'Familia',
  'pets': 'Con mascotas',
  'mixed': 'Mixto',
  'other': 'Otro'
};

// Filter values
const typeFilter = ref(props.filters.type || 'all');
const agencyFilter = ref(props.filters.agency_id || null);

// Apply filters
const applyFilters = () => {
  const params: Record<string, string> = {};

  if (typeFilter.value !== 'all') {
    params.type = typeFilter.value;
  }

  if (agencyFilter.value) {
    params.agency_id = agencyFilter.value.toString();
  }

  window.location.href = `/dashboard/groups?${new URLSearchParams(params).toString()}`;
};

// Reset filters
const resetFilters = () => {
  typeFilter.value = 'all';
  agencyFilter.value = null;
  window.location.href = '/dashboard/groups';
};

// Delete group confirmation
const deleteGroup = (groupId: number) => {
  if (confirm('¿Estás seguro de que quieres eliminar este grupo? Esta acción no se puede deshacer.')) {
    window.location.href = `/dashboard/groups/${groupId}?_method=DELETE`;
  }
};
</script>

<template>
  <Head title="Gestión de Grupos" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Gestión de Grupos</h1>
        <Link
          href="/dashboard/groups/create"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Crear Grupo
        </Link>
      </div>

      <!-- Filters -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Filtros</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label for="type-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tipo de Grupo
            </label>
            <select
              id="type-filter"
              v-model="typeFilter"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">Todos los tipos</option>
              <option v-for="type in groupTypes" :key="type" :value="type">
                {{ groupTypeLabels[type] || type }}
              </option>
            </select>
          </div>

          <div v-if="isSuperAdmin">
            <label for="agency-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Operador/Agencia
            </label>
            <select
              id="agency-filter"
              v-model="agencyFilter"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option :value="null">Todas las agencias</option>
              <option v-for="agency in agencies" :key="agency.id" :value="agency.id">
                {{ agency.name }}
              </option>
            </select>
          </div>

          <div class="flex items-end">
            <button
              @click="applyFilters"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-2"
            >
              Aplicar Filtros
            </button>
            <button
              @click="resetFilters"
              class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Restablecer
            </button>
          </div>
        </div>
      </div>

      <!-- Groups List -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div v-if="groups.data.length > 0">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Nombre
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Tipo
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Personas
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Usuarios
                </th>
                <th
                  v-if="isSuperAdmin"
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Operador/Agencia
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Estado
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="group in groups.data" :key="group.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  {{ group.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {{ groupTypeLabels[group.type] || group.type }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  <div class="flex items-center">
                    <Users class="h-4 w-4 mr-1" />
                    {{ group.number_of_people || 1 }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  <div class="flex items-center">
                    <User class="h-4 w-4 mr-1" />
                    {{ group.users_count }}
                  </div>
                </td>
                <td
                  v-if="isSuperAdmin"
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"
                >
                  <div v-if="group.agency" class="flex items-center">
                    <Building2 class="h-4 w-4 mr-1" />
                    {{ group.agency.name }}
                  </div>
                  <div v-else class="text-gray-400">
                    Sin agencia
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="group.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  >
                    {{ group.is_active ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <Link
                    :href="`/dashboard/groups/${group.id}`"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                  >
                    Ver
                  </Link>
                  <Link
                    :href="`/dashboard/groups/${group.id}/edit`"
                    class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3"
                  >
                    Editar
                  </Link>
                  <Link
                    :href="`/dashboard/groups/${group.id}/manage-users`"
                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"
                  >
                    Usuarios
                  </Link>
                  <button
                    @click="deleteGroup(group.id)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Eliminar
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="p-6 text-center text-gray-500 dark:text-gray-400">
          No hay grupos disponibles. Crea un nuevo grupo para empezar.
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="groups.data.length > 0" class="flex justify-center mt-4">
        <nav class="flex items-center">
          <template v-for="(link, index) in groups.links" :key="index">
            <div
              v-if="link.url === null"
              class="px-3 py-1 text-gray-500 dark:text-gray-400"
              v-html="link.label"
            ></div>
            <Link
              v-else
              :href="link.url"
              class="px-3 py-1 rounded"
              :class="[
                link.active
                  ? 'bg-blue-500 text-white'
                  : 'text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900',
              ]"
              v-html="link.label"
            ></Link>
          </template>
        </nav>
      </div>
    </div>
  </AppLayout>
</template>
