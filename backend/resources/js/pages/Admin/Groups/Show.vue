<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Users, Calendar, Building2, User, Clock, MapPin } from 'lucide-vue-next';

const props = defineProps<{
  group: {
    id: number;
    name: string;
    description: string | null;
    type: string;
    number_of_people: number;
    group_members: string[] | null;
    agency: {
      id: number;
      name: string;
    } | null;
    creator: {
      id: number;
      name: string;
    } | null;
    users: Array<{
      id: number;
      name: string;
      email: string;
      pivot: {
        is_guide: boolean;
      };
    }>;
    reservations: Array<{
      id: number;
      experience: {
        id: number;
        title: string;
        type: string;
      };
      date: string;
      time_slot: string;
      status: string;
    }>;
    is_active: boolean;
    created_at: string;
  };
  isAgencyUser: boolean;
  isSuperAdmin: boolean;
  userAgencyId: number | null;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Grupos',
    href: '/dashboard/groups',
  },
  {
    title: props.group.name,
    href: `/dashboard/groups/${props.group.id}`,
  },
];

// Group type translations
const groupTypeLabels: Record<string, string> = {
  'couple': 'Pareja',
  'family': 'Familia',
  'pets': 'Con mascotas',
  'mixed': 'Mixto',
  'other': 'Otro'
};

// Reservation status translations
const reservationStatusLabels: Record<string, string> = {
  'pending': 'Pendiente',
  'confirmed': 'Confirmada',
  'cancelled': 'Cancelada',
  'completed': 'Completada'
};

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Delete group confirmation
const deleteGroup = () => {
  if (confirm('¿Estás seguro de que quieres eliminar este grupo? Esta acción no se puede deshacer.')) {
    window.location.href = `/dashboard/groups/${props.group.id}?_method=DELETE`;
  }
};
</script>

<template>
  <Head :title="'Grupo: ' + group.name" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Detalles del Grupo</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/groups/${group.id}/edit`"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Editar
          </Link>
          <Link
            :href="`/dashboard/groups/${group.id}/manage-users`"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Gestionar Usuarios
          </Link>
          <button
            @click="deleteGroup"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Eliminar
          </button>
          <Link
            href="/dashboard/groups"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver a Grupos
          </Link>
        </div>
      </div>

      <!-- Group Information -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Información del Grupo</h2>

            <div class="space-y-3">
              <div>
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Nombre:</span>
                <p class="text-gray-900 dark:text-white">{{ group.name }}</p>
              </div>

              <div>
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Tipo:</span>
                <p class="text-gray-900 dark:text-white">{{ groupTypeLabels[group.type] || group.type }}</p>
              </div>

              <div>
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Número de Personas:</span>
                <p class="text-gray-900 dark:text-white flex items-center">
                  <Users class="h-5 w-5 mr-2 text-blue-500" />
                  <span class="text-lg font-semibold">{{ group.number_of_people }}</span>
                </p>
              </div>

              <div v-if="group.agency">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Operador/Agencia:</span>
                <p class="text-gray-900 dark:text-white flex items-center">
                  <Building2 class="h-4 w-4 mr-1" />
                  {{ group.agency.name }}
                </p>
              </div>

              <div>
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Estado:</span>
                <p class="text-gray-900 dark:text-white">
                  <span
                    class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="group.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  >
                    {{ group.is_active ? 'Activo' : 'Inactivo' }}
                  </span>
                </p>
              </div>

              <div>
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Fecha de Creación:</span>
                <p class="text-gray-900 dark:text-white">{{ formatDate(group.created_at) }}</p>
              </div>

              <div v-if="group.creator">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Creado por:</span>
                <p class="text-gray-900 dark:text-white flex items-center">
                  <User class="h-4 w-4 mr-1" />
                  {{ group.creator.name }}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Descripción</h2>
            <p class="text-gray-700 dark:text-gray-300">
              {{ group.description || 'No hay descripción disponible.' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Group Members -->
      <div v-if="group.group_members && group.group_members.length > 0" class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden mb-4">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              Miembros del Grupo sin Cuenta ({{ group.group_members.length }})
            </h2>
            <div class="flex items-center bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded-full">
              <Users class="h-5 w-5 mr-1 text-blue-600 dark:text-blue-400" />
              <span class="text-blue-700 dark:text-blue-300 font-medium">{{ group.number_of_people }} personas en total</span>
            </div>
          </div>
        </div>

        <div class="p-6">
          <ul class="space-y-2">
            <li v-for="(member, index) in group.group_members" :key="index" class="flex items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <User class="h-5 w-5 mr-2 text-gray-500 dark:text-gray-400" />
              <span class="text-gray-900 dark:text-white">{{ member }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Group Users -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              Usuarios del Grupo con Cuenta ({{ group.users.length }})
            </h2>
            <div v-if="!group.group_members || group.group_members.length === 0" class="flex items-center bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded-full">
              <Users class="h-5 w-5 mr-1 text-blue-600 dark:text-blue-400" />
              <span class="text-blue-700 dark:text-blue-300 font-medium">{{ group.number_of_people }} personas en total</span>
            </div>
            <Link
              :href="`/dashboard/groups/${group.id}/manage-users`"
              class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Gestionar Usuarios
            </Link>
          </div>
        </div>

        <div v-if="group.users.length > 0">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Nombre
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Email
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Rol
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="user in group.users" :key="user.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  {{ user.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {{ user.email }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="user.pivot.is_guide ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'"
                  >
                    {{ user.pivot.is_guide ? 'Guía' : 'Miembro' }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="p-6 text-center text-gray-500 dark:text-gray-400">
          No hay usuarios asignados a este grupo.
        </div>
      </div>

      <!-- Group Reservations -->
      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Reservas del Grupo ({{ group.reservations.length }})
          </h2>
        </div>

        <div v-if="group.reservations.length > 0">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Experiencia
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Tipo
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Fecha
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Hora
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Estado
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="reservation in group.reservations" :key="reservation.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  {{ reservation.experience.title }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {{ reservation.experience.type }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  <div class="flex items-center">
                    <Calendar class="h-4 w-4 mr-1" />
                    {{ formatDate(reservation.date) }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  <div class="flex items-center">
                    <Clock class="h-4 w-4 mr-1" />
                    {{ reservation.time_slot }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-yellow-100 text-yellow-800': reservation.status === 'pending',
                      'bg-green-100 text-green-800': reservation.status === 'confirmed',
                      'bg-red-100 text-red-800': reservation.status === 'cancelled',
                      'bg-blue-100 text-blue-800': reservation.status === 'completed',
                    }"
                  >
                    {{ reservationStatusLabels[reservation.status] || reservation.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <Link
                    :href="`/dashboard/reservations/${reservation.id}`"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Ver Detalles
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="p-6 text-center text-gray-500 dark:text-gray-400">
          Este grupo no tiene reservas.
        </div>
      </div>
    </div>
  </AppLayout>
</template>
