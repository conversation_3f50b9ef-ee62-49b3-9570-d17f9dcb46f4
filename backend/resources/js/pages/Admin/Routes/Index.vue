<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { MapPin, Clock, Ruler, Activity, Star, Eye, EyeOff, Building } from 'lucide-vue-next';

const props = defineProps<{
  routes: Array<{
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    image: string | null;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    agency: {
      id: number;
      name: string;
    } | null;
    points: Array<{
      id: number;
      location: {
        id: number;
        name: string;
      } | null;
    }>;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }>;
  isAgencyUser?: boolean;
  isSuperAdmin?: boolean;
  userAgencyId?: number;
  sort?: string;
  direction?: string;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Rutas',
    href: '/dashboard/routes',
  },
];

const searchQuery = ref('');

const filteredRoutes = computed(() => {
  // Ensure routes is an array
  let routes = [];

  // Check if props.routes is an array
  if (Array.isArray(props.routes)) {
    routes = props.routes;
  }
  // Check if props.routes is an object with a collection property
  else if (props.routes && typeof props.routes === 'object' && props.routes.Illuminate$Database$Eloquent$Collection) {
    routes = props.routes.Illuminate$Database$Eloquent$Collection;
  }
  // Check if it's just a plain object
  else if (props.routes && typeof props.routes === 'object') {
    // Try to convert the object to an array
    routes = Object.values(props.routes);

    // If the first item is an array, use that
    if (Array.isArray(routes[0])) {
      routes = routes[0];
    }
  }

  console.log('Routes for filtering:', routes);

  if (!searchQuery.value) return routes;

  const query = searchQuery.value.toLowerCase();
  return routes.filter(route =>
    (route.title && route.title.toLowerCase().includes(query)) ||
    (route.description && route.description.toLowerCase().includes(query))
  );
});

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

// Sorting functions
const currentSort = ref(props.sort || 'created_at');
const currentDirection = ref(props.direction || 'desc');

const sortOptions = [
  { value: 'title', label: 'Título' },
  { value: 'created_at', label: 'Fecha de creación' },
  { value: 'is_active', label: 'Estado' },
];

// Add agency name sorting option only for superadmins
if (props.isSuperAdmin) {
  sortOptions.push({ value: 'agency_name', label: 'Agencia' });
}

const sort = (field: string) => {
  try {
    // If clicking the same field, toggle direction
    const direction = field === currentSort.value && currentDirection.value === 'asc' ? 'desc' : 'asc';

    // Make sure route function exists
    if (typeof route !== 'function') {
      console.error('Route function is not available');
      return;
    }

    // Update the URL with the new sort parameters
    router.get(
      route('admin.routes.index'),
      { sort: field, direction },
      { preserveState: true, preserveScroll: true }
    );

    // Update local state
    currentSort.value = field;
    currentDirection.value = direction;
  } catch (error) {
    console.error('Error sorting routes:', error);
  }
};

const getSortIcon = (field: string) => {
  if (currentSort.value !== field) return '';
  return currentDirection.value === 'asc' ? '↑' : '↓';
};
</script>

<template>
  <AppLayout>
    <Head title="Rutas" />

    <!-- Debug information -->
    <div class="bg-yellow-100 p-4 mb-4 rounded">
      <h2 class="font-bold">Debug Information</h2>
      <p>Routes count: {{ filteredRoutes.length }}</p>
      <p>Is agency user: {{ isAgencyUser }}</p>
      <p>Agency ID: {{ userAgencyId }}</p>
      <p>Is superadmin: {{ isSuperAdmin }}</p>
    </div>

    <div class="container py-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">Rutas</h1>
          <p class="mt-1 text-sm text-gray-600">Gestiona las rutas turísticas de la aplicación</p>
        </div>
        <Link
          :href="route('admin.routes.create')"
          class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          <span>Crear Ruta</span>
        </Link>
      </div>

      <!-- No routes message -->
      <div v-if="filteredRoutes.length === 0" class="bg-white rounded-lg shadow p-8 text-center">
        <div class="text-gray-500 mb-4">
          <MapPin class="h-12 w-12 mx-auto" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No hay rutas disponibles</h3>
        <p class="text-gray-600 mb-4">No se encontraron rutas para mostrar. Puedes crear una nueva ruta haciendo clic en el botón "Crear Ruta".</p>
        <Link
          :href="route('admin.routes.create')"
          class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          <span>Crear Ruta</span>
        </Link>
      </div>

      <!-- Routes table -->
      <div v-else class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 border-b">
          <div class="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
            <!-- Search Box -->
            <div class="relative flex-grow">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 sm:text-sm">
                  <i class="pi pi-search"></i>
                </span>
              </div>
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Buscar rutas..."
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              />
            </div>

            <!-- Sort Dropdown -->
            <div class="flex items-center space-x-2">
              <label for="sort-select" class="text-sm font-medium text-gray-700">Ordenar por:</label>
              <select
                id="sort-select"
                v-model="currentSort"
                @change="sort(currentSort)"
                class="block w-full md:w-auto pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
              >
                <option v-for="option in sortOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>

              <!-- Direction Toggle -->
              <button
                @click="sort(currentSort)"
                class="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <span v-if="currentDirection === 'asc'">↑</span>
                <span v-else>↓</span>
              </button>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sort('title')">
                  Ruta {{ getSortIcon('title') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Detalles
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sort('agency_name')">
                  Agencia {{ getSortIcon('agency_name') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Puntos
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sort('is_active')">
                  Estado {{ getSortIcon('is_active') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sort('created_at')">
                  Fecha {{ getSortIcon('created_at') }}
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="route in filteredRoutes" :key="route.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img
                        v-if="route.image"
                        :src="`/storage/${route.image}`"
                        :alt="route.title"
                        class="h-10 w-10 rounded-md object-cover"
                      />
                      <div v-else class="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                        <MapPin class="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="flex items-center">
                        <div class="text-sm font-medium text-gray-900">{{ route.title }}</div>
                        <Star v-if="route.is_featured" class="ml-1 h-4 w-4 text-amber-500" />
                      </div>
                      <div class="text-sm text-gray-500 max-w-md truncate">
                        {{ route.short_description || route.description }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col space-y-1">
                    <div v-if="route.duration" class="flex items-center text-sm text-gray-500">
                      <Clock class="mr-1 h-4 w-4 text-gray-400" />
                      <span>{{ route.duration }}</span>
                    </div>
                    <div v-if="route.distance" class="flex items-center text-sm text-gray-500">
                      <Ruler class="mr-1 h-4 w-4 text-gray-400" />
                      <span>{{ route.distance }}</span>
                    </div>
                    <div v-if="route.difficulty" class="flex items-center text-sm text-gray-500">
                      <Activity class="mr-1 h-4 w-4 text-gray-400" />
                      <span>{{ route.difficulty }}</span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="route.agency" class="flex items-center text-sm text-gray-500">
                    <Building class="mr-1 h-4 w-4 text-gray-400" />
                    <span>{{ route.agency.name }}</span>
                  </div>
                  <div v-else class="text-sm text-gray-500">-</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ route.points && route.points.length ? route.points.length : 0 }} puntos</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div v-if="route.is_active" class="flex items-center text-green-600">
                      <Eye class="mr-1 h-4 w-4" />
                      <span>Activo</span>
                    </div>
                    <div v-else class="flex items-center text-gray-500">
                      <EyeOff class="mr-1 h-4 w-4" />
                      <span>Inactivo</span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(route.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-2">
                    <Link
                      :href="route('admin.routes.show', route.id)"
                      class="text-primary hover:text-primary/80"
                    >
                      Ver
                    </Link>
                    <Link
                      v-if="isSuperAdmin || (isAgencyUser && route.agency && route.agency.id === userAgencyId)"
                      :href="route('admin.routes.edit', route.id)"
                      class="text-amber-600 hover:text-amber-800"
                    >
                      Editar
                    </Link>
                    <Link
                      v-if="isSuperAdmin"
                      :href="route('admin.routes.destroy', route.id)"
                      method="delete"
                      as="button"
                      type="button"
                      class="text-red-600 hover:text-red-800"
                      confirm="¿Estás seguro de que deseas eliminar esta ruta?"
                      confirm-button="Sí, eliminar"
                      cancel-button="No, cancelar"
                    >
                      Eliminar
                    </Link>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredRoutes.length === 0">
                <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                  No se encontraron rutas
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
