<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { MapPin, Plus, Trash2, ArrowUpDown } from 'lucide-vue-next';

const props = defineProps<{
  agencies: Array<{
    id: number;
    name: string;
  }>;
  locations: Array<{
    id: number;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  }>;
  userAgencyId?: number;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Rutas',
    href: '/dashboard/routes',
  },
  {
    title: 'Crear',
    href: '/dashboard/routes/create',
  },
];

const form = useForm({
  title: '',
  description: '',
  short_description: '',
  image: null as File | null,
  duration: '',
  distance: '',
  difficulty: '',
  agency_id: props.userAgencyId || '' as string | number,
  is_featured: false,
  is_active: true,
  points: [] as Array<{
    location_id: string | number;
    order: number;
    description: string;
    image: File | null;
  }>,
});

const addPoint = () => {
  form.points.push({
    location_id: '',
    order: form.points.length,
    description: '',
    image: null,
  });
};

const removePoint = (index: number) => {
  form.points.splice(index, 1);

  // Update order of remaining points
  form.points.forEach((point, i) => {
    point.order = i;
  });
};

const movePointUp = (index: number) => {
  if (index === 0) return;

  const temp = form.points[index];
  form.points[index] = form.points[index - 1];
  form.points[index - 1] = temp;

  // Update order
  form.points.forEach((point, i) => {
    point.order = i;
  });
};

const movePointDown = (index: number) => {
  if (index === form.points.length - 1) return;

  const temp = form.points[index];
  form.points[index] = form.points[index + 1];
  form.points[index + 1] = temp;

  // Update order
  form.points.forEach((point, i) => {
    point.order = i;
  });
};

const handleImageChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form.image = input.files[0];
  }
};

const handlePointImageChange = (event: Event, index: number) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form.points[index].image = input.files[0];
  }
};

const submit = () => {
  form.post(route('admin.routes.store'), {
    preserveScroll: true,
    onSuccess: () => {
      // Reset form after successful submission
      form.reset();
    },
  });
};
</script>

<template>
  <AppLayout :breadcrumbs="breadcrumbs">
    <Head title="Crear Ruta" />

    <div class="container py-8 px-6">
      <div class="mb-6 pl-2">
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Crear Ruta</h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Añade una nueva ruta turística a la aplicación</p>
      </div>

      <form @submit.prevent="submit" class="space-y-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Información General</h2>
          </div>

          <div class="p-6 space-y-6">
            <!-- Title -->
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Título *</label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
                required
              />
              <div v-if="form.errors.title" class="mt-1 text-sm text-red-600">{{ form.errors.title }}</div>
            </div>

            <!-- Short Description -->
            <div>
              <label for="short_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción Corta</label>
              <input
                id="short_description"
                v-model="form.short_description"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
              />
              <div v-if="form.errors.short_description" class="mt-1 text-sm text-red-600">{{ form.errors.short_description }}</div>
            </div>

            <!-- Description -->
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción Completa *</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="4"
                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
                required
              ></textarea>
              <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">{{ form.errors.description }}</div>
            </div>

            <!-- Image -->
            <div>
              <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagen</label>
              <input
                id="image"
                type="file"
                @change="handleImageChange"
                accept="image/*"
                class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90 dark:file:bg-primary/80"
              />
              <div v-if="form.errors.image" class="mt-1 text-sm text-red-600">{{ form.errors.image }}</div>
            </div>

            <!-- Details Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Duration -->
              <div>
                <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Duración</label>
                <input
                  id="duration"
                  v-model="form.duration"
                  type="text"
                  placeholder="Ej: 2 horas, 3 días"
                  class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                />
                <div v-if="form.errors.duration" class="mt-1 text-sm text-red-600">{{ form.errors.duration }}</div>
              </div>

              <!-- Distance -->
              <div>
                <label for="distance" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Distancia</label>
                <input
                  id="distance"
                  v-model="form.distance"
                  type="text"
                  placeholder="Ej: 5 km"
                  class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                />
                <div v-if="form.errors.distance" class="mt-1 text-sm text-red-600">{{ form.errors.distance }}</div>
              </div>

              <!-- Difficulty -->
              <div>
                <label for="difficulty" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dificultad</label>
                <select
                  id="difficulty"
                  v-model="form.difficulty"
                  class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Seleccionar dificultad</option>
                  <option value="Fácil">Fácil</option>
                  <option value="Moderada">Moderada</option>
                  <option value="Difícil">Difícil</option>
                </select>
                <div v-if="form.errors.difficulty" class="mt-1 text-sm text-red-600">{{ form.errors.difficulty }}</div>
              </div>
            </div>

            <!-- Agency -->
            <div>
              <label for="agency_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Agencia</label>
              <select
                id="agency_id"
                v-model="form.agency_id"
                :disabled="!!props.userAgencyId"
                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Seleccionar agencia</option>
                <option v-for="agency in agencies" :key="agency.id" :value="agency.id">
                  {{ agency.name }}
                </option>
              </select>
              <div v-if="props.userAgencyId" class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Como usuario de agencia, solo puedes crear rutas para tu propia agencia.
              </div>
              <div v-if="form.errors.agency_id" class="mt-1 text-sm text-red-600">{{ form.errors.agency_id }}</div>
            </div>

            <!-- Status Options -->
            <div class="flex space-x-6">
              <div class="flex items-center">
                <input
                  id="is_featured"
                  v-model="form.is_featured"
                  type="checkbox"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label for="is_featured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Destacada</label>
              </div>
              <div class="flex items-center">
                <input
                  id="is_active"
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Activa</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Route Points -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Puntos de la Ruta</h2>
            <button
              type="button"
              @click="addPoint"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <Plus class="h-4 w-4 mr-1" />
              Añadir Punto
            </button>
          </div>

          <div class="p-6">
            <div v-if="form.points.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
              <MapPin class="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500" />
              <p class="mt-2">No hay puntos en esta ruta. Añade al menos un punto.</p>
            </div>

            <div v-else class="space-y-6">
              <div
                v-for="(point, index) in form.points"
                :key="index"
                class="border border-gray-200 dark:border-gray-700 rounded-md p-4 dark:bg-gray-750"
              >
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Punto {{ index + 1 }}</h3>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      @click="movePointUp(index)"
                      class="p-1 text-gray-500 hover:text-gray-700"
                      :disabled="index === 0"
                      :class="{ 'opacity-50 cursor-not-allowed': index === 0 }"
                    >
                      <ArrowUpDown class="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      @click="removePoint(index)"
                      class="p-1 text-red-500 hover:text-red-700"
                    >
                      <Trash2 class="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div class="space-y-4">
                  <!-- Location -->
                  <div>
                    <label :for="`point-${index}-location`" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ubicación *</label>
                    <select
                      :id="`point-${index}-location`"
                      v-model="point.location_id"
                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
                      required
                    >
                      <option value="">Seleccionar ubicación</option>
                      <option v-for="location in locations" :key="location.id" :value="location.id">
                        {{ location.name }} - {{ location.address }}
                      </option>
                    </select>
                    <div v-if="form.errors[`points.${index}.location_id`]" class="mt-1 text-sm text-red-600">
                      {{ form.errors[`points.${index}.location_id`] }}
                    </div>
                  </div>

                  <!-- Description -->
                  <div>
                    <label :for="`point-${index}-description`" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descripción</label>
                    <textarea
                      :id="`point-${index}-description`"
                      v-model="point.description"
                      rows="2"
                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:bg-gray-700 dark:text-white"
                    ></textarea>
                    <div v-if="form.errors[`points.${index}.description`]" class="mt-1 text-sm text-red-600">
                      {{ form.errors[`points.${index}.description`] }}
                    </div>
                  </div>

                  <!-- Image -->
                  <div>
                    <label :for="`point-${index}-image`" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagen</label>
                    <input
                      :id="`point-${index}-image`"
                      type="file"
                      @change="(e) => handlePointImageChange(e, index)"
                      accept="image/*"
                      class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90 dark:file:bg-primary/80"
                    />
                    <div v-if="form.errors[`points.${index}.image`]" class="mt-1 text-sm text-red-600">
                      {{ form.errors[`points.${index}.image`] }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="form.processing"
            class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            <span v-if="form.processing">Guardando...</span>
            <span v-else>Guardar Ruta</span>
          </button>
        </div>
      </form>
    </div>
  </AppLayout>
</template>
