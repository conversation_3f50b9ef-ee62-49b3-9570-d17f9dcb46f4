<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { MapPin, Clock, Ruler, Activity, Star, Eye, EyeOff, Building } from 'lucide-vue-next';

const props = defineProps({
  routes: {
    type: Array,
    default: () => [],
  },
  isAgencyUser: {
    type: Boolean,
    default: false,
  },
  isSuperAdmin: {
    type: Boolean,
    default: false,
  },
  userAgencyId: {
    type: Number,
    default: null,
  },
  sort: {
    type: String,
    default: 'created_at',
  },
  direction: {
    type: String,
    default: 'desc',
  },
});

const breadcrumbs = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Rutas',
    href: '/dashboard/routes',
  },
];

const searchQuery = ref('');

const filteredRoutes = computed(() => {
  // Ensure routes is an array
  const routes = Array.isArray(props.routes) ? props.routes : [];
  
  if (!searchQuery.value) return routes;
  
  const query = searchQuery.value.toLowerCase();
  return routes.filter(route => 
    (route.title && route.title.toLowerCase().includes(query)) ||
    (route.description && route.description.toLowerCase().includes(query))
  );
});

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};
</script>

<template>
  <AppLayout :breadcrumbs="breadcrumbs">
    <Head title="Rutas" />

    <div class="container py-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">Rutas</h1>
          <p class="mt-1 text-sm text-gray-600">Gestiona las rutas turísticas de la aplicación</p>
        </div>
        <Link
          :href="route('admin.routes.create')"
          class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          <span>Crear Ruta</span>
        </Link>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-4 border-b">
          <div class="relative">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Buscar rutas..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gray-500 sm:text-sm">
                <i class="pi pi-search"></i>
              </span>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Título
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tipo
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ubicación
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Agencia
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Estado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Destacado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="route in filteredRoutes" :key="route.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ route.title }}
                  </div>
                  <div v-if="route.short_description" class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                    {{ route.short_description }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col space-y-1">
                    <div v-if="route.duration" class="flex items-center text-sm text-gray-500">
                      <Clock class="mr-1 h-4 w-4 text-gray-400" />
                      <span>{{ route.duration }}</span>
                    </div>
                    <div v-if="route.distance" class="flex items-center text-sm text-gray-500">
                      <Ruler class="mr-1 h-4 w-4 text-gray-400" />
                      <span>{{ route.distance }}</span>
                    </div>
                    <div v-if="route.difficulty" class="flex items-center text-sm text-gray-500">
                      <Activity class="mr-1 h-4 w-4 text-gray-400" />
                      <span>{{ route.difficulty }}</span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <div v-if="route.points && route.points.length > 0 && route.points[0].location">
                    {{ route.points[0].location.name }}
                    <span v-if="route.points.length > 1" class="text-xs text-gray-400">
                      (+ {{ route.points.length - 1 }} más)
                    </span>
                  </div>
                  <div v-else>No especificada</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <div v-if="route.agency" class="flex items-center">
                    <Building class="mr-1 h-4 w-4 text-gray-400" />
                    <span>{{ route.agency.name }}</span>
                  </div>
                  <div v-else>No especificada</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${route.is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`">
                    {{ route.is_active ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="route.is_featured" class="text-amber-500">
                    <Star class="h-5 w-5" />
                  </span>
                  <span v-else class="text-gray-400">
                    <Star class="h-5 w-5" />
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <Link
                      :href="route('admin.routes.show', route.id)"
                      class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Ver
                    </Link>
                    <Link
                      v-if="isSuperAdmin || (isAgencyUser && route.agency && route.agency.id === userAgencyId)"
                      :href="route('admin.routes.edit', route.id)"
                      class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                    >
                      Editar
                    </Link>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredRoutes.length === 0">
                <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No se encontraron rutas
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
