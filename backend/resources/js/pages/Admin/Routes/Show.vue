<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { MapPin, Clock, Ruler, Activity, Star, Eye, EyeOff, Building, Calendar } from 'lucide-vue-next';

const props = defineProps<{
  route: {
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    image: string | null;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    agency: {
      id: number;
      name: string;
      logo: string | null;
    } | null;
    points: Array<{
      id: number;
      location: {
        id: number;
        name: string;
        address: string;
        latitude: number;
        longitude: number;
        image: string | null;
      } | null;
      order: number;
      description: string | null;
      image: string | null;
    }>;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
  isAgencyUser?: boolean;
  isSuperAdmin?: boolean;
  userAgencyId?: number;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Rutas',
    href: '/dashboard/routes',
  },
  {
    title: props.route.title,
    href: `/dashboard/routes/${props.route.id}`,
  },
];

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};
</script>

<template>
  <AppLayout>
    <Head :title="route.title" />

    <div class="container py-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">{{ route.title }}</h1>
          <p class="mt-1 text-sm text-gray-600">{{ route.short_description }}</p>
        </div>
        <div class="flex space-x-2">
          <Link
            v-if="isSuperAdmin || (isAgencyUser && route.agency && route.agency.id === userAgencyId)"
            :href="route('admin.routes.edit', route.id)"
            class="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            <span>Editar</span>
          </Link>
          <Link
            v-if="isSuperAdmin"
            :href="route('admin.routes.destroy', route.id)"
            method="delete"
            as="button"
            type="button"
            class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            confirm="¿Estás seguro de que deseas eliminar esta ruta?"
            confirm-button="Sí, eliminar"
            cancel-button="No, cancelar"
          >
            <span>Eliminar</span>
          </Link>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
        <div class="p-6 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Información General</h2>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Image -->
            <div class="md:col-span-1">
              <div class="rounded-lg overflow-hidden h-64 bg-gray-100">
                <img
                  v-if="route.image"
                  :src="`/storage/${route.image}`"
                  :alt="route.title"
                  class="w-full h-full object-cover"
                />
                <div v-else class="w-full h-full flex items-center justify-center">
                  <MapPin class="h-12 w-12 text-gray-400" />
                </div>
              </div>
            </div>

            <!-- Details -->
            <div class="md:col-span-2 space-y-6">
              <!-- Status Badges -->
              <div class="flex flex-wrap gap-2">
                <div v-if="route.is_featured" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                  <Star class="mr-1 h-3 w-3" />
                  Destacada
                </div>
                <div v-if="route.is_active" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <Eye class="mr-1 h-3 w-3" />
                  Activa
                </div>
                <div v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <EyeOff class="mr-1 h-3 w-3" />
                  Inactiva
                </div>
              </div>

              <!-- Route Details -->
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div v-if="route.duration" class="flex items-center text-sm text-gray-600">
                  <Clock class="mr-2 h-5 w-5 text-gray-400" />
                  <span>{{ route.duration }}</span>
                </div>
                <div v-if="route.distance" class="flex items-center text-sm text-gray-600">
                  <Ruler class="mr-2 h-5 w-5 text-gray-400" />
                  <span>{{ route.distance }}</span>
                </div>
                <div v-if="route.difficulty" class="flex items-center text-sm text-gray-600">
                  <Activity class="mr-2 h-5 w-5 text-gray-400" />
                  <span>{{ route.difficulty }}</span>
                </div>
              </div>

              <!-- Agency -->
              <div v-if="route.agency" class="flex items-center space-x-3 p-3 bg-gray-50 rounded-md">
                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  <img
                    v-if="route.agency.logo"
                    :src="`/storage/${route.agency.logo}`"
                    :alt="route.agency.name"
                    class="h-full w-full object-cover"
                  />
                  <Building v-else class="h-5 w-5 text-gray-500" />
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-900">{{ route.agency.name }}</h3>
                  <p class="text-xs text-gray-500">Agencia</p>
                </div>
              </div>

              <!-- Dates -->
              <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                <div class="flex items-center">
                  <Calendar class="mr-1 h-4 w-4 text-gray-400" />
                  <span>Creada: {{ formatDate(route.created_at) }}</span>
                </div>
                <div class="flex items-center">
                  <Calendar class="mr-1 h-4 w-4 text-gray-400" />
                  <span>Actualizada: {{ formatDate(route.updated_at) }}</span>
                </div>
              </div>

              <!-- Description -->
              <div>
                <h3 class="text-sm font-medium text-gray-900 mb-2">Descripción</h3>
                <div class="prose prose-sm max-w-none text-gray-600">
                  <p>{{ route.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Route Points -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-6 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Puntos de la Ruta ({{ route.points && route.points.length ? route.points.length : 0 }})</h2>
        </div>

        <div class="p-6">
          <div v-if="!route.points || route.points.length === 0" class="text-center py-8 text-gray-500">
            <MapPin class="h-12 w-12 mx-auto text-gray-400" />
            <p class="mt-2">No hay puntos en esta ruta.</p>
          </div>

          <div v-else class="space-y-6">
            <div
              v-for="(point, index) in route.points"
              :key="point.id"
              class="border border-gray-200 rounded-md overflow-hidden"
            >
              <div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
                <h3 class="text-md font-medium text-gray-700">Punto {{ index + 1 }}</h3>
                <div class="text-sm text-gray-500">Orden: {{ point.order }}</div>
              </div>

              <div class="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Point Image -->
                <div class="md:col-span-1">
                  <div class="rounded-md overflow-hidden h-40 bg-gray-100">
                    <img
                      v-if="point.image"
                      :src="`/storage/${point.image}`"
                      alt="Imagen del punto"
                      class="w-full h-full object-cover"
                    />
                    <img
                      v-else-if="point.location && point.location.image"
                      :src="`/storage/${point.location.image}`"
                      :alt="point.location.name"
                      class="w-full h-full object-cover"
                    />
                    <div v-else class="w-full h-full flex items-center justify-center">
                      <MapPin class="h-8 w-8 text-gray-400" />
                    </div>
                  </div>
                </div>

                <!-- Point Details -->
                <div class="md:col-span-2 space-y-4">
                  <!-- Location -->
                  <div v-if="point.location" class="space-y-1">
                    <h4 class="text-sm font-medium text-gray-900">{{ point.location.name }}</h4>
                    <div class="flex items-start text-sm text-gray-600">
                      <MapPin class="mt-0.5 mr-1 h-4 w-4 text-gray-400 flex-shrink-0" />
                      <span>{{ point.location.address }}</span>
                    </div>
                    <div class="flex items-center text-xs text-gray-500">
                      <span>Lat: {{ point.location.latitude }}, Lng: {{ point.location.longitude }}</span>
                    </div>
                  </div>
                  <div v-else class="text-sm text-gray-500">No hay ubicación asociada</div>

                  <!-- Description -->
                  <div v-if="point.description" class="prose prose-sm max-w-none text-gray-600">
                    <p>{{ point.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
