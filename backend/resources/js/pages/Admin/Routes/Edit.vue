<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { MapPin, Plus, Trash2, ArrowUpDown } from 'lucide-vue-next';

const props = defineProps<{
  route: {
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    image: string | null;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    agency_id: number | null;
    is_featured: boolean;
    is_active: boolean;
    points: Array<{
      id: number;
      route_id: number;
      location_id: number | null;
      order: number;
      description: string | null;
      image: string | null;
    }>;
  };
  agencies: Array<{
    id: number;
    name: string;
  }>;
  locations: Array<{
    id: number;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  }>;
  isAgencyUser?: boolean;
  isSuperAdmin?: boolean;
  userAgencyId?: number;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Rutas',
    href: '/dashboard/routes',
  },
  {
    title: 'Editar',
    href: `/dashboard/routes/${props.route.id}/edit`,
  },
];

// Ensure route has all required properties
const routeData = props.route || {};
const routePoints = Array.isArray(routeData.points) ? routeData.points : [];

const form = useForm({
  title: routeData.title || '',
  description: routeData.description || '',
  short_description: routeData.short_description || '',
  image: null as File | null,
  duration: routeData.duration || '',
  distance: routeData.distance || '',
  difficulty: routeData.difficulty || '',
  agency_id: routeData.agency_id || '',
  is_featured: !!routeData.is_featured,
  is_active: routeData.is_active !== false, // Default to true if not explicitly false
  points: routePoints.map(point => ({
    id: point.id,
    location_id: point.location_id || '',
    order: point.order || 0,
    description: point.description || '',
    image: null as File | null,
  })),
  deleted_points: [] as number[],
});

const addPoint = () => {
  form.points.push({
    id: undefined,
    location_id: '',
    order: form.points.length,
    description: '',
    image: null,
  });
};

const removePoint = (index: number) => {
  const point = form.points[index];
  if (point.id) {
    form.deleted_points.push(point.id);
  }
  form.points.splice(index, 1);

  // Update order of remaining points
  form.points.forEach((point, i) => {
    point.order = i;
  });
};

const movePointUp = (index: number) => {
  if (index === 0) return;

  const temp = form.points[index];
  form.points[index] = form.points[index - 1];
  form.points[index - 1] = temp;

  // Update order
  form.points.forEach((point, i) => {
    point.order = i;
  });
};

const movePointDown = (index: number) => {
  if (index === form.points.length - 1) return;

  const temp = form.points[index];
  form.points[index] = form.points[index + 1];
  form.points[index + 1] = temp;

  // Update order
  form.points.forEach((point, i) => {
    point.order = i;
  });
};

const handleImageChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form.image = input.files[0];
  }
};

const handlePointImageChange = (event: Event, index: number) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form.points[index].image = input.files[0];
  }
};

const submit = () => {
  form.post(route('admin.routes.update', props.route.id), {
    method: 'put',
    preserveScroll: true,
  });
};
</script>

<template>
  <AppLayout>
    <Head title="Editar Ruta" />

    <div class="container py-8">
      <div class="mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">Editar Ruta</h1>
        <p class="mt-1 text-sm text-gray-600">Modifica la información de la ruta</p>
      </div>

      <form @submit.prevent="submit" class="space-y-8">
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Información General</h2>
          </div>

          <div class="p-6 space-y-6">
            <!-- Title -->
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700">Título *</label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                required
              />
              <div v-if="form.errors.title" class="mt-1 text-sm text-red-600">{{ form.errors.title }}</div>
            </div>

            <!-- Short Description -->
            <div>
              <label for="short_description" class="block text-sm font-medium text-gray-700">Descripción Corta</label>
              <input
                id="short_description"
                v-model="form.short_description"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
              />
              <div v-if="form.errors.short_description" class="mt-1 text-sm text-red-600">{{ form.errors.short_description }}</div>
            </div>

            <!-- Description -->
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700">Descripción Completa *</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="4"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                required
              ></textarea>
              <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">{{ form.errors.description }}</div>
            </div>

            <!-- Current Image -->
            <div v-if="props.route.image" class="flex items-center space-x-4">
              <div class="w-24 h-24 rounded-md overflow-hidden">
                <img :src="`/storage/${props.route.image}`" :alt="props.route.title" class="w-full h-full object-cover" />
              </div>
              <div class="text-sm text-gray-600">Imagen actual</div>
            </div>

            <!-- Image -->
            <div>
              <label for="image" class="block text-sm font-medium text-gray-700">
                {{ props.route.image ? 'Cambiar Imagen' : 'Imagen' }}
              </label>
              <input
                id="image"
                type="file"
                @change="handleImageChange"
                accept="image/*"
                class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90"
              />
              <div v-if="form.errors.image" class="mt-1 text-sm text-red-600">{{ form.errors.image }}</div>
            </div>

            <!-- Details Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Duration -->
              <div>
                <label for="duration" class="block text-sm font-medium text-gray-700">Duración</label>
                <input
                  id="duration"
                  v-model="form.duration"
                  type="text"
                  placeholder="Ej: 2 horas, 3 días"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                />
                <div v-if="form.errors.duration" class="mt-1 text-sm text-red-600">{{ form.errors.duration }}</div>
              </div>

              <!-- Distance -->
              <div>
                <label for="distance" class="block text-sm font-medium text-gray-700">Distancia</label>
                <input
                  id="distance"
                  v-model="form.distance"
                  type="text"
                  placeholder="Ej: 5 km"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                />
                <div v-if="form.errors.distance" class="mt-1 text-sm text-red-600">{{ form.errors.distance }}</div>
              </div>

              <!-- Difficulty -->
              <div>
                <label for="difficulty" class="block text-sm font-medium text-gray-700">Dificultad</label>
                <select
                  id="difficulty"
                  v-model="form.difficulty"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                >
                  <option value="">Seleccionar dificultad</option>
                  <option value="Fácil">Fácil</option>
                  <option value="Moderada">Moderada</option>
                  <option value="Difícil">Difícil</option>
                </select>
                <div v-if="form.errors.difficulty" class="mt-1 text-sm text-red-600">{{ form.errors.difficulty }}</div>
              </div>
            </div>

            <!-- Agency -->
            <div>
              <label for="agency_id" class="block text-sm font-medium text-gray-700">Agencia</label>
              <select
                id="agency_id"
                v-model="form.agency_id"
                :disabled="!!props.userAgencyId"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
              >
                <option value="">Seleccionar agencia</option>
                <option v-for="agency in agencies" :key="agency.id" :value="agency.id">
                  {{ agency.name }}
                </option>
              </select>
              <div v-if="props.userAgencyId" class="mt-1 text-xs text-gray-500">
                Como usuario de agencia, solo puedes editar rutas de tu propia agencia.
              </div>
              <div v-if="form.errors.agency_id" class="mt-1 text-sm text-red-600">{{ form.errors.agency_id }}</div>
            </div>

            <!-- Status Options -->
            <div class="flex space-x-6">
              <div class="flex items-center">
                <input
                  id="is_featured"
                  v-model="form.is_featured"
                  type="checkbox"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label for="is_featured" class="ml-2 block text-sm text-gray-700">Destacada</label>
              </div>
              <div class="flex items-center">
                <input
                  id="is_active"
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label for="is_active" class="ml-2 block text-sm text-gray-700">Activa</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Route Points -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-medium text-gray-900">Puntos de la Ruta</h2>
            <button
              type="button"
              @click="addPoint"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <Plus class="h-4 w-4 mr-1" />
              Añadir Punto
            </button>
          </div>

          <div class="p-6">
            <div v-if="form.points.length === 0" class="text-center py-8 text-gray-500">
              <MapPin class="h-12 w-12 mx-auto text-gray-400" />
              <p class="mt-2">No hay puntos en esta ruta. Añade al menos un punto.</p>
            </div>

            <div v-else class="space-y-6">
              <div
                v-for="(point, index) in form.points"
                :key="index"
                class="border border-gray-200 rounded-md p-4"
              >
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-md font-medium text-gray-700">Punto {{ index + 1 }}</h3>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      @click="movePointUp(index)"
                      class="p-1 text-gray-500 hover:text-gray-700"
                      :disabled="index === 0"
                      :class="{ 'opacity-50 cursor-not-allowed': index === 0 }"
                    >
                      <ArrowUpDown class="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      @click="removePoint(index)"
                      class="p-1 text-red-500 hover:text-red-700"
                    >
                      <Trash2 class="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div class="space-y-4">
                  <!-- Location -->
                  <div>
                    <label :for="`point-${index}-location`" class="block text-sm font-medium text-gray-700">Ubicación *</label>
                    <select
                      :id="`point-${index}-location`"
                      v-model="point.location_id"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                      required
                    >
                      <option value="">Seleccionar ubicación</option>
                      <option v-for="location in locations" :key="location.id" :value="location.id">
                        {{ location.name }} - {{ location.address }}
                      </option>
                    </select>
                    <div v-if="form.errors[`points.${index}.location_id`]" class="mt-1 text-sm text-red-600">
                      {{ form.errors[`points.${index}.location_id`] }}
                    </div>
                  </div>

                  <!-- Description -->
                  <div>
                    <label :for="`point-${index}-description`" class="block text-sm font-medium text-gray-700">Descripción</label>
                    <textarea
                      :id="`point-${index}-description`"
                      v-model="point.description"
                      rows="2"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                    ></textarea>
                    <div v-if="form.errors[`points.${index}.description`]" class="mt-1 text-sm text-red-600">
                      {{ form.errors[`points.${index}.description`] }}
                    </div>
                  </div>

                  <!-- Current Image -->
                  <div v-if="props.route.points[index]?.image" class="flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-md overflow-hidden">
                      <img :src="`/storage/${props.route.points[index].image}`" alt="Imagen del punto" class="w-full h-full object-cover" />
                    </div>
                    <div class="text-sm text-gray-600">Imagen actual</div>
                  </div>

                  <!-- Image -->
                  <div>
                    <label :for="`point-${index}-image`" class="block text-sm font-medium text-gray-700">
                      {{ props.route.points[index]?.image ? 'Cambiar Imagen' : 'Imagen' }}
                    </label>
                    <input
                      :id="`point-${index}-image`"
                      type="file"
                      @change="(e) => handlePointImageChange(e, index)"
                      accept="image/*"
                      class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90"
                    />
                    <div v-if="form.errors[`points.${index}.image`]" class="mt-1 text-sm text-red-600">
                      {{ form.errors[`points.${index}.image`] }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="form.processing"
            class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            <span v-if="form.processing">Guardando...</span>
            <span v-else>Actualizar Ruta</span>
          </button>
        </div>
      </form>
    </div>
  </AppLayout>
</template>
