<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps<{
  restaurant: {
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    location: {
      id: number;
      name: string;
    } | null;
    agency: {
      id: number;
      name: string;
    } | null;
    type: string;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    price: number | null;
    image: string | null;
    start_date: string | null;
    end_date: string | null;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  { label: 'Dashboard', url: route('dashboard') },
  { label: 'Restaurantes', url: route('admin.restaurants.index') },
  { label: props.restaurant.title, url: route('admin.restaurants.show', props.restaurant.id) },
];

const formatPrice = (price: number | null) => {
  if (price === null) return 'No especificado';
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(price);
};

const formatDate = (date: string | null) => {
  if (!date) return 'No especificado';
  return new Date(date).toLocaleDateString('es-ES');
};

const getImageUrl = (path: string | null) => {
  if (!path) return '/assets/images/placeholder.png';
  return path.startsWith('http') ? path : `/storage/${path}`;
};

// Extract additional fields from the description if they exist
const extractField = (text: string, fieldName: string): string => {
  const regex = new RegExp(`${fieldName}:\\s*([^\\n]+)`, 'i');
  const match = text.match(regex);
  return match && match[1] ? match[1].trim() : 'No especificado';
};

const address = extractField(props.restaurant.description, 'Dirección');
const phone = extractField(props.restaurant.description, 'Teléfono');
const email = extractField(props.restaurant.description, 'Email');
const website = extractField(props.restaurant.description, 'Sitio Web');
const cuisineType = extractField(props.restaurant.description, 'Tipo de Cocina');
const openingHours = extractField(props.restaurant.description, 'Horario');
const menuUrl = extractField(props.restaurant.description, 'Menú');
</script>

<template>
  <Head :title="restaurant.title" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-900">{{ restaurant.title }}</h2>
        <div class="flex space-x-2">
          <Link
            :href="route('admin.restaurants.edit', restaurant.id)"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Editar
          </Link>
          <Link
            :href="route('admin.restaurants.index')"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Volver
          </Link>
        </div>
      </div>
    </template>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
      <!-- Restaurant Image -->
      <div class="relative h-64 bg-gray-200">
        <img
          :src="getImageUrl(restaurant.image)"
          :alt="restaurant.title"
          class="w-full h-full object-cover"
        />
        <div class="absolute top-4 right-4 flex space-x-2">
          <span
            :class="[
              'px-3 py-1 text-xs font-semibold rounded-full',
              restaurant.is_active
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            ]"
          >
            {{ restaurant.is_active ? 'Activo' : 'Inactivo' }}
          </span>
          <span
            v-if="restaurant.is_featured"
            class="px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"
          >
            Destacado
          </span>
        </div>
      </div>

      <!-- Restaurant Details -->
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Left Column: Basic Info -->
          <div class="md:col-span-2 space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Información Básica</h3>
              <div class="bg-gray-50 p-4 rounded-md">
                <div v-if="restaurant.short_description" class="mb-4">
                  <p class="text-gray-700 italic">{{ restaurant.short_description }}</p>
                </div>
                <div class="prose max-w-none text-gray-700">
                  <p>{{ restaurant.description }}</p>
                </div>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Detalles del Restaurante</h3>
              <div class="bg-gray-50 p-4 rounded-md">
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Dirección</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ address }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Distancia</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ restaurant.distance || 'No especificada' }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Teléfono</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ phone }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ email }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Sitio Web</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <a v-if="website !== 'No especificado'" :href="website" target="_blank" class="text-blue-600 hover:text-blue-800">
                        {{ website }}
                      </a>
                      <span v-else>{{ website }}</span>
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Precio Medio</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ formatPrice(restaurant.price) }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Tipo de Cocina</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ cuisineType }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Horario</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ openingHours }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Menú</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <a v-if="menuUrl !== 'No especificado'" :href="menuUrl" target="_blank" class="text-blue-600 hover:text-blue-800">
                        Ver Menú
                      </a>
                      <span v-else>{{ menuUrl }}</span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <div v-if="restaurant.start_date || restaurant.end_date">
              <h3 class="text-lg font-medium text-gray-900 mb-2">Disponibilidad</h3>
              <div class="bg-gray-50 p-4 rounded-md">
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Fecha de Inicio</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ formatDate(restaurant.start_date) }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Fecha de Fin</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ formatDate(restaurant.end_date) }}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          <!-- Right Column: Metadata -->
          <div class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Relaciones</h3>
              <div class="bg-gray-50 p-4 rounded-md">
                <dl class="space-y-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Ubicación</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <div v-if="restaurant.location" class="flex items-center">
                        <Link
                          :href="route('admin.locations.show', restaurant.location.id)"
                          class="text-blue-600 hover:text-blue-800"
                        >
                          {{ restaurant.location.name }}
                        </Link>
                      </div>
                      <div v-else>No especificada</div>
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Agencia</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <div v-if="restaurant.agency" class="flex items-center">
                        <Link
                          :href="route('admin.agencies.show', restaurant.agency.id)"
                          class="text-blue-600 hover:text-blue-800"
                        >
                          {{ restaurant.agency.name }}
                        </Link>
                      </div>
                      <div v-else>No especificada</div>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Metadatos</h3>
              <div class="bg-gray-50 p-4 rounded-md">
                <dl class="space-y-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">ID</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ restaurant.id }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Tipo</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ restaurant.type }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Creado</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ new Date(restaurant.created_at).toLocaleString('es-ES') }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Actualizado</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ new Date(restaurant.updated_at).toLocaleString('es-ES') }}</dd>
                  </div>
                </dl>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Acciones</h3>
              <div class="bg-gray-50 p-4 rounded-md space-y-2">
                <Link
                  :href="route('admin.restaurants.edit', restaurant.id)"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Editar Restaurante
                </Link>
                <Link
                  :href="route('admin.restaurants.destroy', restaurant.id)"
                  method="delete"
                  as="button"
                  type="button"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  @click.prevent="
                    $inertia.delete(route('admin.restaurants.destroy', restaurant.id), {
                      onBefore: () => confirm('¿Estás seguro de que quieres eliminar este restaurante?'),
                    })
                  "
                >
                  Eliminar Restaurante
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
