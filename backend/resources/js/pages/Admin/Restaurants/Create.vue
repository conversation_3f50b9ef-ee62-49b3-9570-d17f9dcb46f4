<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  locations: Array<{
    id: number;
    name: string;
  }>;
  agencies: Array<{
    id: number;
    name: string;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  { label: 'Dashboard', url: route('dashboard') },
  { label: 'Restaurantes', url: route('admin.restaurants.index') },
  { label: 'Crear Restaurante', url: route('admin.restaurants.create') },
];

const form = useForm({
  title: '',
  description: '',
  short_description: '',
  location_id: null as number | null,
  agency_id: null as number | null,
  duration: '',
  distance: '',
  price: null as number | null,
  image: null as File | null,
  start_date: null as string | null,
  end_date: null as string | null,
  is_featured: false,
  is_active: true,
  address: '',
  phone: '',
  email: '',
  website: '',
  cuisine_type: '',
  opening_hours: '',
  menu_url: '',
});

const imagePreview = ref<string | null>(null);

const handleImageChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    form.image = input.files[0];
    
    // Create a preview URL
    imagePreview.value = URL.createObjectURL(input.files[0]);
  }
};

const submit = () => {
  form.post(route('admin.restaurants.store'), {
    onSuccess: () => {
      // Clean up the preview URL
      if (imagePreview.value) {
        URL.revokeObjectURL(imagePreview.value);
        imagePreview.value = null;
      }
    },
  });
};
</script>

<template>
  <Head title="Crear Restaurante" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-900">Crear Restaurante</h2>
        <Link
          :href="route('admin.restaurants.index')"
          class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Volver
        </Link>
      </div>
    </template>

    <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
      <form @submit.prevent="submit" class="space-y-6">
        <!-- Basic Information Section -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Información Básica</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Title -->
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700">Nombre del Restaurante *</label>
              <input
                id="title"
                v-model="form.title"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                required
              />
              <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
            </div>

            <!-- Price -->
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700">Precio Medio (€)</label>
              <input
                id="price"
                v-model="form.price"
                type="number"
                step="0.01"
                min="0"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.price" class="text-red-500 text-sm mt-1">{{ form.errors.price }}</div>
            </div>

            <!-- Short Description -->
            <div class="md:col-span-2">
              <label for="short_description" class="block text-sm font-medium text-gray-700">Descripción Corta</label>
              <input
                id="short_description"
                v-model="form.short_description"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.short_description" class="text-red-500 text-sm mt-1">{{ form.errors.short_description }}</div>
            </div>

            <!-- Full Description -->
            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700">Descripción Completa *</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="4"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                required
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>
          </div>
        </div>

        <!-- Location and Contact Section -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Ubicación y Contacto</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Location -->
            <div>
              <label for="location_id" class="block text-sm font-medium text-gray-700">Ubicación</label>
              <select
                id="location_id"
                v-model="form.location_id"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option :value="null">Seleccionar ubicación</option>
                <option v-for="location in locations" :key="location.id" :value="location.id">
                  {{ location.name }}
                </option>
              </select>
              <div v-if="form.errors.location_id" class="text-red-500 text-sm mt-1">{{ form.errors.location_id }}</div>
            </div>

            <!-- Agency -->
            <div>
              <label for="agency_id" class="block text-sm font-medium text-gray-700">Agencia</label>
              <select
                id="agency_id"
                v-model="form.agency_id"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option :value="null">Seleccionar agencia</option>
                <option v-for="agency in agencies" :key="agency.id" :value="agency.id">
                  {{ agency.name }}
                </option>
              </select>
              <div v-if="form.errors.agency_id" class="text-red-500 text-sm mt-1">{{ form.errors.agency_id }}</div>
            </div>

            <!-- Address -->
            <div>
              <label for="address" class="block text-sm font-medium text-gray-700">Dirección</label>
              <input
                id="address"
                v-model="form.address"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.address" class="text-red-500 text-sm mt-1">{{ form.errors.address }}</div>
            </div>

            <!-- Distance -->
            <div>
              <label for="distance" class="block text-sm font-medium text-gray-700">Distancia</label>
              <input
                id="distance"
                v-model="form.distance"
                type="text"
                placeholder="Ej: 5 km"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.distance" class="text-red-500 text-sm mt-1">{{ form.errors.distance }}</div>
            </div>

            <!-- Phone -->
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700">Teléfono</label>
              <input
                id="phone"
                v-model="form.phone"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.phone" class="text-red-500 text-sm mt-1">{{ form.errors.phone }}</div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</div>
            </div>

            <!-- Website -->
            <div>
              <label for="website" class="block text-sm font-medium text-gray-700">Sitio Web</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.website" class="text-red-500 text-sm mt-1">{{ form.errors.website }}</div>
            </div>
          </div>
        </div>

        <!-- Restaurant Specific Section -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Información del Restaurante</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Cuisine Type -->
            <div>
              <label for="cuisine_type" class="block text-sm font-medium text-gray-700">Tipo de Cocina</label>
              <input
                id="cuisine_type"
                v-model="form.cuisine_type"
                type="text"
                placeholder="Ej: Mediterránea, Tradicional, Fusión..."
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.cuisine_type" class="text-red-500 text-sm mt-1">{{ form.errors.cuisine_type }}</div>
            </div>

            <!-- Menu URL -->
            <div>
              <label for="menu_url" class="block text-sm font-medium text-gray-700">URL del Menú</label>
              <input
                id="menu_url"
                v-model="form.menu_url"
                type="url"
                placeholder="https://ejemplo.com/menu"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.menu_url" class="text-red-500 text-sm mt-1">{{ form.errors.menu_url }}</div>
            </div>

            <!-- Opening Hours -->
            <div class="md:col-span-2">
              <label for="opening_hours" class="block text-sm font-medium text-gray-700">Horario de Apertura</label>
              <textarea
                id="opening_hours"
                v-model="form.opening_hours"
                rows="3"
                placeholder="Ej: Lunes a Viernes: 13:00-16:00, 20:00-23:00. Sábados y Domingos: 13:00-23:00"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              ></textarea>
              <div v-if="form.errors.opening_hours" class="text-red-500 text-sm mt-1">{{ form.errors.opening_hours }}</div>
            </div>
          </div>
        </div>

        <!-- Dates and Status Section -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Fechas y Estado</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Start Date -->
            <div>
              <label for="start_date" class="block text-sm font-medium text-gray-700">Fecha de Inicio</label>
              <input
                id="start_date"
                v-model="form.start_date"
                type="date"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.start_date" class="text-red-500 text-sm mt-1">{{ form.errors.start_date }}</div>
            </div>

            <!-- End Date -->
            <div>
              <label for="end_date" class="block text-sm font-medium text-gray-700">Fecha de Fin</label>
              <input
                id="end_date"
                v-model="form.end_date"
                type="date"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <div v-if="form.errors.end_date" class="text-red-500 text-sm mt-1">{{ form.errors.end_date }}</div>
            </div>

            <!-- Status Toggles -->
            <div>
              <div class="flex items-center">
                <input
                  id="is_active"
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="is_active" class="ml-2 block text-sm text-gray-900">Activo</label>
              </div>
              <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">{{ form.errors.is_active }}</div>
            </div>

            <div>
              <div class="flex items-center">
                <input
                  id="is_featured"
                  v-model="form.is_featured"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="is_featured" class="ml-2 block text-sm text-gray-900">Destacado</label>
              </div>
              <div v-if="form.errors.is_featured" class="text-red-500 text-sm mt-1">{{ form.errors.is_featured }}</div>
            </div>
          </div>
        </div>

        <!-- Image Upload Section -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Imagen</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="image" class="block text-sm font-medium text-gray-700">Imagen del Restaurante</label>
              <input
                id="image"
                type="file"
                @change="handleImageChange"
                accept="image/*"
                class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image }}</div>
            </div>

            <div v-if="imagePreview" class="mt-2">
              <img :src="imagePreview" alt="Image Preview" class="h-32 w-auto object-cover rounded-md" />
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            :disabled="form.processing"
          >
            <span v-if="form.processing">Guardando...</span>
            <span v-else>Guardar Restaurante</span>
          </button>
        </div>
      </form>
    </div>
  </AppLayout>
</template>
