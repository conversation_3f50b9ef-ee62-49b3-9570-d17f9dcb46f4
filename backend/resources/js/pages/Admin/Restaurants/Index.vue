<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps<{
  restaurants: Array<{
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    location: {
      id: number;
      name: string;
    } | null;
    agency: {
      id: number;
      name: string;
    } | null;
    type: string;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    price: number | null;
    image: string | null;
    start_date: string | null;
    end_date: string | null;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  { label: 'Dashboard', url: route('dashboard') },
  { label: 'Restaurantes', url: route('admin.restaurants.index') },
];

const searchQuery = ref('');

const filteredRestaurants = computed(() => {
  if (!searchQuery.value) return props.restaurants;
  
  const query = searchQuery.value.toLowerCase();
  return props.restaurants.filter(restaurant => 
    restaurant.title.toLowerCase().includes(query) ||
    (restaurant.description && restaurant.description.toLowerCase().includes(query)) ||
    (restaurant.short_description && restaurant.short_description.toLowerCase().includes(query)) ||
    (restaurant.location && restaurant.location.name.toLowerCase().includes(query))
  );
});

const formatPrice = (price: number | null) => {
  if (price === null) return 'N/A';
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(price);
};

const formatDate = (date: string | null) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('es-ES');
};

const getImageUrl = (path: string | null) => {
  if (!path) return '/assets/images/placeholder.png';
  return path.startsWith('http') ? path : `/storage/${path}`;
};
</script>

<template>
  <Head title="Restaurantes" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-900">Restaurantes</h2>
        <Link
          :href="route('admin.restaurants.create')"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Crear Restaurante
        </Link>
      </div>
    </template>

    <!-- Search Bar -->
    <div class="mb-6">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="Buscar restaurantes..."
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>

    <!-- Restaurants Table -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Imagen
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Título
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ubicación
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Precio Medio
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Estado
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Destacado
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Acciones
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="restaurant in filteredRestaurants" :key="restaurant.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <img :src="getImageUrl(restaurant.image)" alt="Restaurant Image" class="h-16 w-16 object-cover rounded-md" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ restaurant.title }}</div>
                <div v-if="restaurant.short_description" class="text-sm text-gray-500 truncate max-w-xs">
                  {{ restaurant.short_description }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="restaurant.location" class="text-sm text-gray-900">{{ restaurant.location.name }}</div>
                <div v-else class="text-sm text-gray-500">No especificada</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatPrice(restaurant.price) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                    restaurant.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ restaurant.is_active ? 'Activo' : 'Inactivo' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                    restaurant.is_featured
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ restaurant.is_featured ? 'Destacado' : 'No destacado' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <Link
                    :href="route('admin.restaurants.show', restaurant.id)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    Ver
                  </Link>
                  <Link
                    :href="route('admin.restaurants.edit', restaurant.id)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    Editar
                  </Link>
                  <Link
                    :href="route('admin.restaurants.destroy', restaurant.id)"
                    method="delete"
                    as="button"
                    type="button"
                    class="text-red-600 hover:text-red-900"
                    @click.prevent="
                      $inertia.delete(route('admin.restaurants.destroy', restaurant.id), {
                        onBefore: () => confirm('¿Estás seguro de que quieres eliminar este restaurante?'),
                      })
                    "
                  >
                    Eliminar
                  </Link>
                </div>
              </td>
            </tr>
            <tr v-if="filteredRestaurants.length === 0">
              <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                No se encontraron restaurantes
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </AppLayout>
</template>
