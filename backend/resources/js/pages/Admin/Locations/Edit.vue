<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps<{
  location: {
    id: number;
    name: string;
    description: string;
    address: string;
    latitude: number;
    longitude: number;
    image: string | null;
    type: string;
    is_featured: boolean;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Locations',
    href: '/dashboard/locations',
  },
  {
    title: 'Edit',
    href: `/dashboard/locations/${props.location.id}/edit`,
  },
];

const locationTypes = [
  { value: 'point', label: 'Point of Interest' },
  { value: 'hotel', label: 'Hotel' },
  { value: 'restaurant', label: 'Restaurant' },
  { value: 'museum', label: 'Museum' },
  { value: 'park', label: 'Park' },
  { value: 'attraction', label: 'Attraction' },
];

const form = useForm({
  name: props.location.name,
  description: props.location.description,
  address: props.location.address,
  latitude: props.location.latitude,
  longitude: props.location.longitude,
  image: props.location.image || '',
  type: props.location.type,
  is_featured: props.location.is_featured,
});

const submit = () => {
  form.put(`/dashboard/locations/${props.location.id}`);
};
</script>

<template>
  <Head title="Edit Location" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-white">Edit Location</h1>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
        <form @submit.prevent="submit">
          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
            <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">
              {{ form.errors.name }}
            </div>
          </div>

          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            ></textarea>
            <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">
              {{ form.errors.description }}
            </div>
          </div>

          <div class="mb-4">
            <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Address</label>
            <input
              id="address"
              v-model="form.address"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
            <div v-if="form.errors.address" class="text-red-500 text-sm mt-1">
              {{ form.errors.address }}
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label for="latitude" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Latitude</label>
              <input
                id="latitude"
                v-model="form.latitude"
                type="number"
                step="0.0000001"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
              />
              <div v-if="form.errors.latitude" class="text-red-500 text-sm mt-1">
                {{ form.errors.latitude }}
              </div>
            </div>
            <div>
              <label for="longitude" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Longitude</label>
              <input
                id="longitude"
                v-model="form.longitude"
                type="number"
                step="0.0000001"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
              />
              <div v-if="form.errors.longitude" class="text-red-500 text-sm mt-1">
                {{ form.errors.longitude }}
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Type</label>
            <select
              id="type"
              v-model="form.type"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            >
              <option v-for="type in locationTypes" :key="type.value" :value="type.value">
                {{ type.label }}
              </option>
            </select>
            <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">
              {{ form.errors.type }}
            </div>
          </div>

          <div class="mb-4">
            <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Image</label>
            <input
              id="image"
              v-model="form.image"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="e.g., museum1.jpeg"
            />
            <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">
              {{ form.errors.image }}
            </div>
          </div>

          <div class="mb-4">
            <div class="flex items-center">
              <input
                id="is_featured"
                v-model="form.is_featured"
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <label for="is_featured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Featured Location</label>
            </div>
            <div v-if="form.errors.is_featured" class="text-red-500 text-sm mt-1">
              {{ form.errors.is_featured }}
            </div>
          </div>

          <div class="flex justify-end">
            <button
              type="button"
              class="mr-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
              @click="$inertia.visit('/dashboard/locations')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800"
              :disabled="form.processing"
            >
              Update Location
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
