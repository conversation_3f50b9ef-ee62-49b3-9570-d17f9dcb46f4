<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Newspaper, Calendar } from 'lucide-vue-next';

const props = defineProps<{
  news: {
    id: number;
    title: string;
    content: string;
    image: string | null;
    is_published: boolean;
    created_at: string;
    updated_at: string;
  };
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Noticias y Eventos',
    href: '/dashboard/news-events',
  },
  {
    title: props.news.title,
    href: `/dashboard/news-events/news/${props.news.id}`,
  },
];

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<template>
  <Head :title="`Noticia - ${news.title}`" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">{{ news.title }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/news/${news.id}/edit`"
            class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Editar
          </Link>
          <Link
            href="/dashboard/news-events"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver
          </Link>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- News Image -->
        <div class="md:col-span-1">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div v-if="news.image" class="aspect-square">
              <img :src="`/storage/${news.image}`" :alt="news.title" class="w-full h-full object-cover" />
            </div>
            <div v-else class="aspect-square bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <Newspaper class="h-24 w-24 text-gray-400" />
            </div>
            
            <div class="p-4">
              <div class="flex items-center mb-2">
                <span
                  class="px-2 py-1 text-xs font-semibold rounded-full"
                  :class="news.is_published ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'"
                >
                  {{ news.is_published ? 'Publicado' : 'Borrador' }}
                </span>
              </div>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Calendar class="h-4 w-4 mr-1" />
                {{ formatDate(news.created_at) }}
              </div>
            </div>
          </div>
        </div>

        <!-- News Content -->
        <div class="md:col-span-2">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
            <div class="mb-6">
              <h2 class="text-lg font-semibold mb-4 text-primary">Contenido</h2>
              <div class="prose dark:prose-invert max-w-none">
                <p class="whitespace-pre-line">{{ news.content }}</p>
              </div>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
              <h3 class="text-md font-semibold mb-2">Información Administrativa</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                <span class="font-medium">Creado:</span> {{ formatDate(news.created_at) }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                <span class="font-medium">Última actualización:</span> {{ formatDate(news.updated_at) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
