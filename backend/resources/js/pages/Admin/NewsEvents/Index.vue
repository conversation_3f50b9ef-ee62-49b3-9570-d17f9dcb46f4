<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { Newspaper, CalendarDays } from 'lucide-vue-next';

const props = defineProps<{
  news: Array<{
    id: number;
    title: string;
    content: string;
    image: string | null;
    is_published: boolean;
    created_at: string;
    updated_at: string;
  }>;
  events: Array<{
    id: number;
    title: string;
    description: string;
    location: string;
    start_datetime: string;
    end_datetime: string | null;
    image: string | null;
    rating: number | null;
    is_featured: boolean;
    created_at: string;
    updated_at: string;
  }>;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Noticias y Eventos',
    href: '/dashboard/news-events',
  },
];

const activeTab = ref('news'); // 'news' or 'events'
const searchQuery = ref('');

const filteredNews = computed(() => {
  if (!searchQuery.value) return props.news;

  const query = searchQuery.value.toLowerCase();
  return props.news.filter(item =>
    item.title.toLowerCase().includes(query) ||
    item.content.toLowerCase().includes(query)
  );
});

const filteredEvents = computed(() => {
  if (!searchQuery.value) return props.events;

  const query = searchQuery.value.toLowerCase();
  return props.events.filter(item =>
    item.title.toLowerCase().includes(query) ||
    item.description.toLowerCase().includes(query) ||
    item.location.toLowerCase().includes(query)
  );
});

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const confirmDeleteNews = (id: number) => {
  if (confirm('¿Estás seguro de que quieres eliminar esta noticia?')) {
    window.location.href = `/dashboard/news/${id}/delete`;
  }
};

const confirmDeleteEvent = (id: number) => {
  if (confirm('¿Estás seguro de que quieres eliminar este evento?')) {
    window.location.href = `/dashboard/events/${id}/delete`;
  }
};
</script>

<template>
  <Head title="Noticias y Eventos" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">Noticias y Eventos</h1>
        <div class="flex space-x-2">
          <Link
            v-if="activeTab === 'news'"
            href="/dashboard/news/create"
            class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Crear Noticia
          </Link>
          <Link
            v-if="activeTab === 'events'"
            href="/dashboard/events/create"
            class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Crear Evento
          </Link>
        </div>
      </div>

      <!-- Tabs -->
      <div class="flex border-b border-gray-200 dark:border-gray-700 mb-4">
        <button
          @click="activeTab = 'news'"
          :class="[
            'py-2 px-4 font-medium text-sm focus:outline-none',
            activeTab === 'news'
              ? 'border-b-2 border-primary text-primary'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
          ]"
        >
          <div class="flex items-center">
            <Newspaper class="h-4 w-4 mr-2" />
            Noticias
          </div>
        </button>
        <button
          @click="activeTab = 'events'"
          :class="[
            'py-2 px-4 font-medium text-sm focus:outline-none',
            activeTab === 'events'
              ? 'border-b-2 border-primary text-primary'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
          ]"
        >
          <div class="flex items-center">
            <CalendarDays class="h-4 w-4 mr-2" />
            Eventos
          </div>
        </button>
      </div>

      <!-- Search Bar -->
      <div class="mb-4">
        <input
          v-model="searchQuery"
          type="text"
          :placeholder="activeTab === 'news' ? 'Buscar noticias...' : 'Buscar eventos...'"
          class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
      </div>

      <!-- News Tab -->
      <div v-if="activeTab === 'news'" class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Título
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Fecha de Publicación
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Estado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="item in filteredNews" :key="item.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img
                        v-if="item.image"
                        :src="`/storage/${item.image}`"
                        :alt="item.title"
                        class="h-10 w-10 rounded-full object-cover"
                      />
                      <div
                        v-else
                        class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center"
                      >
                        <Newspaper class="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ item.title }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(item.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="item.is_published ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'"
                  >
                    {{ item.is_published ? 'Publicado' : 'Borrador' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <Link :href="`/dashboard/news-events/news/${item.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                      Ver
                    </Link>
                    <Link :href="`/dashboard/news/${item.id}/edit`" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                      Editar
                    </Link>
                    <button @click="confirmDeleteNews(item.id)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      Eliminar
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredNews.length === 0">
                <td colspan="4" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No se encontraron noticias
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Events Tab -->
      <div v-if="activeTab === 'events'" class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Título
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ubicación
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Fecha
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Destacado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="item in filteredEvents" :key="item.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img
                        v-if="item.image"
                        :src="`/storage/${item.image}`"
                        :alt="item.title"
                        class="h-10 w-10 rounded-full object-cover"
                      />
                      <div
                        v-else
                        class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center"
                      >
                        <CalendarDays class="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ item.title }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ item.location }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(item.start_datetime) }}
                  <span v-if="item.end_datetime"> - {{ formatDate(item.end_datetime) }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="item.is_featured ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'"
                  >
                    {{ item.is_featured ? 'Destacado' : 'Normal' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <Link :href="`/dashboard/news-events/events/${item.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                      Ver
                    </Link>
                    <Link :href="`/dashboard/events/${item.id}/edit`" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                      Editar
                    </Link>
                    <button @click="confirmDeleteEvent(item.id)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      Eliminar
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredEvents.length === 0">
                <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No se encontraron eventos
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
