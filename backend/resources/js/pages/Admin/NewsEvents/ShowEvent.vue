<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { CalendarDays, MapPin, Star, Clock } from 'lucide-vue-next';

const props = defineProps<{
  event: {
    id: number;
    title: string;
    description: string;
    location: string;
    start_datetime: string;
    end_datetime: string | null;
    image: string | null;
    rating: number | null;
    is_featured: boolean;
    created_at: string;
    updated_at: string;
  };
  isAgencyUser: boolean;
  isSuperAdmin: boolean;
}>();

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Noticias y Eventos',
    href: '/dashboard/news-events',
  },
  {
    title: props.event.title,
    href: `/dashboard/news-events/events/${props.event.id}`,
  },
];

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<template>
  <Head :title="`Evento - ${event.title}`" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">{{ event.title }}</h1>
        <div class="flex space-x-2">
          <Link
            :href="`/dashboard/events/${event.id}/edit`"
            class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Editar
          </Link>
          <Link
            href="/dashboard/news-events"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver
          </Link>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Event Image -->
        <div class="md:col-span-1">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div v-if="event.image" class="aspect-square">
              <img :src="`/storage/${event.image}`" :alt="event.title" class="w-full h-full object-cover" />
            </div>
            <div v-else class="aspect-square bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <CalendarDays class="h-24 w-24 text-gray-400" />
            </div>
            
            <div class="p-4">
              <div class="flex items-center mb-2">
                <span
                  class="px-2 py-1 text-xs font-semibold rounded-full mr-2"
                  :class="event.is_featured ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'"
                >
                  {{ event.is_featured ? 'Destacado' : 'Normal' }}
                </span>
                <div v-if="event.rating" class="flex items-center">
                  <Star class="h-4 w-4 text-yellow-500 mr-1" />
                  <span class="text-sm font-medium">{{ event.rating }}/5</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Event Details -->
        <div class="md:col-span-2">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden p-6">
            <div class="mb-6">
              <h2 class="text-lg font-semibold mb-4 text-primary">Detalles del Evento</h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="flex items-start">
                  <Clock class="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Fecha de inicio</p>
                    <p class="font-medium">{{ formatDate(event.start_datetime) }}</p>
                  </div>
                </div>
                
                <div v-if="event.end_datetime" class="flex items-start">
                  <Clock class="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Fecha de fin</p>
                    <p class="font-medium">{{ formatDate(event.end_datetime) }}</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <MapPin class="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Ubicación</p>
                    <p class="font-medium">{{ event.location }}</p>
                  </div>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="text-md font-semibold mb-2">Descripción</h3>
                <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">{{ event.description }}</p>
              </div>

              <div v-if="isSuperAdmin" class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                <h3 class="text-md font-semibold mb-2">Información Administrativa</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <span class="font-medium">Creado:</span> {{ formatDate(event.created_at) }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <span class="font-medium">Última actualización:</span> {{ formatDate(event.updated_at) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
