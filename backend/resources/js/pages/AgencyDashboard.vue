<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { Newspaper, MapPin, Coffee, Building2, CalendarDays, Code, FileText, Box } from 'lucide-vue-next';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Panel',
        href: '/dashboard',
    },
];

const page = usePage();
const user = page.props.auth.user;
const agency = user.agency;
</script>

<template>
    <Head title="Panel de Agencia" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <h1 class="text-3xl font-bold text-primary">Panel de Agencia</h1>
                <img src="/assets/logos/CorkExpLogoBlack.png" alt="Cork Experience" class="h-12 dark:hidden" />
                <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience" class="h-12 hidden dark:block" />
            </div>

            <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                <!-- Agency Info Card -->
                <div class="flex flex-col gap-4 rounded-xl border border-border bg-card p-6 shadow-sm">
                    <div class="flex items-center gap-4">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                            <Building2 class="h-6 w-6" />
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold">{{ agency.name }}</h2>
                            <p class="text-sm text-muted-foreground">Información de la agencia</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p v-if="agency.description" class="text-sm">{{ agency.description }}</p>
                        <p v-if="agency.address" class="text-sm"><strong>Dirección:</strong> {{ agency.address }}</p>
                        <p v-if="agency.email" class="text-sm"><strong>Email:</strong> {{ agency.email }}</p>
                        <p v-if="agency.phone" class="text-sm"><strong>Teléfono:</strong> {{ agency.phone }}</p>
                    </div>
                    <div class="mt-auto">
                        <Link :href="route('admin.agencies.edit', agency.id)" class="text-sm text-primary hover:underline">
                            Editar información
                        </Link>
                    </div>
                </div>

                <!-- Experiences Card -->
                <div class="flex flex-col gap-4 rounded-xl border border-border bg-card p-6 shadow-sm">
                    <div class="flex items-center gap-4">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                            <Coffee class="h-6 w-6" />
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold">Experiencias</h2>
                            <p class="text-sm text-muted-foreground">Gestiona tus experiencias</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p class="text-sm">Crea y gestiona experiencias para tus clientes.</p>
                    </div>
                    <div class="mt-auto">
                        <Link :href="route('admin.experiences.index')" class="text-sm text-primary hover:underline">
                            Ver experiencias
                        </Link>
                    </div>
                </div>

                <!-- Routes Card -->
                <div class="flex flex-col gap-4 rounded-xl border border-border bg-card p-6 shadow-sm">
                    <div class="flex items-center gap-4">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                            <MapPin class="h-6 w-6" />
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold">Rutas</h2>
                            <p class="text-sm text-muted-foreground">Gestiona tus rutas</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p class="text-sm">Crea y gestiona rutas turísticas para tus clientes.</p>
                    </div>
                    <div class="mt-auto">
                        <Link :href="route('admin.routes.index')" class="text-sm text-primary hover:underline">
                            Ver rutas
                        </Link>
                    </div>
                </div>

                <!-- Events Card -->
                <div class="flex flex-col gap-4 rounded-xl border border-border bg-card p-6 shadow-sm">
                    <div class="flex items-center gap-4">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                            <CalendarDays class="h-6 w-6" />
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold">Eventos</h2>
                            <p class="text-sm text-muted-foreground">Gestiona tus eventos</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p class="text-sm">Crea y gestiona eventos para tus clientes.</p>
                    </div>
                    <div class="mt-auto">
                        <Link :href="route('admin.events.index')" class="text-sm text-primary hover:underline">
                            Ver eventos
                        </Link>
                    </div>
                </div>

                <!-- Users Card -->
                <div class="flex flex-col gap-4 rounded-xl border border-border bg-card p-6 shadow-sm">
                    <div class="flex items-center gap-4">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                            <Code class="h-6 w-6" />
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold">Usuarios</h2>
                            <p class="text-sm text-muted-foreground">Gestiona tus usuarios</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p class="text-sm">Administra los usuarios de tu agencia.</p>
                    </div>
                    <div class="mt-auto">
                        <Link :href="route('admin.agencies.manage-users', agency.id)" class="text-sm text-primary hover:underline">
                            Gestionar usuarios
                        </Link>
                    </div>
                </div>

                <!-- Documentation Card -->
                <div class="flex flex-col gap-4 rounded-xl border border-border bg-card p-6 shadow-sm">
                    <div class="flex items-center gap-4">
                        <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                            <FileText class="h-6 w-6" />
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold">Documentación</h2>
                            <p class="text-sm text-muted-foreground">Ayuda y recursos</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p class="text-sm">Accede a la documentación y recursos de ayuda.</p>
                    </div>
                    <div class="mt-auto">
                        <Link :href="route('admin.documentation.index')" class="text-sm text-primary hover:underline">
                            Ver documentación
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
