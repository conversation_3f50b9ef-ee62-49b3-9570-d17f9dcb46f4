<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { Newspaper, MapPin, Coffee, Building2, CalendarDays, Code, FileText, Box, Users, Settings, Video } from 'lucide-vue-next';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Panel',
        href: '/dashboard',
    },
];

const page = usePage();
const user = page.props.auth.user;

// Determine user role
const isSuperAdmin = user.role === 'superadmin';
const isAgencyUser = !!user.agency_id;
const isAgencyAdmin = user.role === 'agency_admin';
const agency = user.agency;

// Debug user data
console.log('Dashboard - User data:', user);
console.log('Dashboard - User role:', user.role);
console.log('Dashboard - Is agency admin?', isAgencyAdmin);
console.log('Dashboard - Agency:', agency);

// Define dashboard cards for different user roles
const dashboardCards = [];

// Cards for agency users
if (isAgencyUser) {
    // Agency Users Management Card (only for agency admins)
    if (isAgencyAdmin && agency) {
        dashboardCards.push({
            title: 'Gestionar Usuarios',
            description: 'Administra los usuarios de tu agencia',
            icon: Users,
            href: '/dashboard/agency-user-management',
            color: 'bg-purple-500',
        });
    }

    // Agency Info Card
    dashboardCards.push({
        title: 'Mi Agencia',
        description: 'Ver información de tu agencia',
        icon: Building2,
        color: 'secondary',
        href: `/dashboard/my-agency`,
    });

    // Experiences Card
    dashboardCards.push({
        title: 'Experiencias',
        description: 'Gestionar experiencias, alojamientos y restaurantes',
        icon: Coffee,
        color: '#EEAE8F',
        href: '/dashboard/experiences',
    });

    // Routes Card
    dashboardCards.push({
        title: 'Rutas',
        description: 'Gestionar rutas turísticas',
        icon: MapPin,
        color: '#DC8960',
        href: '/dashboard/routes',
    });

    // Events Card
    dashboardCards.push({
        title: 'Eventos',
        description: 'Gestionar eventos de tu agencia',
        icon: CalendarDays,
        color: 'primary',
        href: '/dashboard/events',
    });

    // Documentation Card
    dashboardCards.push({
        title: 'Documentación',
        description: 'Acceder a la documentación y recursos de ayuda',
        icon: FileText,
        color: 'blue-500',
        href: '/dashboard/documentation',
    });
}

// Cards for superadmins
if (isSuperAdmin) {
    // News & Events Card
    dashboardCards.push({
        title: 'Noticias y Eventos',
        description: 'Gestionar noticias, eventos y actividades',
        icon: Newspaper,
        color: 'primary',
        href: '/dashboard/news-events',
    });

    // Locations Card
    dashboardCards.push({
        title: 'Ubicaciones',
        description: 'Gestionar ubicaciones del mapa interactivo',
        icon: MapPin,
        color: 'secondary',
        href: '/dashboard/locations',
    });

    // Experiences Card
    dashboardCards.push({
        title: 'Experiencias',
        description: 'Gestionar experiencias, alojamientos y restaurantes',
        icon: Coffee,
        color: '#EEAE8F',
        href: '/dashboard/experiences',
    });

    // StoryTours Card
    dashboardCards.push({
        title: 'StoryTours',
        description: 'Gestionar tours interactivos y experiencias AR',
        icon: Box,
        color: 'green-600',
        href: '/dashboard/story-tours',
    });

    // Routes Card
    dashboardCards.push({
        title: 'Rutas',
        description: 'Gestionar rutas turísticas',
        icon: MapPin,
        color: '#DC8960',
        href: '/dashboard/routes',
    });

    // Agencies Card
    dashboardCards.push({
        title: 'Operadores y Agencias',
        description: 'Gestionar operadores, agencias y usuarios',
        icon: Building2,
        color: '#DC8960',
        href: '/dashboard/agencies',
    });

    // Welcome Settings Card
    dashboardCards.push({
        title: 'Pantalla de Bienvenida',
        description: 'Gestionar textos y videos de la pantalla de bienvenida',
        icon: Video,
        color: 'purple-500',
        href: '/dashboard/welcome-settings',
    });

    // Documentation Card
    dashboardCards.push({
        title: 'Documentación',
        description: 'Acceder a la documentación y recursos de ayuda',
        icon: FileText,
        color: 'blue-500',
        href: '/dashboard/documentation',
    });
}
</script>

<template>
    <Head :title="isAgencyUser ? 'Panel de Agencia' : 'Panel de Control'" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <h1 class="text-3xl font-bold text-primary">{{ isAgencyUser ? 'Panel de Agencia' : 'Panel de Administración' }}</h1>
                <img src="/assets/logos/CorkExpLogoBlack.png" alt="Cork Experience" class="h-12 dark:hidden" />
                <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience" class="h-12 hidden dark:block" />
            </div>

            <!-- Dashboard Cards Grid -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <!-- Render all dashboard cards using v-for -->
                <template v-for="(card, index) in dashboardCards" :key="index">
                    <Link :href="card.href" class="group flex flex-col p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 dark:bg-gray-800 border-l-4" :class="{
                        'border-primary': card.color === 'primary',
                        'border-secondary': card.color === 'secondary',
                        'border-[#EEAE8F]': card.color === '#EEAE8F',
                        'border-[#DC8960]': card.color === '#DC8960',
                        'border-green-600': card.color === 'green-600',
                        'border-blue-500': card.color === 'blue-500',
                        'border-purple-500': card.color === 'purple-500',
                    }">
                        <div class="flex items-center mb-4">
                            <div class="p-3 rounded-full mr-4" :class="{
                                'bg-primary/10 text-primary': card.color === 'primary',
                                'bg-secondary/10 text-secondary': card.color === 'secondary',
                                'bg-[#EEAE8F]/10 text-[#EEAE8F]': card.color === '#EEAE8F',
                                'bg-[#DC8960]/10 text-[#DC8960]': card.color === '#DC8960',
                                'bg-green-600/10 text-green-600': card.color === 'green-600',
                                'bg-blue-500/10 text-blue-500': card.color === 'blue-500',
                                'bg-purple-500/10 text-purple-500': card.color === 'purple-500',
                            }">
                                <component :is="card.icon" class="h-6 w-6" />
                            </div>
                            <h2 class="text-xl font-semibold dark:text-white group-hover:text-primary transition-colors">{{ card.title }}</h2>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300">{{ card.description }}</p>

                        <!-- Special case for News & Events card -->
                        <div v-if="card.title === 'Noticias y Eventos'" class="mt-3 flex gap-2">
                            <Link href="/dashboard/news" class="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full hover:bg-primary/20">
                                Noticias
                            </Link>
                            <Link href="/dashboard/news-events" class="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full hover:bg-primary/20">
                                Eventos
                            </Link>
                        </div>

                        <!-- Special case for StoryTours card -->
                        <div v-if="card.title === 'StoryTours'" class="mt-3 flex flex-wrap gap-2">
                            <span class="text-xs px-2 py-1 bg-green-600/10 text-green-600 rounded-full">Paisaje</span>
                            <span class="text-xs px-2 py-1 bg-green-600/10 text-green-600 rounded-full">Patrimonio</span>
                            <span class="text-xs px-2 py-1 bg-green-600/10 text-green-600 rounded-full">Cultura</span>
                        </div>
                    </Link>
                </template>
            </div>

            <!-- API Endpoints - Only visible to superadmins -->
            <div v-if="isSuperAdmin" class="mt-4">
                <h2 class="text-xl font-semibold mb-4 text-secondary">API Endpoints</h2>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                    <Link href="/api/news" target="_blank" class="group flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-all dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                        <div class="p-2 rounded-full bg-primary/10 text-primary mr-3">
                            <Code class="h-5 w-5" />
                        </div>
                        <div>
                            <h3 class="font-medium dark:text-white">API de Noticias</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Ver endpoints</p>
                        </div>
                    </Link>

                    <Link href="/api/events" target="_blank" class="group flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-all dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                        <div class="p-2 rounded-full bg-secondary/10 text-secondary mr-3">
                            <Code class="h-5 w-5" />
                        </div>
                        <div>
                            <h3 class="font-medium dark:text-white">API de Eventos</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Ver endpoints</p>
                        </div>
                    </Link>

                    <Link href="/api/locations" target="_blank" class="group flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-all dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                        <div class="p-2 rounded-full bg-[#EEAE8F]/10 text-[#EEAE8F] mr-3">
                            <Code class="h-5 w-5" />
                        </div>
                        <div>
                            <h3 class="font-medium dark:text-white">API de Ubicaciones</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Ver endpoints</p>
                        </div>
                    </Link>

                    <Link href="/api/story-tours" target="_blank" class="group flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-all dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                        <div class="p-2 rounded-full bg-green-600/10 text-green-600 mr-3">
                            <Code class="h-5 w-5" />
                        </div>
                        <div>
                            <h3 class="font-medium dark:text-white">API de StoryTours</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Ver endpoints</p>
                        </div>
                    </Link>
                </div>
            </div>

            <!-- Agency-specific info - Only visible to agency users -->
            <div v-if="isAgencyUser && agency" class="mt-4">
                <h2 class="text-xl font-semibold mb-4 text-secondary">Información de la Agencia</h2>
                <div class="bg-white p-6 rounded-xl shadow-md dark:bg-gray-800">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-[#DC8960]/10 text-[#DC8960] mr-4">
                            <Building2 class="h-6 w-6" />
                        </div>
                        <h2 class="text-xl font-semibold">{{ agency.name }}</h2>
                    </div>
                    <div class="grid gap-4 md:grid-cols-2">
                        <div v-if="agency.description" class="mb-4">
                            <h3 class="font-medium mb-2">Descripción</h3>
                            <p class="text-gray-600 dark:text-gray-300">{{ agency.description }}</p>
                        </div>
                        <div class="space-y-2">
                            <p v-if="agency.address" class="text-gray-600 dark:text-gray-300"><strong>Dirección:</strong> {{ agency.address }}</p>
                            <p v-if="agency.city" class="text-gray-600 dark:text-gray-300"><strong>Ciudad:</strong> {{ agency.city }}</p>
                            <p v-if="agency.phone" class="text-gray-600 dark:text-gray-300"><strong>Teléfono:</strong> {{ agency.phone }}</p>
                            <p v-if="agency.email" class="text-gray-600 dark:text-gray-300"><strong>Email:</strong> {{ agency.email }}</p>
                            <p v-if="agency.website" class="text-gray-600 dark:text-gray-300"><strong>Web:</strong> {{ agency.website }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
