<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import { ref } from 'vue';
import { Book, Map, Coffee, Box, Building2, CalendarDays, Newspaper, User, Settings, HelpCircle } from 'lucide-vue-next';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Panel',
    href: '/dashboard',
  },
  {
    title: 'Documentación',
    href: '/dashboard/documentation',
  },
];

const activeSection = ref('intro');

const sections = [
  { id: 'intro', title: 'Introducción', icon: Book },
  { id: 'news-events', title: 'Noticias y Eventos', icon: Newspaper },
  { id: 'locations', title: 'Ubicaciones', icon: Map },
  { id: 'experiences', title: 'Experiencias', icon: Coffee },
  { id: 'story-tours', title: 'StoryTours', icon: Box },
  { id: 'agencies', title: 'Operadores y Agencias', icon: Building2 },
  { id: 'users', title: 'Usuarios', icon: User },
  { id: 'settings', title: 'Configuración', icon: Settings },
  { id: 'faq', title: 'Preguntas Frecuentes', icon: HelpCircle },
];
</script>

<template>
  <Head title="Documentación" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-primary">Documentación de Cork Experience</h1>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Sidebar Navigation -->
        <div class="md:col-span-1">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-4 sticky top-4">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Contenido</h2>
            <nav>
              <ul class="space-y-2">
                <li v-for="section in sections" :key="section.id">
                  <button
                    @click="activeSection = section.id"
                    :class="[
                      'w-full flex items-center px-3 py-2 rounded-md text-left transition-colors',
                      activeSection === section.id
                        ? 'bg-primary/10 text-primary font-medium'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                    ]"
                  >
                    <component :is="section.icon" class="h-4 w-4 mr-2" />
                    <span>{{ section.title }}</span>
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>

        <!-- Content Area -->
        <div class="md:col-span-3">
          <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
            <!-- Introduction -->
            <div v-if="activeSection === 'intro'" class="prose dark:prose-invert max-w-none">
              <h2>Bienvenido a Cork Experience</h2>
              <p>
                Cork Experience es una aplicación móvil diseñada para ofrecer experiencias interactivas y enriquecedoras a los visitantes de la región. Esta documentación está dirigida a los administradores y gestores de contenido que utilizarán el panel de administración para mantener la aplicación actualizada.
              </p>
              <p>
                La aplicación está optimizada para dispositivos móviles y ofrece las siguientes funcionalidades principales:
              </p>
              <ul>
                <li>Gestión de noticias y eventos</li>
                <li>Ubicaciones en mapa interactivo</li>
                <li>Experiencias, alojamientos y restaurantes</li>
                <li>StoryTours con contenido multimedia y realidad aumentada</li>
                <li>Gestión de operadores y agencias</li>
              </ul>
              <p>
                Esta documentación le guiará a través de todas las funcionalidades disponibles en el panel de administración y le proporcionará consejos para optimizar la experiencia del usuario.
              </p>
              <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-blue-800 dark:text-blue-300 text-lg font-medium mb-2">Importante</h3>
                <p class="text-blue-700 dark:text-blue-400">
                  Cork Experience está diseñada principalmente para dispositivos móviles. Los usuarios que accedan desde un ordenador serán redirigidos a una página que les invitará a escanear un código QR para acceder desde su dispositivo móvil.
                </p>
              </div>
            </div>

            <!-- News and Events -->
            <div v-if="activeSection === 'news-events'" class="prose dark:prose-invert max-w-none">
              <h2>Noticias y Eventos</h2>
              <p>
                La sección de Noticias y Eventos permite gestionar todo el contenido informativo y los eventos programados en la aplicación.
              </p>
              
              <h3>Gestión de Noticias</h3>
              <p>
                Las noticias son publicaciones informativas que aparecen en la aplicación. Para crear una nueva noticia:
              </p>
              <ol>
                <li>Vaya a <strong>Noticias y Eventos</strong> en el menú lateral</li>
                <li>Seleccione la pestaña <strong>Noticias</strong></li>
                <li>Haga clic en <strong>Crear Noticia</strong></li>
                <li>Complete el formulario con título, contenido e imagen (opcional)</li>
                <li>Marque <strong>Publicado</strong> si desea que la noticia sea visible inmediatamente</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <h3>Gestión de Eventos</h3>
              <p>
                Los eventos son actividades programadas con fecha y ubicación. Para crear un nuevo evento:
              </p>
              <ol>
                <li>Vaya a <strong>Noticias y Eventos</strong> en el menú lateral</li>
                <li>Seleccione la pestaña <strong>Eventos</strong></li>
                <li>Haga clic en <strong>Crear Evento</strong></li>
                <li>Complete el formulario con título, descripción, ubicación, fechas e imagen (opcional)</li>
                <li>Marque <strong>Destacado</strong> si desea que el evento aparezca en la sección destacada</li>
                <li>Marque <strong>Activo</strong> si desea que el evento sea visible</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <div class="bg-yellow-50 dark:bg-yellow-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-yellow-800 dark:text-yellow-300 text-lg font-medium mb-2">Consejo</h3>
                <p class="text-yellow-700 dark:text-yellow-400">
                  Para una mejor experiencia del usuario, mantenga las imágenes de eventos y noticias en formato 16:9 y con un tamaño máximo de 2MB.
                </p>
              </div>
            </div>

            <!-- Locations -->
            <div v-if="activeSection === 'locations'" class="prose dark:prose-invert max-w-none">
              <h2>Ubicaciones</h2>
              <p>
                La sección de Ubicaciones permite gestionar los puntos de interés que aparecen en el mapa interactivo de la aplicación.
              </p>
              
              <h3>Gestión de Ubicaciones</h3>
              <p>
                Para crear una nueva ubicación:
              </p>
              <ol>
                <li>Vaya a <strong>Ubicaciones</strong> en el menú lateral</li>
                <li>Haga clic en <strong>Crear Ubicación</strong></li>
                <li>Complete el formulario con nombre, descripción, coordenadas (latitud y longitud) e imagen (opcional)</li>
                <li>Seleccione el tipo de ubicación (punto de interés, restaurante, alojamiento, etc.)</li>
                <li>Marque <strong>Activo</strong> si desea que la ubicación sea visible</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <h3>Obtener Coordenadas</h3>
              <p>
                Para obtener las coordenadas exactas de una ubicación:
              </p>
              <ol>
                <li>Abra Google Maps en su navegador</li>
                <li>Busque la ubicación deseada</li>
                <li>Haga clic derecho en el punto exacto</li>
                <li>Seleccione "¿Qué hay aquí?"</li>
                <li>En la parte inferior aparecerán las coordenadas (latitud, longitud)</li>
                <li>Copie estos valores en los campos correspondientes del formulario</li>
              </ol>
              
              <div class="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-green-800 dark:text-green-300 text-lg font-medium mb-2">Importante</h3>
                <p class="text-green-700 dark:text-green-400">
                  Las ubicaciones son fundamentales para otras funcionalidades como Experiencias y StoryTours. Se recomienda crear primero las ubicaciones antes de crear contenido relacionado.
                </p>
              </div>
            </div>

            <!-- Experiences -->
            <div v-if="activeSection === 'experiences'" class="prose dark:prose-invert max-w-none">
              <h2>Experiencias</h2>
              <p>
                La sección de Experiencias permite gestionar las actividades, alojamientos y restaurantes disponibles en la aplicación.
              </p>
              
              <h3>Tipos de Experiencias</h3>
              <p>
                Cork Experience maneja tres tipos principales de experiencias:
              </p>
              <ul>
                <li><strong>Actividades:</strong> Experiencias turísticas, culturales o de ocio</li>
                <li><strong>Alojamientos:</strong> Hoteles, casas rurales, apartamentos, etc.</li>
                <li><strong>Restaurantes:</strong> Establecimientos gastronómicos</li>
              </ul>
              
              <h3>Crear una Nueva Experiencia</h3>
              <p>
                Para crear una nueva experiencia:
              </p>
              <ol>
                <li>Vaya a <strong>Experiencias</strong> en el menú lateral</li>
                <li>Haga clic en <strong>Crear Experiencia</strong></li>
                <li>Seleccione el tipo de experiencia (actividad, alojamiento o restaurante)</li>
                <li>Complete el formulario con título, descripción, ubicación, precio (opcional) e imágenes</li>
                <li>Añada detalles específicos según el tipo de experiencia</li>
                <li>Marque <strong>Destacado</strong> si desea que la experiencia aparezca en la sección destacada</li>
                <li>Marque <strong>Activo</strong> si desea que la experiencia sea visible</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <h3>Gestión de Imágenes</h3>
              <p>
                Las experiencias pueden tener múltiples imágenes. Para una mejor presentación:
              </p>
              <ul>
                <li>Utilice imágenes de alta calidad (mínimo 1200x800 píxeles)</li>
                <li>Mantenga una proporción consistente en todas las imágenes</li>
                <li>La primera imagen será la principal en los listados</li>
                <li>Optimice las imágenes para web antes de subirlas</li>
              </ul>
              
              <div class="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-purple-800 dark:text-purple-300 text-lg font-medium mb-2">Consejo</h3>
                <p class="text-purple-700 dark:text-purple-400">
                  Para mejorar la visibilidad en las búsquedas, utilice palabras clave relevantes en el título y la descripción de las experiencias.
                </p>
              </div>
            </div>

            <!-- StoryTours -->
            <div v-if="activeSection === 'story-tours'" class="prose dark:prose-invert max-w-none">
              <h2>StoryTours</h2>
              <p>
                Los StoryTours son experiencias interactivas que combinan contenido multimedia (audio, video) y realidad aumentada para ofrecer una experiencia inmersiva a los usuarios.
              </p>
              
              <h3>Tipos de StoryTours</h3>
              <p>
                Cork Experience ofrece varios tipos de StoryTours:
              </p>
              <ul>
                <li><strong>Paisaje:</strong> Centrados en entornos naturales y paisajísticos</li>
                <li><strong>Patrimonio:</strong> Enfocados en monumentos y patrimonio histórico</li>
                <li><strong>Cultura:</strong> Relacionados con aspectos culturales y tradiciones</li>
                <li><strong>Gastronomía:</strong> Centrados en la oferta gastronómica</li>
                <li><strong>Naturaleza:</strong> Enfocados en flora, fauna y espacios naturales</li>
              </ul>
              
              <h3>Crear un Nuevo StoryTour</h3>
              <p>
                Para crear un nuevo StoryTour:
              </p>
              <ol>
                <li>Vaya a <strong>StoryTours</strong> en el menú lateral</li>
                <li>Haga clic en <strong>Crear StoryTour</strong></li>
                <li>Complete el formulario con título, descripción, tipo y ubicación</li>
                <li>Suba una imagen representativa</li>
                <li>Añada archivos multimedia:
                  <ul>
                    <li><strong>Audio:</strong> Narración o efectos sonoros (formatos MP3, WAV, OGG)</li>
                    <li><strong>Video:</strong> Contenido visual complementario (formatos MP4, MOV)</li>
                    <li><strong>Modelo AR:</strong> Modelos 3D para experiencias de realidad aumentada (formatos GLB, GLTF)</li>
                  </ul>
                </li>
                <li>Marque <strong>Destacado</strong> si desea que el StoryTour aparezca en la sección destacada</li>
                <li>Marque <strong>Activo</strong> si desea que el StoryTour sea visible</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <h3>Recomendaciones para Archivos Multimedia</h3>
              <p>
                Para optimizar la experiencia del usuario:
              </p>
              <ul>
                <li><strong>Audio:</strong> Duración máxima recomendada de 3-5 minutos, calidad mínima de 128kbps</li>
                <li><strong>Video:</strong> Duración máxima recomendada de 2-3 minutos, resolución mínima de 720p</li>
                <li><strong>Modelos AR:</strong> Optimizados para móviles (máximo 10MB), con texturas comprimidas</li>
              </ul>
              
              <div class="bg-orange-50 dark:bg-orange-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-orange-800 dark:text-orange-300 text-lg font-medium mb-2">Importante</h3>
                <p class="text-orange-700 dark:text-orange-400">
                  Los modelos AR deben estar optimizados para dispositivos móviles. Se recomienda probar los modelos en diferentes dispositivos antes de publicarlos.
                </p>
              </div>
            </div>

            <!-- Agencies -->
            <div v-if="activeSection === 'agencies'" class="prose dark:prose-invert max-w-none">
              <h2>Operadores y Agencias</h2>
              <p>
                La sección de Operadores y Agencias permite gestionar las empresas y organizaciones que colaboran con Cork Experience.
              </p>
              
              <h3>Gestión de Agencias</h3>
              <p>
                Para crear una nueva agencia:
              </p>
              <ol>
                <li>Vaya a <strong>Operadores y Agencias</strong> en el menú lateral</li>
                <li>Haga clic en <strong>Crear Agencia</strong></li>
                <li>Complete el formulario con nombre, descripción, información de contacto y logo</li>
                <li>Añada la ubicación si la agencia tiene una oficina física</li>
                <li>Marque <strong>Activo</strong> si desea que la agencia sea visible</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <h3>Gestión de Usuarios de Agencias</h3>
              <p>
                Cada agencia puede tener usuarios asociados que pueden gestionar su contenido:
              </p>
              <ol>
                <li>Vaya a la página de detalle de la agencia</li>
                <li>Haga clic en <strong>Gestionar Usuarios</strong></li>
                <li>Para añadir un usuario:
                  <ul>
                    <li>Seleccione un usuario existente o cree uno nuevo</li>
                    <li>Asigne los permisos correspondientes</li>
                    <li>Haga clic en <strong>Añadir Usuario</strong></li>
                  </ul>
                </li>
                <li>Para eliminar un usuario, haga clic en <strong>Eliminar</strong> junto al usuario correspondiente</li>
              </ol>
              
              <div class="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-red-800 dark:text-red-300 text-lg font-medium mb-2">Precaución</h3>
                <p class="text-red-700 dark:text-red-400">
                  Los usuarios de agencias solo pueden gestionar el contenido relacionado con su propia agencia. Asegúrese de asignar los permisos correctos para evitar accesos no autorizados.
                </p>
              </div>
            </div>

            <!-- Users -->
            <div v-if="activeSection === 'users'" class="prose dark:prose-invert max-w-none">
              <h2>Usuarios</h2>
              <p>
                La sección de Usuarios permite gestionar las cuentas de administradores y usuarios de la plataforma.
              </p>
              
              <h3>Tipos de Usuarios</h3>
              <p>
                Cork Experience maneja varios tipos de usuarios:
              </p>
              <ul>
                <li><strong>Superadministradores:</strong> Acceso completo a todas las funcionalidades</li>
                <li><strong>Administradores:</strong> Acceso a la mayoría de las funcionalidades, excepto configuraciones sensibles</li>
                <li><strong>Editores:</strong> Pueden crear y editar contenido, pero no pueden eliminar</li>
                <li><strong>Usuarios de Agencia:</strong> Acceso limitado al contenido de su agencia</li>
                <li><strong>Usuarios Registrados:</strong> Usuarios de la aplicación móvil</li>
              </ul>
              
              <h3>Crear un Nuevo Usuario Administrativo</h3>
              <p>
                Para crear un nuevo usuario administrativo:
              </p>
              <ol>
                <li>Vaya a <strong>Usuarios</strong> en el menú lateral</li>
                <li>Haga clic en <strong>Crear Usuario</strong></li>
                <li>Complete el formulario con nombre, correo electrónico y contraseña</li>
                <li>Seleccione el rol adecuado</li>
                <li>Asigne permisos específicos si es necesario</li>
                <li>Haga clic en <strong>Guardar</strong></li>
              </ol>
              
              <h3>Gestión de Permisos</h3>
              <p>
                Los permisos determinan qué acciones puede realizar cada usuario:
              </p>
              <ul>
                <li>Vaya a la página de edición del usuario</li>
                <li>En la sección de permisos, marque o desmarque los permisos según sea necesario</li>
                <li>Los permisos están agrupados por módulo (Noticias, Eventos, Experiencias, etc.)</li>
                <li>Cada módulo tiene permisos de lectura, creación, edición y eliminación</li>
                <li>Haga clic en <strong>Guardar</strong> para aplicar los cambios</li>
              </ul>
              
              <div class="bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-indigo-800 dark:text-indigo-300 text-lg font-medium mb-2">Seguridad</h3>
                <p class="text-indigo-700 dark:text-indigo-400">
                  Se recomienda seguir el principio de mínimo privilegio: asigne a cada usuario solo los permisos que necesita para realizar sus tareas.
                </p>
              </div>
            </div>

            <!-- Settings -->
            <div v-if="activeSection === 'settings'" class="prose dark:prose-invert max-w-none">
              <h2>Configuración</h2>
              <p>
                La sección de Configuración permite personalizar diversos aspectos de la aplicación Cork Experience.
              </p>
              
              <h3>Configuración General</h3>
              <p>
                Aquí puede modificar los ajustes básicos de la aplicación:
              </p>
              <ul>
                <li><strong>Nombre de la Aplicación:</strong> El nombre que aparece en la cabecera y el título</li>
                <li><strong>Descripción:</strong> Una breve descripción utilizada en metadatos</li>
                <li><strong>Logotipos:</strong> Los diferentes logotipos utilizados en la aplicación</li>
                <li><strong>Colores:</strong> La paleta de colores principal</li>
                <li><strong>Información de Contacto:</strong> Correo electrónico, teléfono, etc.</li>
              </ul>
              
              <h3>Configuración de Correo Electrónico</h3>
              <p>
                Configure los ajustes para el envío de correos electrónicos:
              </p>
              <ul>
                <li><strong>Servidor SMTP:</strong> Configuración del servidor de correo saliente</li>
                <li><strong>Remitente:</strong> Dirección de correo y nombre del remitente</li>
                <li><strong>Plantillas:</strong> Personalización de las plantillas de correo</li>
              </ul>
              
              <h3>Configuración de API</h3>
              <p>
                Gestione las claves y configuraciones de APIs externas:
              </p>
              <ul>
                <li><strong>Google Maps:</strong> Clave de API para la integración de mapas</li>
                <li><strong>Servicios de Pago:</strong> Configuración para pasarelas de pago (si aplica)</li>
                <li><strong>Redes Sociales:</strong> Claves para integración con redes sociales</li>
              </ul>
              
              <div class="bg-teal-50 dark:bg-teal-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-teal-800 dark:text-teal-300 text-lg font-medium mb-2">Importante</h3>
                <p class="text-teal-700 dark:text-teal-400">
                  Los cambios en la configuración pueden afectar al funcionamiento de toda la aplicación. Se recomienda realizar pruebas después de cualquier modificación.
                </p>
              </div>
            </div>

            <!-- FAQ -->
            <div v-if="activeSection === 'faq'" class="prose dark:prose-invert max-w-none">
              <h2>Preguntas Frecuentes</h2>
              
              <div class="space-y-6">
                <div>
                  <h3>¿Cómo puedo cambiar mi contraseña?</h3>
                  <p>
                    Para cambiar su contraseña, vaya a su perfil de usuario haciendo clic en su nombre en la esquina superior derecha y seleccione "Perfil". En la sección de seguridad, haga clic en "Cambiar contraseña" y siga las instrucciones.
                  </p>
                </div>
                
                <div>
                  <h3>¿Cómo puedo recuperar contenido eliminado?</h3>
                  <p>
                    El contenido eliminado no se puede recuperar directamente desde la interfaz. Si necesita recuperar contenido eliminado, contacte con el administrador del sistema para restaurarlo desde una copia de seguridad.
                  </p>
                </div>
                
                <div>
                  <h3>¿Cuál es el tamaño máximo de archivo para las imágenes?</h3>
                  <p>
                    El tamaño máximo para las imágenes es de 2MB. Para archivos de audio, el límite es de 10MB, para vídeos 50MB y para modelos AR 20MB.
                  </p>
                </div>
                
                <div>
                  <h3>¿Cómo puedo hacer que un contenido aparezca destacado en la aplicación?</h3>
                  <p>
                    Para destacar un contenido, edite el elemento (noticia, evento, experiencia o StoryTour) y marque la casilla "Destacado". Guarde los cambios y el contenido aparecerá en las secciones destacadas correspondientes.
                  </p>
                </div>
                
                <div>
                  <h3>¿Puedo programar la publicación de contenido para una fecha futura?</h3>
                  <p>
                    Actualmente, la plataforma no permite programar publicaciones para fechas futuras. Puede crear el contenido y mantenerlo como inactivo hasta que desee publicarlo, momento en el cual deberá editarlo y marcarlo como activo manualmente.
                  </p>
                </div>
                
                <div>
                  <h3>¿Cómo puedo ver cómo se ve mi contenido en la aplicación móvil?</h3>
                  <p>
                    Para previsualizar el contenido como se vería en la aplicación móvil, puede utilizar la función "Vista previa" disponible en la página de detalle de cada elemento. También puede acceder a la aplicación desde su dispositivo móvil para ver el contenido publicado.
                  </p>
                </div>
                
                <div>
                  <h3>¿Qué formatos de archivo son compatibles con los modelos AR?</h3>
                  <p>
                    Los formatos compatibles para modelos AR son GLB y GLTF, que son estándares para contenido 3D en la web. Estos formatos incluyen geometría, materiales y animaciones en un formato eficiente.
                  </p>
                </div>
                
                <div>
                  <h3>¿Cómo puedo reportar un problema técnico?</h3>
                  <p>
                    Para reportar problemas técnicos, utilice la sección "Soporte" en el menú de usuario o envíe un correo electró<NAME_EMAIL> con los detalles del problema, incluyendo capturas de pantalla si es posible.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
