<script setup lang="ts">
import AppContent from '@/components/AppContent.vue';
import AppShell from '@/components/AppShell.vue';
import AppSidebar from '@/components/AppSidebar.vue';
import AgencyUserSidebar from '@/components/AgencyUserSidebar.vue';
import AppSidebarHeader from '@/components/AppSidebarHeader.vue';
import type { BreadcrumbItemType } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Props {
    breadcrumbs?: BreadcrumbItemType[];
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
});

const page = usePage();
const user = page.props.auth?.user || {};

const isAgencyUser = computed(() => {
    return user.agency_id !== null && user.agency_id !== undefined && user.role !== 'superadmin';
});

const isSuperAdmin = computed(() => {
    return user.role === 'superadmin';
});

// Default to AppSidebar if we can't determine the user role
const showAgencyUserSidebar = computed(() => {
    return isAgencyUser.value && !isSuperAdmin.value;
});
</script>

<template>
    <AppShell variant="sidebar">
        <!-- Use the appropriate sidebar based on user role -->
        <template v-if="showAgencyUserSidebar">
            <AgencyUserSidebar />
        </template>
        <template v-else>
            <AppSidebar />
        </template>

        <AppContent variant="sidebar">
            <AppSidebarHeader :breadcrumbs="breadcrumbs" />
            <slot />
        </AppContent>
    </AppShell>
</template>
