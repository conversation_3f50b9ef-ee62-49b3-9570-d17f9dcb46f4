#!/bin/bash

# Create the welcome directory in storage if it doesn't exist
mkdir -p storage/app/public/welcome
mkdir -p storage/app/public/images
mkdir -p storage/app/public/videos

# Copy welcome assets from frontend to storage
cp ../frontend/public/assets/logos/CorkExpLogoInicial.png storage/app/public/welcome/
cp ../frontend/public/assets/logos/CorkExpLogo.png storage/app/public/welcome/
cp ../frontend/public/assets/images/videoplaceholder.jpeg storage/app/public/images/

echo "Welcome assets copied to storage/app/public/"
