#!/bin/bash

# Create the events directory in storage if it doesn't exist
mkdir -p storage/app/public/events

# Copy sample images from frontend to storage
cp ../frontend/public/assets/images/events/CorkExpEvent.jpeg storage/app/public/events/
cp ../frontend/public/assets/images/events/CorkExpEvent2.jpeg storage/app/public/events/
cp ../frontend/public/assets/images/events/CorkExpEvent3.jpeg storage/app/public/events/

echo "Sample event images copied to storage/app/public/events/"
