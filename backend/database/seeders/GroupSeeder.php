<?php

namespace Database\Seeders;

use App\Models\Agency;
use App\Models\Group;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first agency
        $agency = Agency::first();
        
        if (!$agency) {
            $this->command->info('No agencies found. Please run AgencySeeder first.');
            return;
        }
        
        // Get the superadmin user
        $superadmin = User::where('role', 'superadmin')->first();
        
        if (!$superadmin) {
            $this->command->info('No superadmin user found.');
            return;
        }
        
        // Get the regular user
        $regularUser = User::where('email', '<EMAIL>')->first();
        
        if (!$regularUser) {
            $this->command->info('No regular user found.');
            return;
        }
        
        // Get the agency user
        $agencyUser = User::where('email', '<EMAIL>')->first();
        
        if (!$agencyUser) {
            $this->command->info('No agency user found.');
            return;
        }
        
        // Create a test group
        $group = Group::create([
            'name' => 'Grupo de Prueba',
            'description' => 'Este es un grupo de prueba para la aplicación',
            'type' => 'mixed',
            'agency_id' => $agency->id,
            'created_by' => $superadmin->id,
            'is_active' => true,
        ]);
        
        // Add users to the group
        $group->users()->attach([
            $regularUser->id => ['is_guide' => false],
            $agencyUser->id => ['is_guide' => true],
        ]);
        
        // Create a family group
        $familyGroup = Group::create([
            'name' => 'Familia García',
            'description' => 'Grupo familiar para excursiones',
            'type' => 'family',
            'agency_id' => $agency->id,
            'created_by' => $superadmin->id,
            'is_active' => true,
        ]);
        
        // Add users to the family group
        $familyGroup->users()->attach([
            $regularUser->id => ['is_guide' => false],
        ]);
        
        // Create a couple group
        $coupleGroup = Group::create([
            'name' => 'Pareja Aventurera',
            'description' => 'Grupo para parejas que buscan aventuras',
            'type' => 'couple',
            'agency_id' => $agency->id,
            'created_by' => $superadmin->id,
            'is_active' => true,
        ]);
        
        // Add users to the couple group
        $coupleGroup->users()->attach([
            $regularUser->id => ['is_guide' => false],
        ]);
        
        $this->command->info('Groups seeded successfully.');
    }
}
