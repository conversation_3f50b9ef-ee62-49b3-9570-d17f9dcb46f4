<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AppSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Welcome screen settings
        $welcomeSettings = [
            [
                'key' => 'welcome_title',
                'value' => 'Bienvenido | Cork Experience',
                'type' => 'text',
                'group' => 'welcome',
                'label' => 'Título de Bienvenida',
                'description' => 'Título mostrado en la pantalla de bienvenida',
                'is_public' => true,
            ],
            [
                'key' => 'video_title',
                'value' => 'Cork Experience',
                'type' => 'text',
                'group' => 'welcome',
                'label' => 'Título del Video',
                'description' => 'Título mostrado en la pantalla de video',
                'is_public' => true,
            ],
            [
                'key' => 'video_description',
                'value' => 'Los paisajes del corcho son los paisajes de Quercus suber, una especie endémica de la cuenca mediterránea. La mitad de la superficie mundial de alcornocal se encuentra en la Península Ibérica, sobre todo en su zona suroccidental, una buena parte al norte de África, y el resto se reparte entre el sur de Francia, el litoral oeste de Italia y las islas de Córcega, Cerdeña y Sicilia.\n\nLa diversidad del paisaje corchero es uno de sus bienes más preciados. No nos debe extrañar si tenemos en cuenta que su área de distribución tiene características climáticas muy distintas, así como la intensa y antigua relación del hombre con este ecosistema.',
                'type' => 'textarea',
                'group' => 'welcome',
                'label' => 'Descripción del Video',
                'description' => 'Texto descriptivo mostrado debajo del video',
                'is_public' => true,
            ],
            [
                'key' => 'welcome_logo',
                'value' => 'welcome/CorkExpLogoInicial.png',
                'type' => 'image',
                'group' => 'welcome',
                'label' => 'Logo de Bienvenida',
                'description' => 'Logo mostrado en la pantalla de bienvenida',
                'is_public' => true,
            ],
            [
                'key' => 'video_logo',
                'value' => 'welcome/CorkExpLogo.png',
                'type' => 'image',
                'group' => 'welcome',
                'label' => 'Logo del Video',
                'description' => 'Logo mostrado en la pantalla de video',
                'is_public' => true,
            ],
            [
                'key' => 'welcome_video',
                'value' => null, // Will be set by the frontend
                'type' => 'video',
                'group' => 'welcome',
                'label' => 'Video de Bienvenida',
                'description' => 'Video mostrado en la pantalla de bienvenida',
                'is_public' => true,
            ],
            [
                'key' => 'video_placeholder',
                'value' => 'images/videoplaceholder.jpeg',
                'type' => 'image',
                'group' => 'welcome',
                'label' => 'Imagen de Placeholder del Video',
                'description' => 'Imagen mostrada como placeholder del video',
                'is_public' => true,
            ],
        ];

        foreach ($welcomeSettings as $setting) {
            \App\Models\AppSetting::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
