<?php

namespace Database\Seeders;

use App\Models\Location;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 15 random locations
        Location::factory()->count(15)->create();

        // Create specific featured locations
        Location::factory()->create([
            'name' => 'Hotel Albarragena',
            'description' => 'Situado en el centro histórico de Cáceres, a 200 metros de la Plaza Mayor, el Hotel Albarragena ofrece habitaciones con aire acondicionado y conexión Wi-Fi gratuita.',
            'address' => 'Calle Parras, 8, 10004 Cáceres, España',
            'latitude' => 39.4753,
            'longitude' => -6.3724,
            'image' => 'hotel1.jpeg',
            'type' => 'hotel',
            'is_featured' => true,
        ]);

        Location::factory()->create([
            'name' => 'Restaurante Torre de Sande',
            'description' => 'Restaurante de cocina tradicional extremeña ubicado en un palacio del siglo XV en el corazón del casco antiguo de Cáceres.',
            'address' => 'Calle Condes, 3, 10003 Cáceres, España',
            'latitude' => 39.4758,
            'longitude' => -6.3719,
            'image' => 'restaurant1.jpeg',
            'type' => 'restaurant',
            'is_featured' => true,
        ]);

        Location::factory()->create([
            'name' => 'Museo del Corcho',
            'description' => 'El Museo del Corcho es una institución que se dedica a la interpretación y difusión del patrimonio y los territorios vinculados al mundo del corcho.',
            'address' => 'Calle Convento, 10, 10003 Cáceres, España',
            'latitude' => 39.4760,
            'longitude' => -6.3730,
            'image' => 'museum1.jpeg',
            'type' => 'museum',
            'is_featured' => true,
        ]);
    }
}
