<?php

namespace Database\Seeders;

use App\Models\Experience;
use Illuminate\Database\Seeder;

class ExperiencesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample experiences with new classification system

        // RUTA - Individual routes/paths
        Experience::create([
            'title' => 'Ruta de los Alcornocales',
            'description' => 'Recorre los alcornocales en bicicleta eléctrica y descubre la belleza natural de este ecosistema único. Una ruta autoguiada que te llevará por los paisajes más espectaculares.',
            'short_description' => 'El más grande alcornocal de la península ibérica y uno de los más importantes del mundo.',
            'location_id' => null,
            'agency_id' => 1,
            'type' => 'ruta',
            'duration' => '3 horas',
            'distance' => '12 km',
            'difficulty' => 'moderate',
            'price' => null, // Routes are typically free
            'image' => 'experiences/alcornocales.jpeg',
            'start_date' => null,
            'end_date' => null,
            'is_featured' => true,
            'is_active' => true,
        ]);

        // PAQUETE - Bundle of activities provided by an agency
        Experience::create([
            'title' => 'Paquete Cork Experience Completo',
            'description' => 'Paquete completo que incluye visita guiada a la fábrica de corcho, alojamiento en hotel sostenible, actividades gastronómicas y ruta por los alcornocales. Una experiencia integral de 3 días.',
            'short_description' => 'Experiencia completa de 3 días que incluye alojamiento, visitas guiadas y actividades gastronómicas.',
            'location_id' => null,
            'agency_id' => 1,
            'type' => 'paquete',
            'duration' => '3 días',
            'distance' => null,
            'difficulty' => 'easy',
            'price' => 285.00,
            'image' => 'experiences/cordesuro.png',
            'start_date' => '2025-04-01',
            'end_date' => '2025-10-31',
            'is_featured' => true,
            'is_active' => true,
            'available_time_slots' => ['09:00', '14:00'],
            'capacity_per_time_slot' => 12,
        ]);

        // ACTIVIDADES - Single-day experiences
        Experience::create([
            'title' => 'Taller de Artesanía con Corcho',
            'description' => 'Actividad de un día donde aprenderás las técnicas tradicionales de trabajo con corcho. Incluye materiales y refrigerio. Perfecto para familias y grupos.',
            'short_description' => 'Aprende técnicas tradicionales de artesanía con corcho en un taller práctico.',
            'location_id' => null,
            'agency_id' => 1,
            'type' => 'actividades',
            'duration' => '4 horas',
            'distance' => null,
            'difficulty' => 'easy',
            'price' => 35.00,
            'image' => 'experiences/hotelcasacormoran.jpeg',
            'start_date' => '2025-04-01',
            'end_date' => '2025-12-31',
            'is_featured' => false,
            'is_active' => true,
            'available_time_slots' => ['10:00', '16:00'],
            'capacity_per_time_slot' => 8,
        ]);

        // Additional ACTIVIDADES - Seminar
        Experience::create([
            'title' => 'Seminario de Sostenibilidad y Corcho',
            'description' => 'Seminario educativo sobre la importancia del corcho en la sostenibilidad ambiental. Incluye conferencias de expertos y visita a plantaciones.',
            'short_description' => 'Seminario educativo sobre sostenibilidad y la importancia del corcho.',
            'location_id' => null,
            'agency_id' => 1,
            'type' => 'actividades',
            'duration' => '6 horas',
            'distance' => null,
            'difficulty' => 'easy',
            'price' => 45.00,
            'image' => 'experiences/alcornocales.jpeg',
            'start_date' => '2025-05-15',
            'end_date' => '2025-09-30',
            'is_featured' => false,
            'is_active' => true,
            'available_time_slots' => ['09:00'],
            'capacity_per_time_slot' => 25,
        ]);

        // ACTIVIDADES - Only available as part of package
        Experience::create([
            'title' => 'Cata de Vinos con Tapones de Corcho',
            'description' => 'Experiencia exclusiva de cata de vinos que solo está disponible como parte del paquete completo. Aprende sobre la relación entre el corcho y el vino.',
            'short_description' => 'Cata exclusiva disponible solo como parte de paquetes.',
            'location_id' => null,
            'agency_id' => 1,
            'type' => 'actividades',
            'duration' => '2 horas',
            'distance' => null,
            'difficulty' => 'easy',
            'price' => null, // Not reservable individually
            'image' => 'experiences/cordesuro.png',
            'start_date' => null,
            'end_date' => null,
            'is_featured' => false,
            'is_active' => true,
        ]);

        // Keep existing hotel for compatibility
        Experience::create([
            'title' => 'Hotel Casa Convento',
            'description' => 'Alojamiento único que destaca por su arquitectura sostenible, utilizando el corcho como material principal en su construcción.',
            'short_description' => 'Alojamiento sostenible construido con materiales de corcho, ofreciendo una experiencia única.',
            'location_id' => null,
            'agency_id' => 1,
            'type' => 'hotel',
            'duration' => null,
            'distance' => '8 km',
            'difficulty' => null,
            'price' => 85.00,
            'image' => 'experiences/hotelcasacormoran.jpeg',
            'start_date' => null,
            'end_date' => null,
            'is_featured' => true,
            'is_active' => true,
        ]);
    }
}
