<?php

namespace Database\Seeders;

use App\Models\Agency;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class AgencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a default agency
        Agency::create([
            'name' => 'Cork Experience',
            'slug' => 'cork-experience',
            'description' => 'The main agency for Cork Experience app.',
            'logo' => 'agencies/cork-experience-logo.png',
            'address' => 'Calle Principal 123',
            'city' => 'Badajoz',
            'state' => 'Extremadura',
            'country' => 'Spain',
            'postal_code' => '06001',
            'phone' => '+34 924 123 456',
            'email' => '<EMAIL>',
            'website' => 'https://corkexperience.com',
            'is_active' => true,
        ]);

        // Create some sample agencies
        Agency::factory()->count(5)->create();
    }
}
