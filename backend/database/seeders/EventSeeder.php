<?php

namespace Database\Seeders;

use App\Models\Event;
use Illuminate\Database\Seeder;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 10 random events
        Event::factory()->count(10)->create();

        // Create specific featured events
        Event::factory()->create([
            'title' => '"Cork Experience: el corcho, un mundo por descubrir"',
            'description' => 'Es un proyecto que tiene como objetivo desarrollar nuevos productos turísticos sostenibles basados en el corcho.',
            'start_datetime' => now()->addDays(5)->setTime(14, 30),
            'location' => 'Badajoz (España)',
            'image' => 'events/CorkExpEvent.jpeg',
            'rating' => 3.5,
            'is_featured' => true,
        ]);

        Event::factory()->create([
            'title' => 'Taller de cajas nido con corcho natural',
            'description' => 'En este taller los participantes tendrán la oportunidad de desarrollar sus ideas y desarrollar herramientas tradicionales y actuales con corcho, de forma más sostenible y práctica.',
            'start_datetime' => now()->addDays(10)->setTime(15, 30),
            'location' => 'Trujillo de Extremadura (España)',
            'image' => 'events/CorkExpEvent2.jpeg',
            'rating' => 4.5,
            'is_featured' => true,
        ]);

        Event::factory()->create([
            'title' => 'Museo del Corcho de Cataluña: "El corcho en clave de mujer"',
            'description' => 'El Museo del Corcho es una institución que se dedica a la interpretación y difusión del patrimonio y los territorios vinculados al mundo del corcho.',
            'start_datetime' => now()->addDays(15)->setTime(13, 0),
            'location' => 'Palafrugell (España)',
            'image' => 'events/CorkExpEvent3.jpeg',
            'rating' => 5.0,
            'is_featured' => true,
        ]);
    }
}
