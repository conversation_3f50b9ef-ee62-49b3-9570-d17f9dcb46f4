<?php

namespace Database\Seeders;

use App\Models\Route;
use App\Models\RoutePoint;
use Illuminate\Database\Seeder;

class RouteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Route 1: Ruta del Corcho
        $route1 = Route::create([
            'title' => 'Ruta del Corcho',
            'description' => 'Una ruta para descubrir el proceso de producción del corcho en Extremadura, desde la extracción en los alcornocales hasta su procesamiento en fábricas tradicionales. Aprenderás sobre la importancia económica y ecológica de este recurso natural.',
            'short_description' => 'Descubre el proceso del corcho',
            'duration' => '4 horas',
            'distance' => '12 km',
            'difficulty' => 'Moderada',
            'is_featured' => true,
            'is_active' => true,
        ]);

        // Route 2: Ruta de los Alcornocales
        $route2 = Route::create([
            'title' => 'Ruta de los Alcornocales',
            'description' => 'Una ruta para descubrir los mejores bosques de alcornoques de Extremadura y aprender sobre el proceso de extracción del corcho. Disfrutarás de paisajes impresionantes y podrás observar la flora y fauna autóctona de la región.',
            'short_description' => 'Descubre los alcornocales extremeños',
            'duration' => '6 horas',
            'distance' => '15 km',
            'difficulty' => 'Moderada',
            'is_featured' => true,
            'is_active' => true,
        ]);

        // Route 3: Ruta Histórica del Corcho
        $route3 = Route::create([
            'title' => 'Ruta Histórica del Corcho',
            'description' => 'Un recorrido por los lugares históricos relacionados con la industria del corcho en Extremadura. Visitarás antiguas fábricas, museos y centros de interpretación donde podrás conocer la evolución de esta importante actividad económica a lo largo de los siglos.',
            'short_description' => 'Historia y tradición del corcho',
            'duration' => '3 horas',
            'distance' => '8 km',
            'difficulty' => 'Fácil',
            'is_featured' => false,
            'is_active' => true,
        ]);

        // Add route points for Route 1
        RoutePoint::create([
            'route_id' => $route1->id,
            'location_id' => 1, // Fábrica de Corcho San Vicente
            'order' => 0,
            'description' => 'Inicio de la ruta en la fábrica donde podrás ver el proceso de transformación del corcho.',
        ]);

        RoutePoint::create([
            'route_id' => $route1->id,
            'location_id' => 2, // Alcornocal de Monfragüe
            'order' => 1,
            'description' => 'Visita al alcornocal para observar los árboles y el proceso de extracción del corcho.',
        ]);

        RoutePoint::create([
            'route_id' => $route1->id,
            'location_id' => 3, // Museo del Corcho
            'order' => 2,
            'description' => 'Finalización de la ruta en el museo donde aprenderás sobre la historia del corcho.',
        ]);

        // Add route points for Route 2
        RoutePoint::create([
            'route_id' => $route2->id,
            'location_id' => 2, // Alcornocal de Monfragüe
            'order' => 0,
            'description' => 'Inicio de la ruta en el corazón del alcornocal.',
        ]);

        RoutePoint::create([
            'route_id' => $route2->id,
            'location_id' => 4, // Mirador Sierra de San Pedro
            'order' => 1,
            'description' => 'Parada en el mirador para disfrutar de vistas panorámicas de los alcornocales.',
        ]);

        RoutePoint::create([
            'route_id' => $route2->id,
            'location_id' => 5, // Centro de Interpretación del Corcho
            'order' => 2,
            'description' => 'Finalización de la ruta con una visita al centro de interpretación.',
        ]);

        // Add route points for Route 3
        RoutePoint::create([
            'route_id' => $route3->id,
            'location_id' => 3, // Museo del Corcho
            'order' => 0,
            'description' => 'Inicio de la ruta histórica en el museo.',
        ]);

        RoutePoint::create([
            'route_id' => $route3->id,
            'location_id' => 1, // Fábrica de Corcho San Vicente
            'order' => 1,
            'description' => 'Visita a la fábrica tradicional para conocer los métodos históricos de procesamiento.',
        ]);

        RoutePoint::create([
            'route_id' => $route3->id,
            'location_id' => 5, // Centro de Interpretación del Corcho
            'order' => 2,
            'description' => 'Finalización de la ruta en el centro de interpretación con exposiciones históricas.',
        ]);
    }
}
