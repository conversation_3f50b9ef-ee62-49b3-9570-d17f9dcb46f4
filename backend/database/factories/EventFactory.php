<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = fake()->dateTimeBetween('now', '+2 months');
        $endDate = clone $startDate;
        $endDate->modify('+' . rand(1, 4) . ' hours');

        return [
            'title' => fake()->sentence(),
            'description' => fake()->paragraph(3),
            'start_datetime' => $startDate,
            'end_datetime' => $endDate,
            'location' => fake()->city() . ' (España)',
            'image' => 'CorkExpEvent' . rand(1, 3) . '.jpeg',
            'rating' => fake()->randomFloat(1, 3, 5),
            'is_featured' => fake()->boolean(20),
        ];
    }
}
