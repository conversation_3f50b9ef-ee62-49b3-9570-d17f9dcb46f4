<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Location>
 */
class LocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Spanish cities coordinates
        $locations = [
            ['Badajoz', 38.8794, -6.9706],
            ['Cáceres', 39.4753, -6.3724],
            ['M<PERSON>rida', 38.9122, -6.3436],
            ['Plasencia', 40.0294, -6.0858],
            ['Trujillo', 39.4589, -5.8830],
            ['Zaf<PERSON>', 38.4242, -6.4164],
            ['<PERSON><PERSON><PERSON><PERSON><PERSON>', 38.6861, -6.4081],
            ['<PERSON>', 38.9569, -5.8619],
            ['Villanueva de la Serena', 38.9750, -5.8008],
            ['Navalmoral de la Mata', 39.8919, -5.5428],
        ];

        $location = fake()->randomElement($locations);
        $types = ['point', 'hotel', 'restaurant', 'museum', 'park', 'attraction'];

        return [
            'name' => fake()->company() . ' ' . $location[0],
            'description' => fake()->paragraph(3),
            'address' => fake()->streetAddress() . ', ' . $location[0] . ', España',
            'latitude' => $location[1] + fake()->randomFloat(5, -0.02, 0.02),
            'longitude' => $location[2] + fake()->randomFloat(5, -0.02, 0.02),
            'image' => 'CorkExpEvent' . fake()->numberBetween(1, 3) . '.jpeg',
            'type' => fake()->randomElement($types),
            'is_featured' => fake()->boolean(20),
        ];
    }
}
