<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the first agency ID
        $agency = \App\Models\Agency::first();

        if ($agency) {
            // Update all experiences with null agency_id to use the first agency
            \DB::table('experiences')
                ->whereNull('agency_id')
                ->update(['agency_id' => $agency->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as we don't know which experiences
        // originally had null agency_id
    }
};
