<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('story_tours', function (Blueprint $table) {
            // Add territory field (territorio)
            if (!Schema::hasColumn('story_tours', 'territory')) {
                $table->string('territory')->nullable()->after('type');
            }
            
            // Add modality field (modalidad: Juego, Testimonio, Paisaje, Información)
            if (!Schema::hasColumn('story_tours', 'modality')) {
                $table->string('modality')->nullable()->after('territory');
            }
            
            // Add crafts flag (oficios)
            if (!Schema::hasColumn('story_tours', 'has_crafts')) {
                $table->boolean('has_crafts')->default(false)->after('modality');
            }
            
            // Add artists flag (artesanos y artistas)
            if (!Schema::hasColumn('story_tours', 'has_artists')) {
                $table->boolean('has_artists')->default(false)->after('has_crafts');
            }
            
            // Add year field for date filtering
            if (!Schema::hasColumn('story_tours', 'year')) {
                $table->integer('year')->nullable()->after('has_artists');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('story_tours', function (Blueprint $table) {
            // Drop filter fields if they exist
            $columns = [
                'territory', 'modality', 'has_crafts', 'has_artists', 'year'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('story_tours', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
