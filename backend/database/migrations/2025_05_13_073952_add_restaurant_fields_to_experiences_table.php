<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            // Add restaurant-specific fields if they don't exist
            if (!Schema::hasColumn('experiences', 'address')) {
                $table->string('address')->nullable();
            }
            if (!Schema::hasColumn('experiences', 'phone')) {
                $table->string('phone', 50)->nullable();
            }
            if (!Schema::hasColumn('experiences', 'email')) {
                $table->string('email')->nullable();
            }
            if (!Schema::hasColumn('experiences', 'website')) {
                $table->string('website')->nullable();
            }
            if (!Schema::hasColumn('experiences', 'cuisine_type')) {
                $table->string('cuisine_type', 100)->nullable();
            }
            if (!Schema::hasColumn('experiences', 'opening_hours')) {
                $table->text('opening_hours')->nullable();
            }
            if (!Schema::hasColumn('experiences', 'menu_url')) {
                $table->string('menu_url')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            // Drop restaurant-specific fields if they exist
            $columns = [
                'address', 'phone', 'email', 'website',
                'cuisine_type', 'opening_hours', 'menu_url'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('experiences', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
