<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('text'); // text, textarea, image, video, etc.
            $table->string('group')->default('general'); // For grouping settings: general, welcome, etc.
            $table->string('label')->nullable(); // Human-readable label
            $table->text('description')->nullable(); // Description of the setting
            $table->boolean('is_public')->default(true); // Whether this setting is accessible via public API
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_settings');
    }
};
