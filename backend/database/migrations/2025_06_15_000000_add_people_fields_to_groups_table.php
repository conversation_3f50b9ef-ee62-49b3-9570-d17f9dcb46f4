<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('groups', function (Blueprint $table) {
            // Add number_of_people field
            $table->integer('number_of_people')->default(1)->after('type');
            
            // Add group_members field to store names of people who don't have accounts
            $table->json('group_members')->nullable()->after('number_of_people');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('groups', function (Blueprint $table) {
            $table->dropColumn(['number_of_people', 'group_members']);
        });
    }
};
