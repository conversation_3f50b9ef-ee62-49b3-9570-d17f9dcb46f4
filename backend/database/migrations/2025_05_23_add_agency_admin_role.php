<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the first user of each agency to be an agency_admin
        $agencies = DB::table('agencies')->get();
        
        foreach ($agencies as $agency) {
            // Find the first user of this agency
            $firstUser = DB::table('users')
                ->where('agency_id', $agency->id)
                ->orderBy('created_at', 'asc')
                ->first();
            
            if ($firstUser) {
                DB::table('users')
                    ->where('id', $firstUser->id)
                    ->update(['role' => 'agency_admin']);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert agency_admin roles back to default
        DB::table('users')
            ->where('role', 'agency_admin')
            ->update(['role' => 'user']);
    }
};
