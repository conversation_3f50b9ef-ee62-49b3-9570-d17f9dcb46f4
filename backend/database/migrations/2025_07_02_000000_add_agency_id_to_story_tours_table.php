<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('story_tours', function (Blueprint $table) {
            // Add agency_id field
            if (!Schema::hasColumn('story_tours', 'agency_id')) {
                $table->foreignId('agency_id')->nullable()->after('location_id')->constrained()->nullOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('story_tours', function (Blueprint $table) {
            if (Schema::hasColumn('story_tours', 'agency_id')) {
                $table->dropForeign(['agency_id']);
                $table->dropColumn('agency_id');
            }
        });
    }
};
