<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            // Add time slot and capacity fields
            if (!Schema::hasColumn('experiences', 'available_time_slots')) {
                $table->json('available_time_slots')->nullable();
            }
            if (!Schema::hasColumn('experiences', 'capacity_per_time_slot')) {
                $table->integer('capacity_per_time_slot')->nullable();
            }
            if (!Schema::hasColumn('experiences', 'max_reservations_per_day')) {
                $table->integer('max_reservations_per_day')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            // Drop time slot and capacity fields if they exist
            $columns = [
                'available_time_slots', 'capacity_per_time_slot', 'max_reservations_per_day'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('experiences', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
