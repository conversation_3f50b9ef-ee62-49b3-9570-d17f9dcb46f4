-- Create guide user for Cork Experience
-- This script creates a guide user with email: <EMAIL> and password: password

-- First, check if user already exists and delete if necessary
DELETE FROM users WHERE email = '<EMAIL>';

-- Insert the guide user
-- Password hash for 'password' using <PERSON><PERSON>'s default bcrypt
INSERT INTO users (name, email, email_verified_at, password, role, agency_id, created_at, updated_at) 
VALUES (
    'Guide User',
    '<EMAIL>',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- bcrypt hash for 'password'
    'guide',
    1, -- Assuming agency with ID 1 exists
    NOW(),
    NOW()
);

-- Verify the user was created
SELECT id, name, email, role, agency_id, created_at FROM users WHERE email = '<EMAIL>';
