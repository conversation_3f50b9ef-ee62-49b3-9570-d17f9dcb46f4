<?php

use App\Http\Controllers\Api\AgencyController;
use App\Http\Controllers\Api\AppSettingsController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\ExperienceController;
use App\Http\Controllers\Api\LocationController;
use App\Http\Controllers\Api\NewsController;
use App\Http\Controllers\Api\ReservationController;
use App\Http\Controllers\Api\RouteController;
use App\Http\Controllers\Api\StoryTourController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\BroadcastController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Auth Routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected Routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user/groups', [UserController::class, 'groups']);
    Route::get('/user/profile', [UserController::class, 'profile']);
    Route::get('/user/reservations', [ReservationController::class, 'getUserReservations']);

    // Notifications API Routes
    Route::get('/notifications', [\App\Http\Controllers\Api\NotificationsController::class, 'index']);
    Route::post('/notifications/{id}/mark-as-read', [\App\Http\Controllers\Api\NotificationsController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-as-read', [\App\Http\Controllers\Api\NotificationsController::class, 'markAllAsRead']);
    Route::delete('/notifications/{id}', [\App\Http\Controllers\Api\NotificationsController::class, 'destroy']);
    Route::delete('/notifications', [\App\Http\Controllers\Api\NotificationsController::class, 'destroyAll']);

    // Broadcasting Authentication
    Route::post('/broadcasting/auth', [BroadcastController::class, 'authenticate']);
});

// News API Routes
Route::apiResource('news', NewsController::class);

// Events API Routes
Route::apiResource('events', EventController::class);

// Locations API Routes
Route::apiResource('locations', LocationController::class);

// Agencies API Routes
Route::apiResource('agencies', AgencyController::class);

// Experiences API Routes
Route::apiResource('experiences', ExperienceController::class);

// StoryTours API Routes
Route::apiResource('story-tours', StoryTourController::class);
Route::get('story-tours/type/{type}', [StoryTourController::class, 'getByType']);
Route::get('story-tours-filter-options', [StoryTourController::class, 'getFilterOptions']);

// Routes API Routes
Route::apiResource('routes', RouteController::class);

// App Settings API Routes
Route::get('settings', [AppSettingsController::class, 'index']);
Route::get('settings/group/{group}', [AppSettingsController::class, 'getByGroup']);
Route::get('settings/welcome', [AppSettingsController::class, 'getWelcomeSettings']);

// Reservation API Routes
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('reservations', ReservationController::class);
    Route::post('reservations/{id}/cancel', [ReservationController::class, 'cancel']);
    Route::get('available-time-slots', [ReservationController::class, 'getAvailableTimeSlots']);
    Route::get('booking-code/{code}', [ReservationController::class, 'getByBookingCode']);
});

// Guide Management Routes
Route::middleware('auth:sanctum')->prefix('guide')->group(function () {
    Route::get('/dashboard-stats', [\App\Http\Controllers\GuideController::class, 'getDashboardStats']);
    Route::get('/profile', [\App\Http\Controllers\GuideController::class, 'getGuideProfile']);
    Route::get('/experiences', [\App\Http\Controllers\GuideController::class, 'getMyExperiences']);
    Route::put('/experiences/{id}', [\App\Http\Controllers\GuideController::class, 'updateExperience']);
    Route::get('/groups', [\App\Http\Controllers\GuideController::class, 'getMyGroups']);
    Route::get('/restaurants', [\App\Http\Controllers\GuideController::class, 'getAvailableRestaurants']);

    // Additional endpoints that will be implemented as needed
    Route::post('/groups', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
    Route::put('/groups/{id}', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
    Route::delete('/groups/{id}', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
    Route::post('/experiences/{id}/restaurant', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
    Route::put('/experiences/{id}/time-slots', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
    Route::post('/groups/{id}/members', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
    Route::delete('/groups/{id}/members/{memberId}', function() { return response()->json(['message' => 'Endpoint en desarrollo']); });
});

// User Role Management Routes
Route::middleware('auth:sanctum')->prefix('admin')->group(function () {
    Route::get('/users', [\App\Http\Controllers\UserRoleController::class, 'getManageableUsers']);
    Route::post('/users/{id}/roles', [\App\Http\Controllers\UserRoleController::class, 'assignRole']);
    Route::delete('/users/{id}/roles', [\App\Http\Controllers\UserRoleController::class, 'removeRole']);
    Route::post('/users/{id}/toggle-guide', [\App\Http\Controllers\UserRoleController::class, 'toggleGuideRole']);
    Route::get('/available-roles', [\App\Http\Controllers\UserRoleController::class, 'getAvailableRoles']);
});
