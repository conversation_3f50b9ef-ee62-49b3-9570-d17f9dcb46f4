<?php

use App\Http\Controllers\AgencyProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::redirect('/', '/dashboard')->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Agency Profile routes - accessible to any authenticated user with an agency_id
Route::middleware(['auth', 'verified'])->prefix('dashboard')->group(function () {
    Route::get('agency-profile', [AgencyProfileController::class, 'edit'])->name('agency-profile.edit');
    Route::put('agency-profile', [AgencyProfileController::class, 'update'])->name('agency-profile.update');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/admin.php';
require __DIR__.'/agency.php';

// Agency User Management Routes
Route::middleware(['auth', 'verified'])->prefix('dashboard')->name('admin.')->group(function () {
    Route::resource('agency-user-management', \App\Http\Controllers\Admin\AgencyUserManagementController::class);
});
