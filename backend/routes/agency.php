<?php

use App\Http\Controllers\Admin\AgencyController;
use App\Http\Controllers\Admin\AgencyUserController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\ExperienceController;
use App\Http\Controllers\Admin\RouteController;
use App\Http\Controllers\Admin\DocumentationController;
use Illuminate\Support\Facades\Route;

// Agency routes - only accessible to users associated with an agency
Route::middleware(['auth', 'verified', 'agency'])->prefix('dashboard')->name('admin.')->group(function () {
    // Agency can only manage their own experiences
    Route::get('experiences', [ExperienceController::class, 'index'])->name('experiences.index');
    Route::get('experiences/create', [ExperienceController::class, 'create'])->name('experiences.create');
    Route::post('experiences', [ExperienceController::class, 'store'])->name('experiences.store');
    Route::get('experiences/{experience}', [ExperienceController::class, 'show'])->name('experiences.show');
    Route::get('experiences/{experience}/edit', [ExperienceController::class, 'edit'])->name('experiences.edit');
    Route::put('experiences/{experience}', [ExperienceController::class, 'update'])->name('experiences.update');
    Route::delete('experiences/{experience}', [ExperienceController::class, 'destroy'])->name('experiences.destroy');

    // Events are now managed through the news-events page
    // Agency users can access the news-events page to manage their events

    // Agency can only manage their own routes
    Route::get('routes', [RouteController::class, 'index'])->name('routes.index');
    Route::get('routes/create', [RouteController::class, 'create'])->name('routes.create');
    Route::post('routes', [RouteController::class, 'store'])->name('routes.store');
    Route::get('routes/{route}', [RouteController::class, 'show'])->name('routes.show');
    Route::get('routes/{route}/edit', [RouteController::class, 'edit'])->name('routes.edit');
    Route::put('routes/{route}', [RouteController::class, 'update'])->name('routes.update');
    Route::delete('routes/{route}', [RouteController::class, 'destroy'])->name('routes.destroy');

    // Documentation
    Route::get('documentation', [DocumentationController::class, 'index'])->name('documentation.index');

    // Agency profile - allow agency users to view their own agency
    Route::get('agencies/{agency}', [AgencyController::class, 'show'])->name('agencies.show');

    // My Agency - dedicated routes for agency users to manage their own agency
    Route::get('my-agency', [AgencyController::class, 'editMyAgency'])->name('my-agency.edit');
    Route::put('my-agency', [AgencyController::class, 'updateMyAgency'])->name('my-agency.update');

    // Agency Users Management - Only accessible to agency admins
    Route::middleware(['agency.admin'])->group(function () {
        Route::resource('agency-users', AgencyUserController::class);
    });

    // Test routes
    Route::get('test-agency', function() {
        return 'Agency test route works!';
    })->name('test-agency');

    Route::get('test-agency-users', function() {
        return 'Agency users test route works! Your role is: ' . auth()->user()->role;
    })->name('test-agency-users');
});
