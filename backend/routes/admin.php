<?php

use App\Http\Controllers\Admin\AgencyController;
use App\Http\Controllers\Admin\AgencyUserController;
use App\Http\Controllers\Admin\AppSettingsController;
use App\Http\Controllers\Admin\BookingCodeController;
use App\Http\Controllers\Admin\DocumentationController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\ExperienceController;
use App\Http\Controllers\Admin\GroupController;
use App\Http\Controllers\Admin\LocationController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\NewsEventsController;
use App\Http\Controllers\Admin\NotificationsController;
use App\Http\Controllers\Admin\ReservationController;
use App\Http\Controllers\Admin\RouteController;
use App\Http\Controllers\Admin\StoryTourController;
use Illuminate\Support\Facades\Route;

// Import SuperAdminMiddleware
use App\Http\Middleware\SuperAdminMiddleware;

Route::middleware(['auth', 'verified'])->prefix('dashboard')->name('admin.')->group(function () {
    // News and Events combined view - Accessible to both superadmins and agency users
    Route::get('news-events', [NewsEventsController::class, 'index'])->name('news-events.index');
    Route::get('news-events/news/{news}', [NewsEventsController::class, 'showNews'])->name('news-events.show-news');
    Route::get('news-events/events/{event}', [NewsEventsController::class, 'showEvent'])->name('news-events.show-event');

    // Events - Accessible to both superadmins and agency users with proper access control
    Route::resource('events', EventController::class);

    // News - Only superadmins can manage news
    Route::middleware('superadmin')->group(function () {
        Route::resource('news', NewsController::class);
    });

    // Locations - Only superadmins can manage locations
    Route::middleware('superadmin')->group(function () {
        Route::resource('locations', LocationController::class);
    });

    // Agencies - Only superadmins can create/edit/delete agencies
    Route::middleware('superadmin')->group(function () {
        Route::resource('agencies', AgencyController::class)->except(['show']);
    });

    // Agency details can be viewed by anyone
    Route::get('agencies/{agency}', [AgencyController::class, 'show'])->name('agencies.show');

    // Agency users can only be managed by superadmins
    Route::middleware('superadmin')->group(function () {
        Route::get('agencies/{agency}/manage-users', [AgencyController::class, 'manageUsers'])->name('agencies.manage-users');
        Route::post('agencies/{agency}/add-users', [AgencyController::class, 'addUsers'])->name('agencies.add-users');
        Route::delete('agencies/{agency}/users/{user}', [AgencyController::class, 'removeUser'])->name('agencies.remove-user');
    });

    // Experiences are handled in agency.php to ensure proper access control

    // StoryTours - Accessible to both superadmins and agency users with proper access control
    Route::resource('story-tours', StoryTourController::class);

    // Routes are handled in agency.php to ensure proper access control

    // Documentation
    Route::get('documentation', [DocumentationController::class, 'index'])->name('documentation.index');

    // App Settings - Only superadmins can manage app settings
    Route::middleware('superadmin')->group(function () {
        Route::resource('app-settings', AppSettingsController::class);

        // Special route for welcome screen settings
        Route::get('welcome-settings', function () {
            // Get welcome settings
            $welcomeSettings = \App\Models\AppSetting::where('group', 'welcome')->get();

            // Transform to key-value pairs
            $settings = [];
            foreach ($welcomeSettings as $setting) {
                $settings[$setting->key] = $setting->value;
            }

            return \Inertia\Inertia::render('Admin/AppSettings/WelcomeSettings', [
                'settings' => $settings,
            ]);
        })->name('welcome-settings');

        // Update welcome screen settings
        Route::post('welcome-settings', [AppSettingsController::class, 'updateWelcomeScreen'])->name('welcome-settings.update');
    });

    // Groups - Accessible to both superadmins and agency users with proper access control
    Route::resource('groups', GroupController::class);
    Route::get('groups/{group}/manage-users', [GroupController::class, 'manageUsers'])->name('groups.manage-users');
    Route::post('groups/{group}/update-users', [GroupController::class, 'updateUsers'])->name('groups.update-users');

    // Booking Codes - Accessible to both superadmins and agency users with proper access control
    Route::post('booking-codes/generate/{reservation}', [BookingCodeController::class, 'generate'])->name('booking-codes.generate');
    Route::post('booking-codes/{bookingCode}/deactivate', [BookingCodeController::class, 'deactivate'])->name('booking-codes.deactivate');
    Route::post('booking-codes/{bookingCode}/activate', [BookingCodeController::class, 'activate'])->name('booking-codes.activate');
    Route::post('booking-codes/{bookingCode}/update-expiration', [BookingCodeController::class, 'updateExpiration'])->name('booking-codes.update-expiration');

    // Reservations - Accessible to both superadmins and agency users with proper access control
    Route::resource('reservations', ReservationController::class);
    Route::post('reservations/{id}/update-status', [ReservationController::class, 'updateStatus'])->name('reservations.update-status');

    // Notifications - Accessible to both superadmins and agency users
    Route::get('notifications', [NotificationsController::class, 'index'])->name('notifications.index');
    Route::post('notifications/{id}/mark-as-read', [NotificationsController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('notifications/mark-all-as-read', [NotificationsController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('notifications/{id}', [NotificationsController::class, 'destroy'])->name('notifications.destroy');
    Route::delete('notifications', [NotificationsController::class, 'destroyAll'])->name('notifications.destroy-all');


});
