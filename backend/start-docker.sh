#!/bin/bash

# Copy .env.example to .env if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
fi

# Copy .env.docker to .env.docker.local if it doesn't exist
if [ ! -f .env.docker.local ]; then
    cp .env.docker .env.docker.local
fi

# Start Docker containers
docker-compose --env-file .env.docker.local up -d

# Install Composer dependencies
docker-compose exec app composer install

# Generate application key
docker-compose exec app php artisan key:generate

# Run migrations
docker-compose exec app php artisan migrate

# Run seeders
docker-compose exec app php artisan db:seed

echo "Laravel application is running at http://localhost:8000"
