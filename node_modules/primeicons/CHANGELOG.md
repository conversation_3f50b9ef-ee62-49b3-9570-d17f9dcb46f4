# Changelog

## [7.0.0](https://github.com/primefaces/primeicons/tree/7.0.0) (2024-03-29)

[Full Changelog](https://github.com/primefaces/primeicons/compare/6.0.1...7.0.0)

**Implemented New Features and Enhancements:**

- Add tags for better search results to all icons [\#1131](https://github.com/primefaces/primeicons/issues/1131)
- Add "arrow-down-left-and-arrow-up-right-to-center" icon [\#1129](https://github.com/primefaces/primeicons/issues/1129)
- Add "microchip" icon [\#1128](https://github.com/primefaces/primeicons/issues/1128)
- Add "objects-column" icon [\#1125](https://github.com/primefaces/primeicons/issues/1125)
- Add "pause-circle" icon [\#1082](https://github.com/primefaces/primeicons/issues/1082)
- Add "folder-plus" icon [\#1013](https://github.com/primefaces/primeicons/issues/1013)
- Add "receipt" icon [\#1003](https://github.com/primefaces/primeicons/issues/1003)
- Add "microchip-ai" icon [\#982](https://github.com/primefaces/primeicons/issues/982)
- Add "crown" icon [\#981](https://github.com/primefaces/primeicons/issues/981)
- Add "list-check" icon [\#964](https://github.com/primefaces/primeicons/issues/964)
- Add "shop" icon [\#959](https://github.com/primefaces/primeicons/issues/959)
- Add "tiktok" icon [\#953](https://github.com/primefaces/primeicons/issues/953)
- Add "star-half" and "star-half-fill" icons [\#949](https://github.com/primefaces/primeicons/issues/949)
- Add "sort-up-fill" and "sort-down-fill" icons [\#947](https://github.com/primefaces/primeicons/issues/947)
- Add "cart-minus" icon [\#919](https://github.com/primefaces/primeicons/issues/919)
- Add "expand" icon [\#896](https://github.com/primefaces/primeicons/issues/896)
- Add "twitch" icon [\#884](https://github.com/primefaces/primeicons/issues/884)
- Add "address-book" icon [\#862](https://github.com/primefaces/primeicons/issues/862)
- Add "sparkles" icon [\#854](https://github.com/primefaces/primeicons/issues/854)
- Add "warehouse" icon [\#834](https://github.com/primefaces/primeicons/issues/834)
- Add "arrow-up-right-and-arrow-down-left-from-center" icon [\#825](https://github.com/primefaces/primeicons/issues/825)
- Add "turkish-lira" icon [\#749](https://github.com/primefaces/primeicons/issues/749)
- Add "chart-scatter" icon [\#741](https://github.com/primefaces/primeicons/issues/741)
- Add "wave-pulse" icon [\#738](https://github.com/primefaces/primeicons/issues/738)
- Add "graduation-cap" icon [\#703](https://github.com/primefaces/primeicons/issues/703)
- Add "building-columns" icon [\#668](https://github.com/primefaces/primeicons/issues/668)
- Add "calendar-clock" icon [\#646](https://github.com/primefaces/primeicons/issues/646)
- Add "bell-slash" icon [\#625](https://github.com/primefaces/primeicons/issues/625)
- Add "equals" icon [\#623](https://github.com/primefaces/primeicons/issues/623)
- Add "hammer" icon [\#609](https://github.com/primefaces/primeicons/issues/609)
- Add "play-circle" icon [\#608](https://github.com/primefaces/primeicons/issues/608)
- Add "barcode" icon [\#587](https://github.com/primefaces/primeicons/issues/587)
- Add "gauge" icon [\#585](https://github.com/primefaces/primeicons/issues/585)
- Add "pinterest" icon [\#583](https://github.com/primefaces/primeicons/issues/583)
- Add "file-arrow-up" icon [\#571](https://github.com/primefaces/primeicons/issues/571)
- Add "indian-rupee" icon [\#550](https://github.com/primefaces/primeicons/issues/550)
- Add "file-plus" icon [\#524](https://github.com/primefaces/primeicons/issues/524)
- Add "file-check" icon [\#413](https://github.com/primefaces/primeicons/issues/413)
- Add "face-smile" icon [\#383](https://github.com/primefaces/primeicons/issues/383)
- Add "thumbtack" icon [\#333](https://github.com/primefaces/primeicons/issues/333)
- Add "cart-arrow-down" icon [\#268](https://github.com/primefaces/primeicons/issues/268)
- Add "asterisks" icon [\#263](https://github.com/primefaces/primeicons/issues/263)
- Add "trophy" icon [\#257](https://github.com/primefaces/primeicons/issues/257)
- Add "pen-to-square" icon [\#255](https://github.com/primefaces/primeicons/issues/255)
- Add "venus" and "mars" icons [\#206](https://github.com/primefaces/primeicons/issues/206)
- Add "clipboard" icon [\#178](https://github.com/primefaces/primeicons/issues/178)
- Add "headphones" icon [\#91](https://github.com/primefaces/primeicons/issues/91)
- Add "lightbulb" icon [\#90](https://github.com/primefaces/primeicons/issues/90)

**Fixed bugs:**

- Fix sort-* icons' alignments [\#1064](https://github.com/primefaces/primeicons/issues/1064)
- Update "twitter" icon [\#1006](https://github.com/primefaces/primeicons/issues/1006)
- Animation: Respect CSS prefers-reduced-motion [\#1065](https://github.com/primefaces/primeicons/issues/1065)

## [6.0.1](https://github.com/primefaces/primeicons/tree/6.0.1) (2022-09-20)

[Full Changelog](https://github.com/primefaces/primeicons/compare/6.0.0...6.0.1)

**Fixed bugs:**

- Fix "cart-plus" and "gift" icons  [\#804](https://github.com/primefaces/primeicons/issues/804)

## [6.0.0](https://github.com/primefaces/primeicons/tree/6.0.0) (2022-09-19)

[Full Changelog](https://github.com/primefaces/primeicons/compare/5.0.0...6.0.0)

**Implemented New Features and Enhancements:**

- Add "delete-left" icon [\#760](https://github.com/primefaces/primeicons/issues/760)
- Add "stopwatch" icon [\#757](https://github.com/primefaces/primeicons/issues/757)
- Add "language" icon [\#704](https://github.com/primefaces/primeicons/issues/704)
- Add "thumbs-down-fill" icon [\#686](https://github.com/primefaces/primeicons/issues/686)
- Add "thumbs-up-fill" icon [\#637](https://github.com/primefaces/primeicons/issues/637)
- Add "gift" icon [\#631](https://github.com/primefaces/primeicons/issues/631)
- Add "arrow-right-arrow-left" icon [\#575](https://github.com/primefaces/primeicons/issues/575)
- Add "file-edit" icon [\#570](https://github.com/primefaces/primeicons/issues/570)
- Add "file-word" icon [\#567](https://github.com/primefaces/primeicons/issues/567)
- Add "file-import" icon [\#545](https://github.com/primefaces/primeicons/issues/545)
- Add "verified" icon [\#496](https://github.com/primefaces/primeicons/issues/496)
- Add "wrench" icon [\#494](https://github.com/primefaces/primeicons/issues/494)
- Add "hourglass" icon [\#491](https://github.com/primefaces/primeicons/issues/491)
- Add "megaphone" icon [\#400](https://github.com/primefaces/primeicons/issues/400)
- Add "file-export" icon  [\#369](https://github.com/primefaces/primeicons/issues/369)
- Add "microphone" icon [\#360](https://github.com/primefaces/primeicons/issues/360)
- Add "cart-plus" icon [\#326](https://github.com/primefaces/primeicons/issues/326)
- Add "bitcoin" icon [\#321](https://github.com/primefaces/primeicons/issues/321)
- Add "eraser" icon [\#287](https://github.com/primefaces/primeicons/issues/287)
- Add "arrows-alt" icon  [\#272](https://github.com/primefaces/primeicons/issues/272)
- Add "truck" icon [\#265](https://github.com/primefaces/primeicons/issues/265)
- Add "calculator" icon [\#193](https://github.com/primefaces/primeicons/issues/193)
- Add woff2 format support [\#301](https://github.com/primefaces/primeicons/issues/301)
- Update readme.md with new icons [\#529](https://github.com/primefaces/primeicons/issues/529)

**Fixed bugs:**

- Update "Discord" icon [\#688](https://github.com/primefaces/primeicons/issues/688)
- Fix "exclamation-triangle" icon [\#795](https://github.com/primefaces/primeicons/issues/795)
- Fix "sort-amount-up-alt" icon [\#794](https://github.com/primefaces/primeicons/issues/794)
- Fix "sort-amount-down-alt" icon [\#793](https://github.com/primefaces/primeicons/issues/793)
- Fix "sort-amount-down" icon [\#792](https://github.com/primefaces/primeicons/issues/792)
- Fix "sort-amount-up" icon [\#791](https://github.com/primefaces/primeicons/issues/791)
- Fix "sort-alt" icon [\#790](https://github.com/primefaces/primeicons/issues/790)
- Fix "sort-alpha-up" icon [\#789](https://github.com/primefaces/primeicons/issues/789)
- Fix "sort-alpha-down" icon [\#788](https://github.com/primefaces/primeicons/issues/788)
- Fix "sort-numeric-up" icon [\#787](https://github.com/primefaces/primeicons/issues/787)
- Fix "sort-alpha-alt-up" icon [\#786](https://github.com/primefaces/primeicons/issues/786)
- Fix "sort-alpha-alt-down" icon [\#785](https://github.com/primefaces/primeicons/issues/785)
- Fix "sort-alt-slash" icon [\#784](https://github.com/primefaces/primeicons/issues/784)
- Fix "shopping-cart" icon [\#783](https://github.com/primefaces/primeicons/issues/783)
- Fix "thumbs-down" icon [\#782](https://github.com/primefaces/primeicons/issues/782)
- Fix "thumbs-up" icon [\#781](https://github.com/primefaces/primeicons/issues/781)
- Fix "step-backward-alt" icon [\#780](https://github.com/primefaces/primeicons/issues/780)
- Fix "step-forward-alt" icon [\#779](https://github.com/primefaces/primeicons/issues/779)
- Fix "forward" icon [\#778](https://github.com/primefaces/primeicons/issues/778)
- Fix "backward" icon [\#777](https://github.com/primefaces/primeicons/issues/777)
- Fix "fast-backward" icon [\#776](https://github.com/primefaces/primeicons/issues/776)
- Fix "fast-forward" icon [\#775](https://github.com/primefaces/primeicons/issues/775)
- Fix "tags" icon [\#774](https://github.com/primefaces/primeicons/issues/774)
- Fix "tag" icon [\#773](https://github.com/primefaces/primeicons/issues/773)
- Fix "tablet" icon [\#770](https://github.com/primefaces/primeicons/issues/770)
- Fix "mobile" icon [\#769](https://github.com/primefaces/primeicons/issues/769)
- Fix "question-circle" icon [\#768](https://github.com/primefaces/primeicons/issues/768)
- Fix "palette" icon [\#767](https://github.com/primefaces/primeicons/issues/767)
- Fix "calendar" icon [\#766](https://github.com/primefaces/primeicons/issues/766)

## [5.0.0](https://github.com/primefaces/primeicons/tree/5.0.0) (2021-11-04)

[Full Changelog](https://github.com/primefaces/primeicons/compare/4.1.0...5.0.0)

**Breaking changes:**

- Rename circle and star icons, remove "-o" postfix, add new "-fill" postfix icons [\#514](https://github.com/primefaces/primeicons/issues/514)

**Implemented New Features and Enhancements:**

- Add "arrows-v" icon [\#516](https://github.com/primefaces/primeicons/issues/516)
- Add "Prime" icon [\#515](https://github.com/primefaces/primeicons/issues/515)
- Add "bookmark-fill" icon [\#511](https://github.com/primefaces/primeicons/issues/511)
- Add "heart-fill" icon [\#510](https://github.com/primefaces/primeicons/issues/510)
- Add "flag-fill" icon [\#509](https://github.com/primefaces/primeicons/issues/509)
- Add "arrow-down-right" icon [\#505](https://github.com/primefaces/primeicons/issues/505)
- Add "arrow-down-left" icon [\#504](https://github.com/primefaces/primeicons/issues/504)
- Add "arrow-up-right" icon [\#503](https://github.com/primefaces/primeicons/issues/503)
- Add "arrow-up-left" icon [\#502](https://github.com/primefaces/primeicons/issues/502)
- Add "stop" icon [\#500](https://github.com/primefaces/primeicons/issues/500)
- Add "server" icon [\#485](https://github.com/primefaces/primeicons/issues/485)
- Add "shopping bag" icon [\#470](https://github.com/primefaces/primeicons/issues/470)
- Add "bolt" icon [\#468](https://github.com/primefaces/primeicons/issues/468)
- Add "sync" icon [\#462](https://github.com/primefaces/primeicons/issues/462)
- Add "Instagram" icon [\#455](https://github.com/primefaces/primeicons/issues/455)
- Add "pi-sort-alt-slash" icon [\#452](https://github.com/primefaces/primeicons/issues/452)
- Add "chart-pie" icon [\#414](https://github.com/primefaces/primeicons/issues/414)
- Add "at" icon [\#404](https://github.com/primefaces/primeicons/issues/404)
- Add "database" icon [\#392](https://github.com/primefaces/primeicons/issues/392)
- Add "car" icon [\#388](https://github.com/primefaces/primeicons/issues/388)
- Add "qrcode" icon [\#387](https://github.com/primefaces/primeicons/issues/387)
- Add "code" icon [\#381](https://github.com/primefaces/primeicons/issues/381)
- Add "Telegram" icon [\#368](https://github.com/primefaces/primeicons/issues/368)
- Add "Reddit" icon [\#312](https://github.com/primefaces/primeicons/issues/312)
- Add "Whatsapp" icon [\#311](https://github.com/primefaces/primeicons/issues/311)
- Add "arrows-h" icon [\#309](https://github.com/primefaces/primeicons/issues/309)
- Add "Linkedin" icon [\#308](https://github.com/primefaces/primeicons/issues/308)
- Add "box" icon [\#264](https://github.com/primefaces/primeicons/issues/264)
- Add "hashtag" icon [\#259](https://github.com/primefaces/primeicons/issues/259)
- Add "history" icon [\#247](https://github.com/primefaces/primeicons/issues/247)
- Add "building" icon [\#236](https://github.com/primefaces/primeicons/issues/236)
- Add "pound" icon [\#226](https://github.com/primefaces/primeicons/issues/226)
- Add "stop-circle" icon [\#214](https://github.com/primefaces/primeicons/issues/214)
- Google LightHouse: font-display:block [\#361](https://github.com/primefaces/primeicons/issues/361)
- Add missing import css from install instructions [\#464](https://github.com/primefaces/primeicons/issues/464)

## [4.1.0](https://github.com/primefaces/primeicons/tree/4.1.0) (2020-11-18)

[Full Changelog](https://github.com/primefaces/primeicons/compare/4.0.0...4.1.0)

**Implemented New Features and Enhancements:**

- Add inverse "pi-sort" icon \(as "pi-sort-inverse"\) [\#151](https://github.com/primefaces/primeicons/issues/151)
- Add "slack" icon [\#294](https://github.com/primefaces/primeicons/issues/294)
- Add "youtube" icon [\#288](https://github.com/primefaces/primeicons/issues/288)
- Add "vimeo" icon [\#276](https://github.com/primefaces/primeicons/issues/276)
- Add "discord" icon [\#273](https://github.com/primefaces/primeicons/issues/273)
- Add "Sun/Moon/Cloud" icons to represent "Light/Dark/Dim" modes [\#256](https://github.com/primefaces/primeicons/issues/256)
- Add "map" icon [\#253](https://github.com/primefaces/primeicons/issues/253)
- Add "paypal" icon [\#235](https://github.com/primefaces/primeicons/issues/235)
- Add "amazon" icon [\#234](https://github.com/primefaces/primeicons/issues/234)
- Add "filter-slash" icon  [\#220](https://github.com/primefaces/primeicons/issues/220)
- Add "percentage" icon [\#209](https://github.com/primefaces/primeicons/issues/209)
- Add "wallet" icon [\#192](https://github.com/primefaces/primeicons/issues/192)
- Add "chain link" icon  [\#190](https://github.com/primefaces/primeicons/issues/190)
- Add "credit card" icon [\#187](https://github.com/primefaces/primeicons/issues/187)
- Add "flag" icon [\#180](https://github.com/primefaces/primeicons/issues/180)
- Add "paper-plane" icon [\#141](https://github.com/primefaces/primeicons/issues/141)
- Add "shield" icon [\#99](https://github.com/primefaces/primeicons/issues/99)
- Add "book" icon [\#97](https://github.com/primefaces/primeicons/issues/97)
- Add "phone" icon [\#77](https://github.com/primefaces/primeicons/issues/77)

**Fixed bugs:**

- Rename exclamation-triangle .svg \(remove the space in the name\) [\#286](https://github.com/primefaces/primeicons/issues/286)

## [4.0.0](https://github.com/primefaces/primeicons/tree/4.0.0) (2020-06-15)

[Full Changelog](https://github.com/primefaces/primeicons/compare/4.0.0-rc.2...4.0.0)

## [4.0.0-rc.2](https://github.com/primefaces/primeicons/tree/4.0.0-rc.2) (2020-05-26)

[Full Changelog](https://github.com/primefaces/primeicons/compare/4.0.0-rc.1...4.0.0-rc.2)

## [4.0.0-rc.1](https://github.com/primefaces/primeicons/tree/4.0.0-rc.1) (2020-05-23)

[Full Changelog](https://github.com/primefaces/primeicons/compare/3.0.0...4.0.0-rc.1)

## [3.0.0](https://github.com/primefaces/primeicons/tree/3.0.0) (2020-05-23)

[Full Changelog](https://github.com/primefaces/primeicons/compare/3.0.0-rc.1...3.0.0)

## [3.0.0-rc.1](https://github.com/primefaces/primeicons/tree/3.0.0-rc.1) (2020-05-07)

[Full Changelog](https://github.com/primefaces/primeicons/compare/2.0.0...3.0.0-rc.1)

**Implemented New Features and Enhancements:**

- Fix issues of arrow, chevron and angle icon families [\#227](https://github.com/primefaces/primeicons/issues/227)

## [2.0.0](https://github.com/primefaces/primeicons/tree/2.0.0) (2019-07-25)

[Full Changelog](https://github.com/primefaces/primeicons/compare/1.0.0...2.0.0)

**Implemented New Features and Enhancements:**

- Controlling Font Performance with font-display [\#164](https://github.com/primefaces/primeicons/issues/164)
- Add "apple" icon [\#125](https://github.com/primefaces/primeicons/issues/125)
- Add "android" icon [\#124](https://github.com/primefaces/primeicons/issues/124)
- Add "google" icon [\#123](https://github.com/primefaces/primeicons/issues/123)
- Add "microsoft" icon [\#122](https://github.com/primefaces/primeicons/issues/122)
- Update PrimeNG documentation to show newer icons [\#185](https://github.com/primefaces/primeicons/issues/185)
- Add "compass" Icon [\#184](https://github.com/primefaces/primeicons/issues/184)
- Add "file-excel" icon [\#181](https://github.com/primefaces/primeicons/issues/181)
- Add "undo" icon [\#179](https://github.com/primefaces/primeicons/issues/179)
- Add sort type icons [\#177](https://github.com/primefaces/primeicons/issues/177)
- Add "directions" icon  [\#176](https://github.com/primefaces/primeicons/issues/176)
- Add "edit user" icon [\#175](https://github.com/primefaces/primeicons/issues/175)
- Add "exclamation circle" icon [\#174](https://github.com/primefaces/primeicons/issues/174)
- Add "sliders" icon [\#173](https://github.com/primefaces/primeicons/issues/173)
- Add play, stop, pause, rew, ffw icons [\#163](https://github.com/primefaces/primeicons/issues/163)
- Add "search-minus" and "search-plus" icons [\#162](https://github.com/primefaces/primeicons/issues/162)
- Add "file-pdf" icon [\#161](https://github.com/primefaces/primeicons/issues/161)
- Add "check-square" icon [\#160](https://github.com/primefaces/primeicons/issues/160)
- Add "ticket" icon [\#153](https://github.com/primefaces/primeicons/issues/153)
- Refresh icons on readme [\#150](https://github.com/primefaces/primeicons/issues/150)
- Add "reply" icon [\#147](https://github.com/primefaces/primeicons/issues/147)
- Add "desktop" icon [\#144](https://github.com/primefaces/primeicons/issues/144)
- Add "file-o" icon [\#140](https://github.com/primefaces/primeicons/issues/140)
- Add "chart-line" icon [\#137](https://github.com/primefaces/primeicons/issues/137)
- Add "thumbs-up & down" icons [\#116](https://github.com/primefaces/primeicons/issues/116)
- Add "id-card" icon [\#111](https://github.com/primefaces/primeicons/issues/111)
- Add "palette" icon [\#83](https://github.com/primefaces/primeicons/issues/83)

**Fixed bugs:**

- Delete file icon for p-fileupload is missing [\#121](https://github.com/primefaces/primeicons/issues/121)
- Angular CLI font path unresolved when deployed [\#169](https://github.com/primefaces/primeicons/issues/169)

## [1.0.0](https://github.com/primefaces/primeicons/tree/1.0.0) (2018-10-16)

[Full Changelog](https://github.com/primefaces/primeicons/compare/705daae329840f6e935a2dde0ea7d5b2705de5c4...1.0.0)

**Implemented New Features and Enhancements:**

- Add "smartphone" icon [\#127](https://github.com/primefaces/primeicons/issues/127)
- Add "tablet" icon [\#126](https://github.com/primefaces/primeicons/issues/126)
- Add "folder" icon [\#102](https://github.com/primefaces/primeicons/issues/102)
- Add "money" icon [\#88](https://github.com/primefaces/primeicons/issues/88)
- Add "volume-down" icon [\#74](https://github.com/primefaces/primeicons/issues/74)
- Add "volume-up" icon [\#73](https://github.com/primefaces/primeicons/issues/73)
- Add "volume-off" icon [\#72](https://github.com/primefaces/primeicons/issues/72)
- Add "video" icon [\#71](https://github.com/primefaces/primeicons/issues/71)
- Add "lock-open" icon [\#66](https://github.com/primefaces/primeicons/issues/66)
- Add "eject" icon [\#58](https://github.com/primefaces/primeicons/issues/58)
- Add "briefcase" icon [\#47](https://github.com/primefaces/primeicons/issues/47)
- Add "floppy-o" icon [\#33](https://github.com/primefaces/primeicons/issues/33)
- Add "cloud download" icon [\#25](https://github.com/primefaces/primeicons/issues/25)
- Add "cloud upload" icon [\#24](https://github.com/primefaces/primeicons/issues/24)
- Add "replay" icon [\#21](https://github.com/primefaces/primeicons/issues/21)
- Add "window-maximize" icon [\#16](https://github.com/primefaces/primeicons/issues/16)
- Add "window-minimize" icon [\#15](https://github.com/primefaces/primeicons/issues/15)
- Add "pencil" icon [\#7](https://github.com/primefaces/primeicons/issues/7)
- Add a  business icons pack [\#6](https://github.com/primefaces/primeicons/issues/6)
- Add "trash" icon [\#5](https://github.com/primefaces/primeicons/issues/5)
- Add "cog" icon [\#3](https://github.com/primefaces/primeicons/issues/3)
- Add "images" icon [\#133](https://github.com/primefaces/primeicons/issues/133)
- Add "bell" icon [\#128](https://github.com/primefaces/primeicons/issues/128)
- Add "dollar" icon [\#119](https://github.com/primefaces/primeicons/issues/119)
- Add "sitemap" icon [\#115](https://github.com/primefaces/primeicons/issues/115)
- Add "table" icon [\#114](https://github.com/primefaces/primeicons/issues/114)
- Add "share" icon [\#112](https://github.com/primefaces/primeicons/issues/112)
- Add "login" icon [\#110](https://github.com/primefaces/primeicons/issues/110)
- Add "list" icon  [\#107](https://github.com/primefaces/primeicons/issues/107)
- Add "eye" icon [\#105](https://github.com/primefaces/primeicons/issues/105)
- Add "map-marker" icon [\#103](https://github.com/primefaces/primeicons/issues/103)
- Add "unlock" icon [\#87](https://github.com/primefaces/primeicons/issues/87)
- Add "question" icon [\#85](https://github.com/primefaces/primeicons/issues/85)
- Add "key" icon [\#80](https://github.com/primefaces/primeicons/issues/80)
- Add "logout" icon [\#79](https://github.com/primefaces/primeicons/issues/79)
- Add "wifi" icon [\#75](https://github.com/primefaces/primeicons/issues/75)
- Add "tags" icon [\#70](https://github.com/primefaces/primeicons/issues/70)
- Add "tag" icon [\#69](https://github.com/primefaces/primeicons/issues/69)
- Add "shopping-cart" icon [\#68](https://github.com/primefaces/primeicons/issues/68)
- Add "power-off" icon [\#67](https://github.com/primefaces/primeicons/issues/67)
- Add "lock" icon [\#65](https://github.com/primefaces/primeicons/issues/65)
- Add "inbox" icon [\#64](https://github.com/primefaces/primeicons/issues/64)
- Add "image" icon [\#63](https://github.com/primefaces/primeicons/issues/63)
- Add "folder-open" icon [\#62](https://github.com/primefaces/primeicons/issues/62)
- Add "folder" icon [\#61](https://github.com/primefaces/primeicons/issues/61)
- Add "ellipsis-v" icon [\#60](https://github.com/primefaces/primeicons/issues/60)
- Add "ellipsis-h" icon [\#59](https://github.com/primefaces/primeicons/issues/59)
- Add "copy "icon [\#57](https://github.com/primefaces/primeicons/issues/57)
- Add "comments" icon [\#56](https://github.com/primefaces/primeicons/issues/56)
- Add "comment" icon [\#55](https://github.com/primefaces/primeicons/issues/55)
- Add "clone" icon [\#53](https://github.com/primefaces/primeicons/issues/53)
- Add "chart-bar" icon [\#52](https://github.com/primefaces/primeicons/issues/52)
- Add "calendar-times" icon [\#51](https://github.com/primefaces/primeicons/issues/51)
- Add "calendar-minus" icon [\#50](https://github.com/primefaces/primeicons/issues/50)
- Add "calendar-plus" icon [\#49](https://github.com/primefaces/primeicons/issues/49)
- Add "camera" icon [\#48](https://github.com/primefaces/primeicons/issues/48)
- Add "document" icon [\#45](https://github.com/primefaces/primeicons/issues/45)
- Add "filter" icon [\#40](https://github.com/primefaces/primeicons/issues/40)
- Add "bookmark" icon [\#39](https://github.com/primefaces/primeicons/issues/39)
- Add "align-right" icon [\#38](https://github.com/primefaces/primeicons/issues/38)
- Add "align-left" icon [\#37](https://github.com/primefaces/primeicons/issues/37)
- Add "align-center" icon [\#36](https://github.com/primefaces/primeicons/issues/36)
- Add "align-justify" icon [\#35](https://github.com/primefaces/primeicons/issues/35)
- Add "question-circle" icon [\#32](https://github.com/primefaces/primeicons/issues/32)
- Add "globe" icon [\#28](https://github.com/primefaces/primeicons/issues/28)
- Add "save" icon [\#26](https://github.com/primefaces/primeicons/issues/26)
- Add "print" icon [\#22](https://github.com/primefaces/primeicons/issues/22)
- Add "visibility off" Icon [\#20](https://github.com/primefaces/primeicons/issues/20)
- Add "envelope" icon [\#19](https://github.com/primefaces/primeicons/issues/19)
- Add "clock" icon [\#17](https://github.com/primefaces/primeicons/issues/17)
- Add "user remove" icon [\#13](https://github.com/primefaces/primeicons/issues/13)
- Add "user add" icon [\#12](https://github.com/primefaces/primeicons/issues/12)
- Add "paperclip" icon [\#9](https://github.com/primefaces/primeicons/issues/9)
- Add "users" icon [\#8](https://github.com/primefaces/primeicons/issues/8)
- Add "external-link" icon [\#4](https://github.com/primefaces/primeicons/issues/4)
- Add "user" icon [\#2](https://github.com/primefaces/primeicons/issues/2)
- Add "refresh" icon [\#1](https://github.com/primefaces/primeicons/issues/1)

**Fixed bugs:**

- Shows square instead of icons [\#46](https://github.com/primefaces/primeicons/issues/46)
- pi-minus and pi-minus-circle do not correspont  [\#23](https://github.com/primefaces/primeicons/issues/23)
- Demo is Not working on chrome & firefox \(not tested on rest of browsers\) [\#10](https://github.com/primefaces/primeicons/issues/10)
- Fix readonly values of old icons [\#54](https://github.com/primefaces/primeicons/issues/54)
