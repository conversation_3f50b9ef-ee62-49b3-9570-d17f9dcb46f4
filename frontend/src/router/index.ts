import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '../stores/auth'
import {
  isAuthenticated,
  canAccessMobile,
  canAccessCMS,
  isGuide,
  userAccessType,
  isInGuestMode,
  canAccessFeature
} from '../services/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'root',
      redirect: (to) => {
        // Check if user is already authenticated
        const token = localStorage.getItem('auth_token');
        if (token) {
          // If authenticated, go to home
          return '/inicio';
        } else {
          // If not authenticated, show welcome screen
          return '/welcome';
        }
      }
    },
    {
      path: '/welcome',
      name: 'welcome',
      component: () => import('../components/welcome/WelcomeScreen.vue'),
    },
    {
      path: '/inicio',
      name: 'inicio',
      component: HomeView,
    },
    {
      path: '/home',
      redirect: '/inicio',
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/registro',
      name: 'registro',
      component: () => import('../views/RegisterView.vue')
    },
    {
      path: '/agenda',
      name: 'agenda',
      component: () => import('../views/AgendaView.vue'),
    },
    {
      path: '/agenda/:id',
      name: 'event-detail',
      component: () => import('../views/EventDetailView.vue'),
    },
    {
      path: '/experiencias',
      name: 'experiencias',
      component: () => import('../views/ExperiencesView.vue'),
    },
    {
      path: '/experiencias/:id',
      name: 'experiencia-detalle',
      component: () => import('../views/ExperienceDetailView.vue'),
    },
    {
      path: '/experiences',
      redirect: '/experiencias',
    },
    {
      path: '/mapa',
      name: 'mapa',
      component: () => import('../views/MapView.vue'),
    },
    {
      path: '/rutas',
      name: 'rutas',
      component: () => import('../views/RoutesView.vue'),
    },
    {
      path: '/rutas/:id',
      name: 'ruta-detalle',
      component: () => import('../views/RouteDetailView.vue'),
    },
    {
      path: '/sugerencias',
      name: 'sugerencias',
      component: () => import('../views/SugerenciasView.vue'),
    },
    {
      path: '/perfil',
      name: 'perfil',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/profile',
      redirect: '/perfil',
    },
    {
      path: '/reservas',
      name: 'reservas',
      component: () => import('../views/UserReservationsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/reservations',
      redirect: '/reservas',
    },
    {
      path: '/faqs',
      name: 'faqs',
      component: () => import('../views/FAQsView.vue'),
    },
    {
      path: '/preferencias',
      name: 'preferencias',
      component: () => import('../views/PreferencesView.vue'),
    },
    {
      path: '/storytours',
      name: 'storytours',
      component: () => import('../views/StoryToursView.vue'),
    },
    {
      path: '/storytours/:id',
      name: 'storytour-detalle',
      component: () => import('../views/StoryTourDetailView.vue'),
    },
    {
      path: '/alojamientos',
      name: 'alojamientos',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'hotel', title: 'Alojamiento' },
    },
    {
      path: '/restaurantes',
      name: 'restaurantes',
      component: () => import('../views/ExperiencesView.vue'),
      props: { filterType: 'restaurant', title: 'Restaurantes' },
    },
    {
      path: '/operadores',
      name: 'operadores',
      component: () => import('../views/OperadoresView.vue'),
    },
    {
      path: '/agencias',
      redirect: '/operadores',
    },

    // Guide Management Routes (Spanish URLs)
    {
      path: '/guia',
      name: 'guia-panel',
      component: () => import('../views/GuideDashboardView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/experiencias',
      name: 'guia-experiencias',
      component: () => import('../views/GuideExperiencesView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/grupos',
      name: 'guia-grupos',
      component: () => import('../views/GuideGroupsView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/guia/perfil',
      name: 'guia-perfil',
      component: () => import('../views/GuideConfigView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    // English redirects for guide routes
    {
      path: '/guide',
      redirect: '/guia',
    },
    {
      path: '/guide/experiences',
      redirect: '/guia/experiencias',
    },
    {
      path: '/guide/groups',
      redirect: '/guia/grupos',
    },
    {
      path: '/guide/config',
      redirect: '/guia/perfil',
    },
    // Add grupos redirect for general groups access
    {
      path: '/grupos',
      name: 'grupos',
      component: () => import('../views/GuideGroupsView.vue'),
      meta: { requiresAuth: true, requiresGuideRole: true }
    },
    {
      path: '/groups',
      redirect: '/grupos',
    },
    {
      path: '/operadores/:id',
      name: 'operador-detalle',
      component: () => import('../views/OperadorDetailView.vue'),
    },
    {
      path: '/agencias/:id',
      redirect: to => `/operadores/${to.params.id}`,
    },
    {
      path: '/rutas',
      name: 'rutas',
      component: () => import('../views/RoutesView.vue'),
    },
    {
      path: '/rutas/:id',
      name: 'ruta-detalle',
      component: () => import('../views/RouteDetailView.vue'),
    },
  ],
})

// Navigation guard for authentication and role-based access
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  // Block CMS access for mobile-only users and guests
  if (to.path.startsWith('/dashboard') || to.path.startsWith('/admin')) {
    if (isInGuestMode()) {
      console.warn('Guest user attempted to access CMS');
      next('/inicio?error=login_required');
      return;
    }
    if (isAuthenticated.value && !canAccessCMS()) {
      console.warn('Mobile user attempted to access CMS');
      next('/inicio?error=cms_access_denied');
      return;
    }
  }

  // Define public routes accessible to guests
  const publicRoutes = [
    '/', '/inicio', '/experiencias', '/mapa', '/storytours', '/operadores',
    '/rutas', '/agenda', '/faqs', '/alojamientos', '/restaurantes', '/sugerencias',
    '/login', '/registro', '/welcome'
  ];
  const isPublicRoute = publicRoutes.includes(to.path) ||
    to.path.startsWith('/experiencias/') ||
    to.path.startsWith('/storytours/') ||
    to.path.startsWith('/operadores/') ||
    to.path.startsWith('/rutas/') ||
    to.path.startsWith('/agenda/');

  // Block mobile access for CMS-only users (except public routes)
  if (!isPublicRoute && isAuthenticated.value && !canAccessMobile()) {
    console.warn('CMS user attempted to access mobile app');
    // Only redirect if they're trying to access protected mobile features
    // Allow access to basic mobile routes like /perfil for logout functionality
    const allowedMobileRoutes = ['/perfil', '/inicio', '/experiencias', '/mapa', '/agenda'];
    if (!allowedMobileRoutes.includes(to.path)) {
      window.location.href = '/dashboard';
      return;
    }
  }

  // Handle authentication requirements
  if (to.meta.requiresAuth) {
    if (isInGuestMode()) {
      // Store intended destination for post-login redirect
      localStorage.setItem('post_login_redirect', to.fullPath);
      next('/inicio?error=login_required&message=Para acceder a esta función necesitas iniciar sesión');
      return;
    }
    if (!isAuthenticated.value) {
      next('/inicio?error=login_required');
      return;
    }
  }

  // Check guide role requirement
  if (to.meta.requiresGuideRole) {
    if (isInGuestMode()) {
      localStorage.setItem('post_login_redirect', to.fullPath);
      next('/inicio?error=login_required&message=Para acceder al panel de guía necesitas iniciar sesión');
      return;
    }
    if (!isAuthenticated.value || !isGuide()) {
      // Redirect non-guides to home with error message
      next('/inicio?error=access_denied');
      return;
    }
  }

  // Handle authenticated-only features for guests (excluding /perfil which should show login/register options)
  const authRequiredPaths = ['/reservas', '/preferencias'];
  if (authRequiredPaths.includes(to.path) && isInGuestMode()) {
    localStorage.setItem('post_login_redirect', to.fullPath);
    next('/inicio?error=login_required&message=Para acceder a esta función necesitas iniciar sesión');
    return;
  }

  next();
});

export default router
