/* Cork Experience Global Styles */

:root {
  --cork-orange: #e67e22;
  --cork-orange-dark: #d35400;
  --cork-text: #333333;
  --cork-text-light: #666666;
  --cork-background: #f9f9f9;
  --cork-border: #dddddd;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Poppins', sans-serif;
  color: var(--cork-text);
  background-color: var(--cork-background);
}

#app {
  height: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin-bottom: 1rem;
  line-height: 1.5;
}

/* Buttons */
button {
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: var(--cork-orange);
  color: white;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: var(--cork-orange-dark);
}

button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.3);
}

/* Forms */
input {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid var(--cork-border);
  border-radius: 4px;
  transition: border-color 0.3s ease;
}

input:focus {
  outline: none;
  border-color: var(--cork-orange);
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
}
