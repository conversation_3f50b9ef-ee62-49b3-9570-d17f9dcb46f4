/**
 * API service for making requests to the backend
 */

// Get the API base URL from environment variables or use a fallback
// Using relative URL to leverage Vite's proxy
export const API_BASE_URL = '/api';

// Check if we're in a production environment and the API URL is the placeholder
const isProduction = import.meta.env.PROD;
const isPlaceholderUrl = API_BASE_URL.includes('your-backend-api-url.com');

// Flag to determine if we should use mock data instead of making API calls
const useMockData = isProduction && isPlaceholderUrl;

// Log a warning if we're using mock data in production
if (useMockData) {
  console.warn(
    'Using mock data because no valid API URL is configured. ' +
    'Set the VITE_API_BASE_URL environment variable to your actual backend URL.'
  );
}

// Define response types
export interface ApiResponse<T> {
  data: T[];
  links?: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta?: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface NewsItem {
  id: number;
  title: string;
  summary: string;
  datetime: string;
  location: string;
  image: string | null;
  created_at: string;
  updated_at: string;
}

export interface EventItem {
  id: number;
  title: string;
  description: string;
  start_datetime: string;
  end_datetime: string | null;
  location: string;
  image: string | null;
  rating: number | null;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface LocationItem {
  id: number;
  name: string;
  description: string;
  address: string;
  latitude: number;
  longitude: number;
  image: string | null;
  type: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface ExperienceItem {
  id: number;
  title: string;
  description: string;
  short_description: string | null;
  location_id: number | null;
  location?: {
    id: number;
    name: string;
  };
  agency_id: number | null;
  agency?: {
    id: number;
    name: string;
  };
  type: string;
  duration: string | null;
  distance: string | null;
  difficulty: string | null;
  price: number | null;
  image: string | null;
  start_date: string | null;
  end_date: string | null;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Restaurant-specific fields
  address?: string | null;
  phone?: string | null;
  email?: string | null;
  website?: string | null;
  cuisine_type?: string | null;
  opening_hours?: string | null;
  menu_url?: string | null;
}

export interface AgencyItem {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postal_code: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface RoutePointItem {
  id: number;
  route_id: number;
  location_id: number | null;
  location?: LocationItem;
  order: number;
  description: string | null;
  image: string | null;
  created_at: string;
  updated_at: string;
}

export interface RouteItem {
  id: number;
  title: string;
  description: string;
  short_description: string | null;
  image: string | null;
  duration: string | null;
  distance: string | null;
  difficulty: string | null;
  agency_id: number | null;
  agency?: {
    id: number;
    name: string;
    logo: string | null;
  };
  points: RoutePointItem[];
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AppSettingItem {
  id: number;
  key: string;
  value: string | null;
  type: string;
  group: string;
  label: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface WelcomeSettings {
  welcome_title?: string;
  video_title?: string;
  video_description?: string;
  welcome_logo?: string;
  video_logo?: string;
  welcome_video?: string;
  video_placeholder?: string;
  [key: string]: string | undefined;
}

export interface StoryTourItem {
  id: number;
  title: string;
  description: string;
  image: string | null;
  type: string;
  territory: string | null;
  modality: string | null;
  has_crafts: boolean;
  has_artists: boolean;
  year: number | null;
  audio_file: string | null;
  video_file: string | null;
  ar_model_file: string | null;
  location_id: number | null;
  location?: {
    id: number;
    name: string;
    latitude: number;
    longitude: number;
  };
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Mock data for fallback when API is not available
const mockData = {
  storyTours: [
    {
      id: 1,
      title: 'Historia del Corcho en Extremadura',
      description: 'Un recorrido por la historia del corcho en la región de Extremadura, desde sus orígenes hasta la actualidad.',
      image: '/assets/images/storytours/paisaje.png',
      type: 'paisaje',
      territory: 'Extremadura',
      modality: 'Información',
      has_crafts: true,
      has_artists: false,
      year: 1950,
      audio_file: null,
      video_file: null,
      ar_model_file: null,
      location_id: 1,
      location: {
        id: 1,
        name: 'Mérida',
        latitude: 38.9122,
        longitude: -6.3436
      },
      is_featured: true,
      is_active: true,
      created_at: '2025-03-01T10:00:00',
      updated_at: '2025-03-01T10:00:00'
    },
    {
      id: 2,
      title: 'Artesanía del Corcho',
      description: 'Descubre las técnicas tradicionales de artesanía con corcho y conoce a los artesanos locales.',
      image: '/assets/images/storytours/patrimonio.png',
      type: 'patrimonio',
      territory: 'Andalucía',
      modality: 'Testimonio',
      has_crafts: true,
      has_artists: true,
      year: 1980,
      audio_file: null,
      video_file: null,
      ar_model_file: null,
      location_id: 2,
      location: {
        id: 2,
        name: 'Sevilla',
        latitude: 37.3891,
        longitude: -5.9845
      },
      is_featured: false,
      is_active: true,
      created_at: '2025-03-02T10:00:00',
      updated_at: '2025-03-02T10:00:00'
    }
  ],
  news: [
    {
      id: 1,
      title: 'Cork Experience Launch',
      summary: 'Cork Experience app has been launched successfully.',
      datetime: '2025-03-15T10:00:00',
      location: 'Badajoz, Spain',
      image: 'CorkExpEvent.jpeg',
      created_at: '2025-03-10T08:00:00',
      updated_at: '2025-03-10T08:00:00'
    },
    {
      id: 2,
      title: 'Cork Workshop',
      summary: 'Join us for a workshop on cork crafting and sustainability.',
      datetime: '2025-03-20T14:30:00',
      location: 'Cáceres, Spain',
      image: 'CorkExpEvent2.jpeg',
      created_at: '2025-03-12T09:00:00',
      updated_at: '2025-03-12T09:00:00'
    }
  ],
  routes: [
    {
      id: 1,
      title: 'Ruta del Corcho',
      description: 'Una ruta para descubrir el proceso de producción del corcho en Extremadura, desde la extracción en los alcornocales hasta su procesamiento en fábricas tradicionales.',
      short_description: 'Descubre el proceso del corcho',
      image: 'CorkExpEvent.jpeg',
      duration: '4 horas',
      distance: '12 km',
      difficulty: 'Moderada',
      agency_id: 1,
      agency: {
        id: 1,
        name: 'Cork Experience',
        logo: 'cork-experience-logo.png'
      },
      points: [
        {
          id: 1,
          route_id: 1,
          location_id: 1,
          location: {
            id: 1,
            name: 'Fábrica de Corcho San Vicente',
            description: 'Fábrica tradicional de procesamiento de corcho',
            address: 'Carretera de San Vicente, km 5, Badajoz',
            latitude: 38.8796,
            longitude: -6.9705,
            image: 'CorkExpEvent.jpeg',
            type: 'point',
            is_featured: true,
            created_at: '2025-03-10T08:00:00',
            updated_at: '2025-03-10T08:00:00'
          },
          order: 0,
          description: 'Inicio de la ruta en la fábrica donde podrás ver el proceso de transformación del corcho.',
          image: null,
          created_at: '2025-03-10T08:00:00',
          updated_at: '2025-03-10T08:00:00'
        },
        {
          id: 2,
          route_id: 1,
          location_id: 2,
          location: {
            id: 2,
            name: 'Alcornocal de Monfragüe',
            description: 'Extenso bosque de alcornoques en el Parque Nacional de Monfragüe',
            address: 'Parque Nacional de Monfragüe, Cáceres',
            latitude: 39.8296,
            longitude: -6.0705,
            image: 'CorkExpEvent2.jpeg',
            type: 'point',
            is_featured: true,
            created_at: '2025-03-10T08:00:00',
            updated_at: '2025-03-10T08:00:00'
          },
          order: 1,
          description: 'Visita al alcornocal para observar los árboles y el proceso de extracción del corcho.',
          image: null,
          created_at: '2025-03-10T08:00:00',
          updated_at: '2025-03-10T08:00:00'
        }
      ],
      is_featured: true,
      is_active: true,
      created_at: '2025-03-10T08:00:00',
      updated_at: '2025-03-10T08:00:00'
    },
    {
      id: 2,
      title: 'Ruta de los Alcornocales',
      description: 'Una ruta para descubrir los mejores bosques de alcornoques de Extremadura y aprender sobre el proceso de extracción del corcho.',
      short_description: 'Descubre los alcornocales extremeños',
      image: 'CorkExpEvent2.jpeg',
      duration: '6 horas',
      distance: '15 km',
      difficulty: 'Moderada',
      agency_id: 2,
      agency: {
        id: 2,
        name: 'Bergstrom Inc',
        logo: null
      },
      points: [
        {
          id: 3,
          route_id: 2,
          location_id: 2,
          location: {
            id: 2,
            name: 'Alcornocal de Monfragüe',
            description: 'Extenso bosque de alcornoques en el Parque Nacional de Monfragüe',
            address: 'Parque Nacional de Monfragüe, Cáceres',
            latitude: 39.8296,
            longitude: -6.0705,
            image: 'CorkExpEvent2.jpeg',
            type: 'point',
            is_featured: true,
            created_at: '2025-03-10T08:00:00',
            updated_at: '2025-03-10T08:00:00'
          },
          order: 0,
          description: 'Inicio de la ruta en el corazón del alcornocal.',
          image: null,
          created_at: '2025-03-10T08:00:00',
          updated_at: '2025-03-10T08:00:00'
        }
      ],
      is_featured: true,
      is_active: true,
      created_at: '2025-03-10T08:00:00',
      updated_at: '2025-03-10T08:00:00'
    }
  ],
  events: [
    {
      id: 1,
      title: 'Cork Harvesting Demo',
      description: 'Learn about traditional cork harvesting techniques.',
      start_datetime: '2025-04-10T09:00:00',
      end_datetime: '2025-04-10T12:00:00',
      location: 'Extremadura, Spain',
      image: 'CorkExpEvent.jpeg',
      rating: 4.5,
      is_featured: true,
      created_at: '2025-03-15T10:00:00',
      updated_at: '2025-03-15T10:00:00'
    },
    {
      id: 2,
      title: 'Cork Products Exhibition',
      description: 'Explore innovative products made from sustainable cork.',
      start_datetime: '2025-04-15T10:00:00',
      end_datetime: '2025-04-15T18:00:00',
      location: 'Badajoz, Spain',
      image: 'CorkExpEvent2.jpeg',
      rating: 4.8,
      is_featured: true,
      created_at: '2025-03-20T11:00:00',
      updated_at: '2025-03-20T11:00:00'
    }
  ],
  locations: [
    {
      id: 1,
      name: 'Cork Museum',
      description: 'A museum dedicated to the history and culture of cork production.',
      address: 'Calle Principal 123, Mérida, Spain',
      latitude: 38.9122,
      longitude: -6.3436,
      image: 'museum1.jpeg',
      type: 'museum',
      is_featured: true,
      created_at: '2025-03-01T10:00:00',
      updated_at: '2025-03-01T10:00:00'
    },
    {
      id: 2,
      name: 'Cork Forest Reserve',
      description: 'Protected natural area with ancient cork oak trees.',
      address: 'Parque Natural, Cáceres, Spain',
      latitude: 39.4753,
      longitude: -6.3724,
      image: 'park1.jpeg',
      type: 'park',
      is_featured: true,
      created_at: '2025-03-05T09:00:00',
      updated_at: '2025-03-05T09:00:00'
    }
  ],
  experiences: [
    {
      id: 1,
      title: 'Alcornocales con ebike',
      description: 'Recorre los alcornocales en bicicleta eléctrica y descubre la belleza natural de este ecosistema único.',
      short_description: 'El más grande alcornocal de la península ibérica y uno de los más importantes del mundo.',
      location_id: 1,
      location: {
        id: 1,
        name: 'Castellar de la Frontera'
      },
      agency_id: 1,
      agency: {
        id: 1,
        name: 'Albatros Tours'
      },
      type: 'activity',
      duration: '3 horas',
      distance: '12 km',
      difficulty: 'moderate',
      price: 25.00,
      image: 'alcornocales.jpeg',
      start_date: '2025-03-30',
      end_date: null,
      is_featured: true,
      is_active: true,
      created_at: '2025-03-01T10:00:00',
      updated_at: '2025-03-01T10:00:00'
    },
    {
      id: 2,
      title: 'Cor de suro',
      description: 'Visita esta fábrica tradicional de corcho y aprende sobre el proceso de fabricación y su historia.',
      short_description: 'Descubre el proceso de fabricación del corcho y su historia en esta fábrica tradicional.',
      location_id: 2,
      location: {
        id: 2,
        name: 'Eslida, Castellón'
      },
      agency_id: null,
      agency: null,
      type: 'museum',
      duration: '1 hora',
      distance: '5 km',
      difficulty: 'easy',
      price: 0,
      image: 'cordesuro.png',
      start_date: null,
      end_date: null,
      is_featured: false,
      is_active: true,
      created_at: '2025-03-02T10:00:00',
      updated_at: '2025-03-02T10:00:00'
    },
    {
      id: 3,
      title: 'Hotel Casa Convento',
      description: 'Alojamiento único que destaca por su arquitectura sostenible, utilizando el corcho como material principal en su construcción.',
      short_description: 'Alojamiento sostenible construido con materiales de corcho, ofreciendo una experiencia única.',
      location_id: 1,
      location: {
        id: 1,
        name: 'Castellar de la Frontera'
      },
      agency_id: null,
      agency: null,
      type: 'hotel',
      duration: null,
      distance: '8 km',
      difficulty: null,
      price: 85.00,
      image: 'hotelcasacormoran.jpeg',
      start_date: null,
      end_date: null,
      is_featured: true,
      is_active: true,
      created_at: '2025-03-03T10:00:00',
      updated_at: '2025-03-03T10:00:00'
    }
  ],
  agencies: [
    {
      id: 1,
      name: 'Albatros Tours',
      slug: 'albatros-tours',
      description: 'Agencia especializada en turismo sostenible y experiencias en la naturaleza.',
      logo: 'agencies/albatrostours.png',
      address: 'Calle Principal 123',
      city: 'Badajoz',
      state: 'Extremadura',
      country: 'España',
      postal_code: '06001',
      phone: '+34 924 123 456',
      email: '<EMAIL>',
      website: 'https://albatrostours.com',
      is_active: true,
      created_at: '2025-03-01T10:00:00',
      updated_at: '2025-03-01T10:00:00'
    },
    {
      id: 2,
      name: 'Cork Experience',
      slug: 'cork-experience',
      description: 'La agencia oficial de Cork Experience, ofreciendo las mejores experiencias relacionadas con el corcho.',
      logo: 'logos/CorkExpLogoBlack.png',
      address: 'Avenida del Corcho 45',
      city: 'Mérida',
      state: 'Extremadura',
      country: 'España',
      postal_code: '06800',
      phone: '+34 924 987 654',
      email: '<EMAIL>',
      website: 'https://corkexperience.com',
      is_active: true,
      created_at: '2025-03-02T10:00:00',
      updated_at: '2025-03-02T10:00:00'
    }
  ]
};

/**
 * Fetch data from the API
 * @param endpoint - API endpoint
 * @param options - Fetch options
 * @returns Response data
 */
export async function fetchFromApi<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
  // Get auth token from localStorage if available
  const token = localStorage.getItem('auth_token');

  // If we're using mock data, return the appropriate mock data based on the endpoint
  if (useMockData) {
    console.log(`Using mock data for endpoint: ${endpoint}`);

    // Determine which mock data to return based on the endpoint
    let mockResponse: T[] = [];

    if (endpoint.startsWith('news')) {
      mockResponse = mockData.news as unknown as T[];
    } else if (endpoint.startsWith('events')) {
      mockResponse = mockData.events as unknown as T[];
    } else if (endpoint.startsWith('locations')) {
      mockResponse = mockData.locations as unknown as T[];
    } else if (endpoint.startsWith('experiences')) {
      mockResponse = mockData.experiences as unknown as T[];
    } else if (endpoint.startsWith('agencies')) {
      mockResponse = mockData.agencies as unknown as T[];
    } else if (endpoint.startsWith('routes')) {
      mockResponse = mockData.routes as unknown as T[];
    } else if (endpoint.startsWith('story-tours')) {
      mockResponse = mockData.storyTours as unknown as T[];
    }

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      data: mockResponse
    };
  }

  // If we're not using mock data, make the actual API call
  try {
    const url = `${API_BASE_URL}/${endpoint.replace(/^\//, '')}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // Add Authorization header if token exists
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...(options.headers || {})
      },
    });

    console.log(`API request to ${url}`, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token ? { 'Authorization': 'Bearer [TOKEN]' } : {}),
        ...(options.headers ? { 'Custom headers': 'Present' } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API error for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * API service object with methods for each endpoint
 */
const apiService = {
  // News endpoints
  news: {
    getAll: () => fetchFromApi<NewsItem>('news'),
    getById: (id: number | string) => fetchFromApi<NewsItem>(`news/${id}`),
  },

  // Events endpoints
  events: {
    getAll: () => fetchFromApi<EventItem>('events'),
    getById: (id: number | string) => fetchFromApi<EventItem>(`events/${id}`),
  },

  // Locations endpoints
  locations: {
    getAll: () => fetchFromApi<LocationItem>('locations'),
    getById: (id: number | string) => fetchFromApi<LocationItem>(`locations/${id}`),
    getByType: (type: string) => fetchFromApi<LocationItem>(`locations?type=${type}`),
    getFeatured: () => fetchFromApi<LocationItem>('locations?featured=true'),
  },

  // Experiences endpoints
  experiences: {
    getAll: () => fetchFromApi<ExperienceItem>('experiences'),
    getById: (id: number | string) => fetchFromApi<ExperienceItem>(`experiences/${id}`),
    getByType: (type: string) => fetchFromApi<ExperienceItem>(`experiences?type=${type}`),
    getFeatured: () => fetchFromApi<ExperienceItem>('experiences?featured=true'),
    getActive: () => fetchFromApi<ExperienceItem>('experiences?active=true'),
    getByAgency: (agencyId: number | string) => fetchFromApi<ExperienceItem>(`experiences?agency_id=${agencyId}`),
  },

  // Accommodations endpoints (specialized experiences of type 'hotel')
  accommodations: {
    getAll: () => fetchFromApi<ExperienceItem>('experiences?type=hotel'),
    getById: (id: number | string) => fetchFromApi<ExperienceItem>(`experiences/${id}`),
    getFeatured: () => fetchFromApi<ExperienceItem>('experiences?type=hotel&featured=true'),
    getActive: () => fetchFromApi<ExperienceItem>('experiences?type=hotel&active=true'),
    getByAgency: (agencyId: number | string) => fetchFromApi<ExperienceItem>(`experiences?type=hotel&agency_id=${agencyId}`),
  },

  // Restaurants endpoints (specialized experiences of type 'restaurant')
  restaurants: {
    getAll: () => fetchFromApi<ExperienceItem>('experiences?type=restaurant'),
    getById: (id: number | string) => fetchFromApi<ExperienceItem>(`experiences/${id}`),
    getFeatured: () => fetchFromApi<ExperienceItem>('experiences?type=restaurant&featured=true'),
    getActive: () => fetchFromApi<ExperienceItem>('experiences?type=restaurant&active=true'),
    getByAgency: (agencyId: number | string) => fetchFromApi<ExperienceItem>(`experiences?type=restaurant&agency_id=${agencyId}`),
  },

  // Agencies endpoints
  agencies: {
    getAll: () => fetchFromApi<AgencyItem>('agencies'),
    getById: (id: number | string) => fetchFromApi<AgencyItem>(`agencies/${id}`),
    getActive: () => fetchFromApi<AgencyItem>('agencies?active=true'),
  },

  // App Settings endpoints
  settings: {
    getAll: () => fetchFromApi<AppSettingItem>('settings'),
    getByGroup: (group: string) => fetchFromApi<AppSettingItem>(`settings/group/${group}`),
    getWelcomeSettings: () => fetchFromApi<WelcomeSettings>('settings/welcome'),
  },

  // Routes endpoints
  routes: {
    getAll: () => fetchFromApi<RouteItem>('routes'),
    getById: (id: number | string) => fetchFromApi<RouteItem>(`routes/${id}`),
    getFeatured: () => fetchFromApi<RouteItem>('routes?featured=true'),
    getActive: () => fetchFromApi<RouteItem>('routes?active=true'),
    getByAgency: (agencyId: number | string) => fetchFromApi<RouteItem>(`routes?agency_id=${agencyId}`),
  },

  // StoryTours endpoints
  storyTours: {
    getAll: () => fetchFromApi<StoryTourItem>('story-tours'),
    getById: (id: number | string) => fetchFromApi<StoryTourItem>(`story-tours/${id}`),
    getByType: (type: string) => fetchFromApi<StoryTourItem>(`story-tours/type/${type}`),
    getFilterOptions: () => fetchFromApi<any>('story-tours-filter-options'),
    filter: (params: any) => {
      const queryParams = new URLSearchParams();

      if (params.territory && params.territory !== 'todos') {
        queryParams.append('territory', params.territory);
      }

      if (params.modality && params.modality !== 'todos') {
        queryParams.append('modality', params.modality);
      }

      if (params.type && params.type !== 'todos') {
        queryParams.append('type', params.type);
      }

      if (params.has_crafts !== undefined) {
        queryParams.append('has_crafts', params.has_crafts ? 'true' : 'false');
      }

      if (params.has_artists !== undefined) {
        queryParams.append('has_artists', params.has_artists ? 'true' : 'false');
      }

      if (params.year_min !== undefined) {
        queryParams.append('year_min', params.year_min.toString());
      }

      if (params.year_max !== undefined) {
        queryParams.append('year_max', params.year_max.toString());
      }

      const queryString = queryParams.toString();
      return fetchFromApi<StoryTourItem>(`story-tours${queryString ? '?' + queryString : ''}`);
    },
    // Helper function to fix image URLs
    fixImageUrls: (storyTours: StoryTourItem[] | any) => {
      if (!storyTours || !Array.isArray(storyTours)) {
        console.warn('fixImageUrls received invalid data:', storyTours);
        return [];
      }

      return storyTours.map(tour => {
        if (tour && tour.image && typeof tour.image === 'string' && tour.image.startsWith('http://localhost:8000/storage/')) {
          tour.image = tour.image.replace('http://localhost:8000/storage/', '/assets/');
        }
        return tour;
      });
    },
  },

  // Reservations endpoints
  reservations: {
    getAll: () => fetchFromApi<ReservationItem>('reservations'),
    getById: (id: number | string) => fetchFromApi<ReservationItem>(`reservations/${id}`),
    getUserReservations: () => fetchFromApi<ReservationItem>('user/reservations'),
    create: async (data: any) => {
      try {
        console.log('API Service: Creating reservation with data:', data);

        // Get auth token from localStorage
        const token = localStorage.getItem('auth_token');
        if (!token) {
          console.error('API Service: No authentication token found');
          throw new Error('No authentication token found');
        }

        console.log('API Service: Using token:', token.substring(0, 10) + '...');

        // Make direct fetch call for more control
        const response = await fetch(`${API_BASE_URL}/reservations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });

        console.log('API Service: Reservation response status:', response.status);

        if (!response.ok) {
          // Special handling for 409 Conflict (existing reservation)
          if (response.status === 409) {
            console.log('API Service: Detected 409 Conflict response');
            const errorData = await response.json();
            console.log('API Service: Conflict response data:', JSON.stringify(errorData, null, 2));

            // Create a custom error object with the conflict data
            const conflictError: any = new Error(errorData.message || `Error ${response.status}: ${response.statusText}`);
            conflictError.status = response.status;
            conflictError.data = errorData;

            console.log('API Service: Created conflict error object:', conflictError);
            console.log('API Service: Error status:', conflictError.status);
            console.log('API Service: Error data:', conflictError.data);

            throw conflictError;
          } else {
            // Handle other errors
            let errorMessage = `Error ${response.status}: ${response.statusText}`;
            try {
              const errorData = await response.json();
              errorMessage = errorData.message || errorMessage;
              console.error('API Service: Error response data:', errorData);
            } catch (e) {
              console.error('API Service: Could not parse error response');
            }
            throw new Error(errorMessage);
          }
        }

        const responseData = await response.json();
        console.log('API Service: Reservation created successfully:', responseData);
        return responseData;
      } catch (error) {
        console.error('API Service: Error creating reservation:', error);

        // Make sure we preserve the error properties when rethrowing
        if (error instanceof Error) {
          const customError: any = error;
          if (customError.status === 409) {
            console.log('API Service: Rethrowing conflict error with status and data');
          }
          throw customError; // Rethrow with all properties intact
        } else {
          throw error;
        }
      }
    },
    cancel: async (id: number | string) => {
      try {
        console.log('API Service: Cancelling reservation with ID:', id);

        // Get auth token from localStorage
        const token = localStorage.getItem('auth_token');
        if (!token) {
          console.error('API Service: No authentication token found for cancellation');
          throw new Error('No authentication token found');
        }

        // Make direct fetch call for more control
        const response = await fetch(`${API_BASE_URL}/reservations/${id}/cancel`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Error ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error cancelling reservation:', error);
        throw error;
      }
    },

    update: async (id: number | string, data: any) => {
      try {
        console.log('API Service: Updating reservation with ID:', id, 'data:', data);

        // Get auth token from localStorage
        const token = localStorage.getItem('auth_token');
        if (!token) {
          console.error('API Service: No authentication token found for update');
          throw new Error('No authentication token found');
        }

        // Make direct fetch call for more control
        const response = await fetch(`${API_BASE_URL}/reservations/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });

        console.log('API Service: Update response status:', response.status);

        // Handle error responses
        if (!response.ok) {
          let errorMessage = `Error ${response.status}: ${response.statusText}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
            console.error('API Service: Error response data:', errorData);
          } catch (e) {
            console.error('API Service: Could not parse error response');
          }
          throw new Error(errorMessage);
        }

        const responseData = await response.json();
        console.log('API Service: Reservation updated successfully:', responseData);
        return responseData;
      } catch (error) {
        console.error('API Service: Error updating reservation:', error);
        throw error;
      }
    },
    // The cancel method is implemented above with more detailed error handling
    getAvailableTimeSlots: async (experienceId: number | string, date: string) => {
      try {
        // Get auth token from localStorage
        const token = localStorage.getItem('auth_token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // Make direct fetch call for more control
        const response = await fetch(`${API_BASE_URL}/available-time-slots?experience_id=${experienceId}&date=${date}`, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Error ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Add availability status to each time slot
        if (data.time_slots && data.capacity_per_time_slot) {
          data.time_slots_with_availability = data.time_slots.map((timeSlot: string) => {
            return {
              time: timeSlot,
              available: true, // If it's in the list, it's available
              capacity: data.capacity_per_time_slot
            };
          });
        }

        return data;
      } catch (error) {
        console.error('Error fetching available time slots:', error);
        throw error;
      }
    },
  },

  // User endpoints
  user: {
    getProfile: () => fetchFromApi<UserItem>('user/profile'),
    getGroups: () => fetchFromApi<GroupItem>('user/groups'),
  },

  // Guide Management endpoints
  guide: {
    // Experience management for guides
    getMyExperiences: () => fetchFromApi<ExperienceItem>('guide/experiences'),
    updateExperience: (id: number, data: any) => fetchFromApi<ExperienceItem>(`guide/experiences/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify(data),
    }),

    // Group management for guides
    getMyGroups: () => fetchFromApi<GroupItem>('guide/groups'),
    createGroup: (data: any) => fetchFromApi<GroupItem>('guide/groups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify(data),
    }),
    updateGroup: (id: number, data: any) => fetchFromApi<GroupItem>(`guide/groups/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify(data),
    }),
    deleteGroup: (id: number) => fetchFromApi<void>(`guide/groups/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    }),

    // Guide profile and configuration
    getGuideProfile: () => fetchFromApi<any>('guide/profile'),

    // Restaurant management for experiences
    getAvailableRestaurants: () => fetchFromApi<any>('guide/restaurants'),
    assignRestaurantToExperience: (experienceId: number, restaurantId: number) => fetchFromApi<any>(`guide/experiences/${experienceId}/restaurant`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify({ restaurant_id: restaurantId }),
    }),

    // Time slots and capacity management
    updateExperienceTimeSlots: (experienceId: number, data: any) => fetchFromApi<any>(`guide/experiences/${experienceId}/time-slots`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify(data),
    }),

    // Group member management
    addGroupMember: (groupId: number, memberData: any) => fetchFromApi<any>(`guide/groups/${groupId}/members`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify(memberData),
    }),
    removeGroupMember: (groupId: number, memberId: number) => fetchFromApi<void>(`guide/groups/${groupId}/members/${memberId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    }),

    // Dashboard statistics
    getDashboardStats: () => fetchFromApi<any>('guide/dashboard-stats'),
  },

  // Generic method for direct API calls
  get: <T>(endpoint: string, options: RequestInit = {}) => fetchFromApi<T>(endpoint, options),
};

export interface ReservationItem {
  id: number;
  user_id: number;
  group_id?: number;
  is_group_reservation: boolean;
  experience_id: number;
  reservation_date: string;
  reservation_time: string | null;
  num_people: number;
  special_requests: string | null;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  admin_notes: string | null;
  experience: {
    id: number;
    title: string;
    type: string;
    agency: {
      id: number;
      name: string;
    } | null;
  };
  group?: {
    id: number;
    name: string;
    type: string;
    description: string;
  };
  booking_code?: {
    id: number;
    code: string;
    expires_at: string | null;
    is_active: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface GroupItem {
  id: number;
  name: string;
  description: string;
  type: string;
  is_active: boolean;
  users?: {
    id: number;
    name: string;
    email: string;
    is_guide: boolean;
  }[];
  guides?: {
    id: number;
    name: string;
    email: string;
  }[];
  created_at: string;
  updated_at: string;
}

export interface UserItem {
  id: number;
  name: string;
  email: string;
  role: string;
  agency_id: number | null;
  groups?: {
    id: number;
    name: string;
    type: string;
    is_guide: boolean;
  }[];
  created_at: string;
  updated_at: string;
}

export default apiService;
