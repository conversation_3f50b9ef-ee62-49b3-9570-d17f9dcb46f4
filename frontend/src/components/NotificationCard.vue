<template>
  <div class="notification-card">
    <div class="notification-icon">
      <i class="pi pi-video"></i>
    </div>
    <div class="notification-content">
      <div class="notification-title">{{ notification.title }}</div>
      <div class="notification-date">{{ notification.date }}</div>
    </div>
    <button class="notification-action">{{ notification.buttonText }}</button>
  </div>
</template>

<script setup lang="ts">
interface Notification {
  title: string;
  date: string;
  buttonText: string;
}

defineProps<{
  notification: Notification;
}>();
</script>

<style scoped>
.notification-card {
  display: flex;
  align-items: center;
  background: linear-gradient(180deg, rgba(51, 73, 96, 0.54) 0%, rgba(51, 73, 96, 0.6) 100%);
  border-radius: 8px;
  padding: 0.65rem 0.9rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.notification-icon {
  background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.506133) 100%);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0.7rem;
  backdrop-filter: blur(2px);
}

.notification-icon i {
  color: #334960;
  font-size: 1.1rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.9rem;
  font-weight: 300;
  color: #FFFFFF;
  margin-bottom: 0.25rem;
  font-family: 'Poppins', sans-serif;
}

.notification-date {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
}

.notification-action {
  background-color: #6366F1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.4rem 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
  transition: background-color 0.2s, transform 0.2s;
}

.notification-action:hover {
  background-color: #5457E6;
  transform: translateY(-1px);
}
</style>
