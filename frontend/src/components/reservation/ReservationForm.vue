<template>
  <div class="reservation-form-container">
    <h3 class="reservation-title">Reservar esta experiencia</h3>

    <!-- Confirmation Dialog for Existing Reservation -->
    <Dialog
      v-model:visible="showUpdateConfirmation"
      modal
      header="Ya tienes una reserva"
      :style="{ width: '90%', maxWidth: '450px', zIndex: 9999 }"
      :closable="false"
      class="reservation-dialog"
    >
      <div class="confirmation-dialog">
        <p>Ya tienes una reserva para esta experiencia el día {{ formatDate(existingReservation?.reservation_date) }}.</p>
        <p>¿Deseas modificar tu reserva existente con los nuevos datos?</p>

        <div class="existing-reservation-details">
          <p><strong>Reserva actual:</strong></p>
          <ul>
            <li>Fecha: {{ formatDate(existingReservation?.reservation_date) }}</li>
            <li>Personas: {{ existingReservation?.num_people }}</li>
            <li v-if="existingReservation?.reservation_time">Hora: {{ existingReservation?.reservation_time }}</li>
          </ul>

          <p><strong>Nueva reserva:</strong></p>
          <ul>
            <li>Fecha: {{ formatDate(form.reservation_date) }}</li>
            <li>Personas: {{ form.num_people }}</li>
            <li v-if="form.reservation_time">Hora: {{ form.reservation_time }}</li>
          </ul>
        </div>

        <div class="dialog-actions">
          <button
            type="button"
            class="cancel-button"
            @click="showUpdateConfirmation = false"
          >
            Cancelar
          </button>
          <button
            type="button"
            class="modify-button"
            @click="updateExistingReservation"
          >
            Modificar reserva
          </button>
        </div>
      </div>
    </Dialog>

    <form @submit.prevent="submitReservation" class="reservation-form">
      <div v-if="error" class="error-message">
        {{ error }}
      </div>

      <div v-if="success" class="success-message">
        <i class="pi pi-check-circle"></i>
        {{ success }}
      </div>

      <div class="form-group">
        <label for="reservation-date">Fecha de reserva <span class="required">*</span></label>
        <input
          type="date"
          id="reservation-date"
          v-model="form.reservation_date"
          class="form-input"
          :min="minDate"
          required
        />
      </div>

      <div v-if="showTimeField" class="form-group">
        <label for="reservation-time">Hora de reserva <span class="required">*</span></label>

        <div v-if="isLoadingTimeSlots" class="loading-time-slots">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Cargando horarios disponibles...</span>
        </div>

        <div v-else-if="availableTimeSlots.length === 0 && form.reservation_date" class="no-time-slots">
          <i class="pi pi-calendar-times"></i>
          <span>No hay horarios disponibles para esta fecha. Por favor, seleccione otra fecha.</span>
        </div>

        <div v-else-if="availableTimeSlots.length > 0" class="time-slots-grid">
          <button
            v-for="timeSlot in availableTimeSlotsWithAvailability"
            :key="timeSlot.time"
            type="button"
            class="time-slot-button"
            :class="{
              'selected': form.reservation_time === timeSlot.time,
              'full': !timeSlot.available
            }"
            @click="timeSlot.available && (form.reservation_time = timeSlot.time)"
            :disabled="!timeSlot.available"
            :title="timeSlot.available ? `Disponible (${timeSlot.capacity} plazas)` : 'Completo'"
          >
            {{ timeSlot.time }}
            <span v-if="!timeSlot.available" class="slot-status">Completo</span>
          </button>
        </div>

        <select
          v-else
          id="reservation-time"
          v-model="form.reservation_time"
          class="form-input"
          required
        >
          <option value="">Seleccionar hora</option>
          <option v-for="timeSlot in availableTimeSlots" :key="timeSlot" :value="timeSlot">
            {{ timeSlot }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label for="num-people">Número de personas <span class="required">*</span></label>
        <input
          type="number"
          id="num-people"
          v-model="form.num_people"
          class="form-input"
          min="1"
          max="50"
          required
        />
      </div>

      <div class="form-group">
        <label for="special-requests">Solicitudes especiales</label>
        <textarea
          id="special-requests"
          v-model="form.special_requests"
          class="form-textarea"
          placeholder="Indique cualquier solicitud especial o requerimiento"
        ></textarea>
      </div>

      <button
        type="submit"
        class="reservation-button"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? 'Procesando...' : 'Reservar ahora' }}
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import Dialog from 'primevue/dialog';
import { authToken, isAuthenticated, currentUser } from '../../services/auth';
import apiService from '../../services/api';

// Props
interface Props {
  experienceId: number;
  experienceType: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['reservationComplete']);

// Format date for display
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString;
  }
};

// Auth service
const router = useRouter();

// Check authentication status on component mount
console.log('Auth status:', {
  isAuthenticated: isAuthenticated.value,
  token: authToken.value ? 'Token exists' : 'No token',
  user: currentUser.value
});

// Form interface
interface ReservationForm {
  experience_id: number;
  reservation_date: string;
  reservation_time: string;
  num_people: number;
  special_requests: string;
  force_update?: boolean;
}

// Form state
const form = ref<ReservationForm>({
  experience_id: props.experienceId,
  reservation_date: '',
  reservation_time: '',
  num_people: 1,
  special_requests: ''
});

// Define the reservation type
interface Reservation {
  id: number;
  experience_id: number;
  reservation_date: string;
  reservation_time: string | null;
  num_people: number;
  special_requests: string | null;
  status: string;
  group_id?: number;
  is_group_reservation?: boolean;
}

// For handling existing reservations
const existingReservation = ref<Reservation | null>(null);
const showUpdateConfirmation = ref(false);

const isSubmitting = ref(false);
const isLoadingTimeSlots = ref(false);
const error = ref('');
const success = ref('');
const availableTimeSlots = ref<string[]>([]);
const timeSlotCapacity = ref<number>(0);
const maxReservationsPerDay = ref<number>(0);
const totalReservationsForDay = ref<number>(0);

// Computed properties
const showTimeField = computed(() => {
  // Show time field for all experience types
  return true;
});

const minDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

// Computed property for time slots with availability information
const availableTimeSlotsWithAvailability = computed(() => {
  return availableTimeSlots.value.map(timeSlot => ({
    time: timeSlot,
    available: true, // If it's in the list from the API, it's available
    capacity: timeSlotCapacity.value
  }));
});



// Watch for date changes to fetch available time slots
watch(() => form.value.reservation_date, async (newDate) => {
  if (newDate && showTimeField.value) {
    await fetchAvailableTimeSlots();
  }
});

// Fetch available time slots from the API
const fetchAvailableTimeSlots = async () => {
  if (!form.value.reservation_date) return;

  try {
    isLoadingTimeSlots.value = true;
    form.value.reservation_time = ''; // Reset time selection when date changes

    // Use the API service's getAvailableTimeSlots method
    const response = await apiService.reservations.getAvailableTimeSlots(props.experienceId, form.value.reservation_date);
    console.log('Time slots response:', response);

    // The API returns the time slots directly
    availableTimeSlots.value = response.time_slots || [];

    // Store capacity information
    timeSlotCapacity.value = response.capacity_per_time_slot || 0;
    maxReservationsPerDay.value = response.max_reservations_per_day || 0;
    totalReservationsForDay.value = response.total_reservations_for_day || 0;

    // Sort time slots chronologically
    availableTimeSlots.value.sort((a, b) => {
      const timeA = new Date(`2000-01-01T${a}`);
      const timeB = new Date(`2000-01-01T${b}`);
      return timeA.getTime() - timeB.getTime();
    });

    // Show a message if no time slots are available due to capacity
    if (availableTimeSlots.value.length === 0 && maxReservationsPerDay.value > 0 &&
        totalReservationsForDay.value >= maxReservationsPerDay.value) {
      error.value = `Esta experiencia ha alcanzado su capacidad máxima para el día ${formatDate(form.value.reservation_date)}. Por favor, seleccione otra fecha.`;
    }
  } catch (err) {
    console.error('Error fetching time slots:', err);
    error.value = 'No se pudieron cargar los horarios disponibles. Por favor, inténtelo de nuevo.';
    availableTimeSlots.value = [];
  } finally {
    isLoadingTimeSlots.value = false;
  }
};

// Update an existing reservation
const updateExistingReservation = async () => {
  if (!existingReservation.value) {
    showUpdateConfirmation.value = false;
    return;
  }

  isSubmitting.value = true;
  error.value = '';

  // Check if the reservation is already confirmed
  const isConfirmed = existingReservation.value.status === 'confirmed';

  // If confirmed, show a warning about limited changes
  if (isConfirmed) {
    // Check if trying to change date, time or experience
    const changingDate = form.value.reservation_date !== existingReservation.value.reservation_date;
    const changingTime = form.value.reservation_time !== (existingReservation.value.reservation_time || '');
    const changingExperience = parseInt(form.value.experience_id.toString()) !== existingReservation.value.experience_id;

    if (changingDate || changingTime || changingExperience) {
      error.value = 'Para una reserva confirmada, solo puedes modificar el número de personas y las solicitudes especiales.';
      showUpdateConfirmation.value = false;
      isSubmitting.value = false;
      return;
    }
  }

  try {
    console.log('Updating existing reservation:', existingReservation.value.id);

    // Create update payload
    const updatePayload = {
      experience_id: form.value.experience_id,
      reservation_date: form.value.reservation_date,
      reservation_time: form.value.reservation_time,
      num_people: form.value.num_people,
      special_requests: form.value.special_requests,
      force_update: true
    };

    // Call the API to update the reservation
    const data = await apiService.reservations.update(existingReservation.value.id, updatePayload);
    console.log('Update response:', data);

    // Close the dialog
    showUpdateConfirmation.value = false;

    // Show success message
    success.value = 'Reserva actualizada con éxito.';

    // Reset form
    form.value = {
      experience_id: props.experienceId,
      reservation_date: '',
      reservation_time: '',
      num_people: 1,
      special_requests: '',
      force_update: false
    };

    // Emit event to parent component
    emit('reservationComplete', data);
  } catch (err: any) {
    console.error('Error updating reservation:', err);
    error.value = err.message || 'Error al actualizar la reserva. Por favor, inténtelo de nuevo.';
  } finally {
    isSubmitting.value = false;
  }
};

// Submit the reservation
const submitReservation = async () => {
  console.log('Submitting reservation, auth status:', {
    isAuthenticated: isAuthenticated.value,
    token: authToken.value ? 'Token exists' : 'No token',
    user: currentUser.value
  });

  // Check if user is logged in
  if (!authToken.value) {
    error.value = 'Debe iniciar sesión para realizar una reserva';
    console.log('User not authenticated, redirecting to login');
    setTimeout(() => {
      router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath));
    }, 2000);
    return;
  }

  // Validate form
  if (showTimeField.value && !form.value.reservation_time) {
    error.value = 'Por favor, seleccione una hora para la reserva';
    return;
  }

  isSubmitting.value = true;
  error.value = '';

  try {
    console.log('Submitting reservation with form data:', form.value);

    try {
      // Use the API service's reservations.create method
      const reservationData = await apiService.reservations.create(form.value);
      console.log('Reservation response:', reservationData);
      success.value = 'Reserva creada con éxito. Recibirá una confirmación por correo electrónico.';

      // Reset form
      form.value = {
        experience_id: props.experienceId,
        reservation_date: '',
        reservation_time: '',
        num_people: 1,
        special_requests: ''
      };

      // Emit event to parent component
      emit('reservationComplete', reservationData);
    } catch (apiError: any) {
      console.log('API error response:', apiError);

      // Check if this is a conflict error (user already has a reservation)
      console.log('Checking for conflict error:', apiError);
      console.log('Status:', apiError.status);
      console.log('Has existing reservation:', apiError.data?.has_existing_reservation);

      if (apiError.status === 409 && apiError.data?.has_existing_reservation) {
        console.log('Conflict detected! Showing confirmation dialog');
        console.log('Existing reservation:', apiError.data.existing_reservation);

        // Check if the existing reservation is in the past
        const reservationDate = new Date(apiError.data.existing_reservation.reservation_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

        if (reservationDate < today) {
          console.log('Existing reservation is in the past, not showing update dialog');
          error.value = 'Ya has disfrutado de esta experiencia en el pasado. Puedes hacer una nueva reserva.';
          return; // Exit early, don't show update dialog
        }

        // Store the existing reservation and show confirmation dialog
        existingReservation.value = apiError.data.existing_reservation;
        showUpdateConfirmation.value = true;
        return; // Exit early, don't reset form or show success message
      }

      // Re-throw the error for the outer catch block
      throw apiError;
    }

    // Clear success message after 5 seconds
    setTimeout(() => {
      success.value = '';
    }, 5000);
  } catch (err: any) {
    console.error('Error creating reservation:', err);
    error.value = err.message || 'Error al crear la reserva. Por favor, inténtelo de nuevo.';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.reservation-form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 1.5rem;
}

.reservation-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: #333;
}

.reservation-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #555;
}

.required {
  color: #e53e3e;
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.reservation-button {
  background-color: #DC8960;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: background-color 0.2s;
}

.reservation-button:hover {
  background-color: #c77a53;
}

.reservation-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.success-message::before {
  content: '\e930'; /* PrimeIcons check circle */
  font-family: 'primeicons';
  font-size: 1.5rem;
  color: #166534;
}

/* Time Slots Styles */
.loading-time-slots {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 4px;
  color: #6b7280;
}

.no-time-slots {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fee2e2;
  border-radius: 4px;
  color: #b91c1c;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.time-slot-button {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #f9fafb;
  color: #4b5563;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.time-slot-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.time-slot-button.selected {
  background-color: #334960;
  color: white;
  border-color: #334960;
}

.time-slot-button.full {
  background-color: #f3f4f6;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
  position: relative;
  overflow: hidden;
}

.time-slot-button .slot-status {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ef4444;
  color: white;
  font-size: 0.6rem;
  padding: 1px 0;
  text-align: center;
}

/* Dialog Styles */
.reservation-dialog {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  background-color: white !important;
}

.reservation-dialog :deep(.p-dialog-header) {
  background-color: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1.25rem 1.5rem !important;
}

.reservation-dialog :deep(.p-dialog-content) {
  background-color: white !important;
  padding: 0 !important;
  overflow: visible !important;
}

.reservation-dialog :deep(.p-dialog-mask) {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(2px);
}

.confirmation-dialog {
  padding: 1.5rem;
  background-color: white;
}

.existing-reservation-details {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.25rem;
  margin: 1.5rem 0;
  border: 1px solid #e5e7eb;
}

.existing-reservation-details p {
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: #334155;
}

.existing-reservation-details ul {
  list-style: none;
  padding-left: 0.5rem;
  margin: 0.75rem 0 1.25rem;
}

.existing-reservation-details li {
  margin-bottom: 0.5rem;
  color: #4b5563;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button,
.modify-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  font-family: 'Poppins', sans-serif;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.modify-button {
  background-color: #DC8960;
  color: white;
}

.modify-button:hover {
  background-color: #c77a53;
}
</style>
