<template>
  <div class="group-reservation-form-container">

    <form @submit.prevent="submitReservation" class="reservation-form">
      <div v-if="error" class="error-message">
        {{ error }}
      </div>

      <div v-if="success" class="success-message">
        <i class="pi pi-check-circle"></i>
        {{ success }}
      </div>

      <div class="form-group">
        <label for="group-select">Grupo <span class="required">*</span></label>
        <select
          id="group-select"
          v-model="form.group_id"
          class="form-input"
          required
        >
          <option value="">Seleccionar grupo</option>
          <option v-for="group in userGroups" :key="group.id" :value="group.id">
            {{ group.name }} ({{ group.type }})
          </option>
        </select>
      </div>

      <div class="form-group">
        <label for="reservation-date">Fecha de reserva <span class="required">*</span></label>
        <input
          type="date"
          id="reservation-date"
          v-model="form.reservation_date"
          class="form-input"
          :min="minDate"
          required
        />
      </div>

      <div v-if="showTimeField" class="form-group">
        <label for="reservation-time">Hora de reserva <span class="required">*</span></label>

        <div v-if="isLoadingTimeSlots" class="loading-time-slots">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Cargando horarios disponibles...</span>
        </div>

        <div v-else-if="availableTimeSlots.length === 0 && form.reservation_date" class="no-time-slots">
          <i class="pi pi-calendar-times"></i>
          <span>No hay horarios disponibles para esta fecha. Por favor, seleccione otra fecha.</span>
        </div>

        <div v-else-if="availableTimeSlots.length > 0" class="time-slots-grid">
          <button
            v-for="timeSlot in availableTimeSlotsWithAvailability"
            :key="timeSlot.time"
            type="button"
            class="time-slot-button"
            :class="{
              'selected': form.reservation_time === timeSlot.time,
              'full': !timeSlot.available
            }"
            @click="timeSlot.available && (form.reservation_time = timeSlot.time)"
            :disabled="!timeSlot.available"
            :title="timeSlot.available ? `Disponible (${timeSlot.capacity} plazas)` : 'Completo'"
          >
            {{ timeSlot.time }}
            <span v-if="!timeSlot.available" class="slot-status">Completo</span>
          </button>
        </div>

        <select
          v-else
          id="reservation-time"
          v-model="form.reservation_time"
          class="form-input"
          required
        >
          <option value="">Seleccionar hora</option>
          <option v-for="timeSlot in availableTimeSlots" :key="timeSlot" :value="timeSlot">
            {{ timeSlot }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label for="num-people">Número de personas <span class="required">*</span></label>
        <input
          type="number"
          id="num-people"
          v-model="form.num_people"
          class="form-input"
          min="1"
          max="50"
          required
        />
      </div>

      <div class="form-group">
        <label for="special-requests">Solicitudes especiales</label>
        <textarea
          id="special-requests"
          v-model="form.special_requests"
          class="form-textarea"
          placeholder="Indique cualquier solicitud especial o requerimiento"
        ></textarea>
      </div>

      <button
        type="submit"
        class="reservation-button"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? 'Procesando...' : 'Reservar ahora' }}
      </button>
    </form>

    <div v-if="bookingCode" class="booking-code-container">
      <h4>Código de reserva</h4>
      <p class="booking-code">{{ bookingCode }}</p>
      <p class="booking-code-info">Comparte este código con los miembros del grupo para que puedan acceder a la reserva.</p>
    </div>

    <!-- Confirmation Dialog for Updating Existing Reservation -->
    <Dialog
      v-model:visible="showUpdateConfirmation"
      modal
      header="Reserva existente"
      :style="{ width: '90%', maxWidth: '450px', zIndex: 9999 }"
      :closable="false"
      class="reservation-dialog"
    >
      <div class="confirmation-dialog">
        <p>Ya tienes una reserva para esta experiencia. ¿Deseas modificarla?</p>

        <div v-if="existingReservation" class="reservation-details">
          <p><strong>Detalles de la reserva actual:</strong></p>
          <ul>
            <li>Fecha: {{ formatDate(existingReservation.reservation_date) }}</li>
            <li v-if="existingReservation.reservation_time">Hora: {{ existingReservation.reservation_time }}</li>
            <li>Personas: {{ existingReservation.num_people }}</li>
          </ul>
        </div>

        <div class="dialog-actions">
          <button
            type="button"
            class="cancel-button"
            @click="showUpdateConfirmation = false"
          >
            Cancelar
          </button>
          <button
            type="button"
            class="modify-button"
            @click="updateExistingReservation"
          >
            Modificar reserva
          </button>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authToken, isAuthenticated, currentUser, getUserGroups } from '../../services/auth';
import apiService from '../../services/api';
import Dialog from 'primevue/dialog';

// Props
interface Props {
  experienceId: number;
  experienceType: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['reservationComplete']);

// Auth service
const router = useRouter();

// Check authentication status on component mount
console.log('Auth status:', {
  isAuthenticated: isAuthenticated.value,
  token: authToken.value ? 'Token exists' : 'No token',
  user: currentUser.value
});

// Form state
const form = ref({
  experience_id: props.experienceId,
  group_id: '',
  reservation_date: '',
  reservation_time: '',
  num_people: 1,
  special_requests: ''
});

const isSubmitting = ref(false);
const isLoadingTimeSlots = ref(false);
const error = ref('');
const success = ref('');
const availableTimeSlots = ref<string[]>([]);
const timeSlotCapacity = ref<number>(0);
const maxReservationsPerDay = ref<number>(0);
const totalReservationsForDay = ref<number>(0);
const userGroups = ref<any[]>([]);
const bookingCode = ref<string | null>(null);

// Define the reservation type
interface Reservation {
  id: number;
  experience_id: number;
  reservation_date: string;
  reservation_time: string | null;
  num_people: number;
  special_requests: string | null;
  status: string;
  group_id?: number;
  is_group_reservation?: boolean;
}

// For handling existing reservations
const existingReservation = ref<Reservation | null>(null);
const showUpdateConfirmation = ref(false);

// Computed properties
const showTimeField = computed(() => {
  // Show time field for all experience types
  return true;
});

const minDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

// Computed property for time slots with availability information
const availableTimeSlotsWithAvailability = computed(() => {
  return availableTimeSlots.value.map(timeSlot => ({
    time: timeSlot,
    available: true, // If it's in the list from the API, it's available
    capacity: timeSlotCapacity.value
  }));
});

// Format date for display
const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString;
  }
};

// Fetch user groups on component mount
const fetchUserGroups = async () => {
  if (!authToken.value) return;

  try {
    // First check if the user already has groups in the currentUser object
    if (currentUser.value?.groups && currentUser.value.groups.length > 0) {
      userGroups.value = currentUser.value.groups;
      return;
    }

    // Otherwise try to fetch them from the API
    const groups = await getUserGroups();
    userGroups.value = groups || [];

    if (userGroups.value.length === 0) {
      error.value = 'No perteneces a ningún grupo. Contacta con tu agencia para ser añadido a un grupo.';
    }
  } catch (err) {
    console.error('Error fetching user groups:', err);
    error.value = 'No se pudieron cargar los grupos. Por favor, inténtelo de nuevo.';
  }
};

// Watch for date changes to fetch available time slots
watch(() => form.value.reservation_date, async (newDate) => {
  if (newDate && showTimeField.value) {
    await fetchAvailableTimeSlots();
  }
});

// Update an existing reservation
const updateExistingReservation = async () => {
  if (!existingReservation.value) {
    showUpdateConfirmation.value = false;
    return;
  }

  isSubmitting.value = true;
  error.value = '';

  try {
    console.log('Updating existing reservation:', existingReservation.value.id);

    // Create update payload with group reservation data
    const updatePayload = {
      experience_id: form.value.experience_id,
      group_id: form.value.group_id,
      is_group_reservation: true,
      reservation_date: form.value.reservation_date,
      reservation_time: form.value.reservation_time,
      num_people: form.value.num_people,
      special_requests: form.value.special_requests,
      force_update: true // This flag tells the backend to update the existing reservation
    };

    // Call the API to update the reservation
    const data = await apiService.reservations.update(existingReservation.value.id, updatePayload);
    console.log('Update response:', data);

    // Close the dialog
    showUpdateConfirmation.value = false;

    // Show success message
    success.value = 'Reserva actualizada con éxito.';

    // Extract booking code if available
    if (data.booking_code && data.booking_code.code) {
      bookingCode.value = data.booking_code.code;
    }

    // Reset form
    form.value = {
      experience_id: props.experienceId,
      group_id: '',
      reservation_date: '',
      reservation_time: '',
      num_people: 1,
      special_requests: ''
    };

    // Emit event to parent component
    emit('reservationComplete', data);
  } catch (err: any) {
    console.error('Error updating reservation:', err);
    error.value = err.message || 'Error al actualizar la reserva. Por favor, inténtelo de nuevo.';
  } finally {
    isSubmitting.value = false;
  }
};

// Fetch available time slots from the API
const fetchAvailableTimeSlots = async () => {
  if (!form.value.reservation_date) return;

  try {
    isLoadingTimeSlots.value = true;
    form.value.reservation_time = ''; // Reset time selection when date changes

    // Use the API service's getAvailableTimeSlots method
    const response = await apiService.reservations.getAvailableTimeSlots(props.experienceId, form.value.reservation_date);
    console.log('Time slots response:', response);

    // The API returns the time slots directly
    availableTimeSlots.value = response.time_slots || [];

    // Store capacity information
    timeSlotCapacity.value = response.capacity_per_time_slot || 0;
    maxReservationsPerDay.value = response.max_reservations_per_day || 0;
    totalReservationsForDay.value = response.total_reservations_for_day || 0;

    // Sort time slots chronologically
    availableTimeSlots.value.sort((a, b) => {
      const timeA = new Date(`2000-01-01T${a}`);
      const timeB = new Date(`2000-01-01T${b}`);
      return timeA.getTime() - timeB.getTime();
    });

    // Show a message if no time slots are available due to capacity
    if (availableTimeSlots.value.length === 0 && maxReservationsPerDay.value > 0 &&
        totalReservationsForDay.value >= maxReservationsPerDay.value) {
      error.value = `Esta experiencia ha alcanzado su capacidad máxima para el día ${formatDate(form.value.reservation_date)}. Por favor, seleccione otra fecha.`;
    }
  } catch (err) {
    console.error('Error fetching time slots:', err);
    error.value = 'No se pudieron cargar los horarios disponibles. Por favor, inténtelo de nuevo.';
    availableTimeSlots.value = [];
  } finally {
    isLoadingTimeSlots.value = false;
  }
};

// Submit the reservation
const submitReservation = async () => {
  console.log('Submitting group reservation, auth status:', {
    isAuthenticated: isAuthenticated.value,
    token: authToken.value ? 'Token exists' : 'No token',
    user: currentUser.value
  });

  // Check if user is logged in
  if (!authToken.value) {
    error.value = 'Debe iniciar sesión para realizar una reserva';
    console.log('User not authenticated, redirecting to login');
    setTimeout(() => {
      router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath));
    }, 2000);
    return;
  }

  // Validate form
  if (!form.value.group_id) {
    error.value = 'Por favor, seleccione un grupo';
    return;
  }

  if (showTimeField.value && !form.value.reservation_time) {
    error.value = 'Por favor, seleccione una hora para la reserva';
    return;
  }

  isSubmitting.value = true;
  error.value = '';

  try {
    // Create a copy of the form with the group reservation flag
    const formData = {
      ...form.value,
      is_group_reservation: true
    };

    try {
      // Use the API service's reservations.create method
      const data = await apiService.reservations.create(formData);
      console.log('Group reservation response:', data);

      success.value = 'Reserva grupal creada con éxito.';

      // Extract booking code if available
      if (data.booking_code && data.booking_code.code) {
        bookingCode.value = data.booking_code.code;
      }

      // Reset form
      form.value = {
        experience_id: props.experienceId,
        group_id: '',
        reservation_date: '',
        reservation_time: '',
        num_people: 1,
        special_requests: ''
      };

      // Emit event to parent component
      emit('reservationComplete', data);
    } catch (error: any) {
      console.log('API error response:', error);

      // Check if this is a conflict error (user already has a reservation)
      console.log('Checking for conflict error:', error);
      console.log('Status:', error.status);
      console.log('Has existing reservation:', error.data?.has_existing_reservation);

      if (error.status === 409 && error.data?.has_existing_reservation) {
        console.log('Conflict detected! Showing confirmation dialog');
        console.log('Existing reservation:', error.data.existing_reservation);

        // Store the existing reservation and show confirmation dialog
        existingReservation.value = error.data.existing_reservation;
        showUpdateConfirmation.value = true;
        return; // Exit early, don't reset form or show success message
      }

      // Re-throw the error for the outer catch block
      throw error;
    }
  } catch (err: any) {
    console.error('Error creating group reservation:', err);
    error.value = err.message || 'Error al crear la reserva grupal. Por favor, inténtelo de nuevo.';
  } finally {
    isSubmitting.value = false;
  }
};

// Fetch user groups on component mount
onMounted(() => {
  fetchUserGroups();
});
</script>

<style scoped>
.group-reservation-form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 1.5rem;
}

.reservation-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: #333;
}

.reservation-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #555;
}

.required {
  color: #e53e3e;
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.reservation-button {
  background-color: #DC8960;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: background-color 0.2s;
}

.reservation-button:hover {
  background-color: #c77a53;
}

.reservation-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.success-message::before {
  content: '\e930'; /* PrimeIcons check circle */
  font-family: 'primeicons';
  font-size: 1.5rem;
  color: #166534;
}

/* Time Slots Styles */
.loading-time-slots {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 4px;
  color: #6b7280;
}

.no-time-slots {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fee2e2;
  border-radius: 4px;
  color: #b91c1c;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.time-slot-button {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #f9fafb;
  color: #4b5563;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.time-slot-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.time-slot-button.selected {
  background-color: #334960;
  color: white;
  border-color: #334960;
}

.time-slot-button.full {
  background-color: #f3f4f6;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
  position: relative;
  overflow: hidden;
}

.time-slot-button .slot-status {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ef4444;
  color: white;
  font-size: 0.6rem;
  padding: 1px 0;
  text-align: center;
}

/* Dialog Styles */
.reservation-dialog {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  background-color: white !important;
}

.reservation-dialog :deep(.p-dialog-header) {
  background-color: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1.25rem 1.5rem !important;
}

.reservation-dialog :deep(.p-dialog-content) {
  background-color: white !important;
  padding: 0 !important;
  overflow: visible !important;
}

.reservation-dialog :deep(.p-dialog-mask) {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(2px);
}

.booking-code-container {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.booking-code-container h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.booking-code {
  font-size: 1.5rem;
  font-weight: 700;
  color: #DC8960;
  text-align: center;
  margin: 0.5rem 0;
  letter-spacing: 2px;
}

.booking-code-info {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.5rem;
}

.confirmation-dialog {
  padding: 1.5rem;
  background-color: white;
}

.confirmation-dialog .reservation-details {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.25rem;
  margin: 1.5rem 0;
  border: 1px solid #e5e7eb;
}

.confirmation-dialog .reservation-details p {
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: #334155;
}

.confirmation-dialog .reservation-details ul {
  list-style: none;
  padding-left: 0.5rem;
  margin: 0.75rem 0 1.25rem;
}

.confirmation-dialog .reservation-details li {
  margin-bottom: 0.5rem;
  color: #4b5563;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button,
.modify-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  font-family: 'Poppins', sans-serif;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.modify-button {
  background-color: #DC8960;
  color: white;
}

.modify-button:hover {
  background-color: #c77a53;
}
</style>
