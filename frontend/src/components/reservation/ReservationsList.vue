<template>
  <div class="reservations-list">
    <div v-if="loading" class="loading-container">
      <i class="pi pi-spin pi-spinner" style="font-size: 1.5rem"></i>
      <p>Cargando reservas...</p>
    </div>

    <div v-else-if="!reservations || reservations.length === 0" class="empty-state">
      <p>No tienes reservas activas.</p>
      <router-link to="/experiencias" class="explore-link">
        Explorar experiencias
      </router-link>
    </div>

    <div v-else class="reservations-grid">
      <div
        v-for="reservation in reservations"
        :key="reservation.id"
        class="reservation-card"
        :class="{ 'cancelled': reservation.status === 'cancelled' }"
      >
        <div class="reservation-header">
          <h3 class="experience-title">{{ reservation.experience?.title }}</h3>
          <div class="reservation-status" :class="statusClass(reservation.status)">
            {{ translateStatus(reservation.status) }}
          </div>
        </div>

        <div class="reservation-details">
          <div class="detail-row">
            <i class="pi pi-calendar"></i>
            <span>{{ formatDate(reservation.reservation_date) }}</span>
          </div>

          <div v-if="reservation.reservation_time" class="detail-row">
            <i class="pi pi-clock"></i>
            <span>{{ reservation.reservation_time }}</span>
          </div>

          <div class="detail-row">
            <i class="pi pi-users"></i>
            <span>{{ reservation.num_people }} {{ reservation.num_people === 1 ? 'persona' : 'personas' }}</span>
          </div>

          <div v-if="reservation.special_requests" class="detail-row special-requests">
            <i class="pi pi-comment"></i>
            <span>{{ reservation.special_requests }}</span>
          </div>
        </div>

        <div class="reservation-actions">
          <button
            @click="viewExperience(reservation)"
            class="view-experience-link"
          >
            Ver experiencia
          </button>

          <button
            v-if="reservation.status === 'pending'"
            @click="$emit('cancel-reservation', reservation)"
            class="cancel-button"
          >
            Cancelar reserva
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No imports needed
import type { ReservationItem } from '../../services/api';

// Props
interface Props {
  reservations: ReservationItem[];
  loading: boolean;
}

defineProps<Props>();

// Emits
const emit = defineEmits(['cancel-reservation', 'view-experience']);

// Format date for display
const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString;
  }
};

// Translate status to Spanish
const translateStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': 'Pendiente',
    'confirmed': 'Confirmada',
    'completed': 'Completada',
    'cancelled': 'Cancelada'
  };

  return statusMap[status] || status;
};

// Get CSS class for status
const statusClass = (status: string): string => {
  return `status-${status}`;
};

// Handle view experience click with safety check
const viewExperience = (reservation: any) => {
  // Check if experience_id exists and is valid
  if (reservation && (reservation.experience_id || (reservation.experience && reservation.experience.id))) {
    // Use experience_id directly if available, otherwise use the id from the experience object
    const experienceId = reservation.experience_id || (reservation.experience ? reservation.experience.id : undefined);
    emit('view-experience', experienceId);
  } else {
    console.error('Cannot view experience: Missing experience ID', reservation);
    // You could show a toast or notification here
  }
};
</script>

<style scoped>
.reservations-list {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.explore-link {
  display: inline-block;
  margin-top: 1rem;
  color: #DC8960;
  text-decoration: none;
  font-weight: 500;
}

.explore-link:hover {
  text-decoration: underline;
}

.reservations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.reservation-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  transition: transform 0.2s, box-shadow 0.2s;
}

.reservation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.reservation-card.cancelled {
  opacity: 0.7;
}

.reservation-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.experience-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #333;
  flex: 1;
}

.reservation-status {
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-align: center;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-confirmed {
  background-color: #dcfce7;
  color: #166534;
}

.status-completed {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #b91c1c;
}

.reservation-details {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #555;
}

.detail-row i {
  color: #777;
  font-size: 0.9rem;
}

.special-requests {
  font-style: italic;
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.5rem;
}

.reservation-actions {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-experience-link {
  color: #DC8960;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

.view-experience-link:hover {
  text-decoration: underline;
}

.cancel-button {
  background-color: transparent;
  color: #b91c1c;
  border: 1px solid #b91c1c;
  border-radius: 4px;
  padding: 0.4rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: #fee2e2;
}

@media (max-width: 768px) {
  .reservations-grid {
    grid-template-columns: 1fr;
  }
}
</style>
