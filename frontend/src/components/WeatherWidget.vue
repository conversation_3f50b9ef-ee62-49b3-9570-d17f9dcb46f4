<template>
  <div class="weather-widget">
    <div class="weather-header">
      <div class="weather-location-date">
        <div class="weather-date">{{ currentDate }}</div>
        <div class="weather-location">Badajoz, España</div>
      </div>
      <div class="weather-actions">
        <button class="refresh-button">
          <i class="pi pi-refresh"></i>
        </button>
      </div>
    </div>
    <div class="weather-content">
      <div class="weather-main">
        <div class="weather-icon">
          <i class="pi pi-sun"></i>
        </div>
        <div class="weather-temp">24°C</div>
        <div class="weather-condition">Soleado</div>
      </div>
      <div class="weather-details">
        <div class="weather-detail">
          <div class="detail-label">Humedad</div>
          <div class="detail-value">45%</div>
        </div>
        <div class="weather-detail">
          <div class="detail-label">Viento</div>
          <div class="detail-value">10 km/h</div>
        </div>
        <div class="weather-detail">
          <div class="detail-label">Visibilidad</div>
          <div class="detail-value">20 km</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Format current date in Spanish
const currentDate = ref(new Date().toLocaleDateString('es-ES', {
  weekday: 'long',
  day: 'numeric',
  month: 'long'
}));
</script>

<style scoped>
.weather-widget {
  background-color: #f5f7fa;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-family: 'Poppins', sans-serif;
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.weather-date {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.25rem;
  text-transform: capitalize;
}

.weather-location {
  font-size: 0.9rem;
  font-weight: 500;
  color: #334960;
}

.refresh-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
}

.weather-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weather-main {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.weather-icon {
  font-size: 2rem;
  color: #DC8960;
  margin-bottom: 0.5rem;
}

.weather-temp {
  font-size: 2rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.25rem;
}

.weather-condition {
  font-size: 0.8rem;
  color: #666;
}

.weather-details {
  display: flex;
  gap: 1rem;
}

.weather-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.detail-label {
  font-size: 0.7rem;
  color: #888;
  margin-bottom: 0.25rem;
}

.detail-value {
  font-size: 0.8rem;
  font-weight: 500;
  color: #334960;
}
</style>
