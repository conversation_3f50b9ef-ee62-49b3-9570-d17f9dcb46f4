<template>
  <router-link :to="route" class="menu-card" :style="{ backgroundImage: `url(${background})` }">
    <div class="menu-card-content">
      <img :src="icon" alt="Menu Icon" class="menu-icon" />
      <div class="menu-title">{{ title }}</div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  icon: string;
  route: string;
  background: string;
}

const props = defineProps<Props>();
</script>

<style scoped>
.menu-card {
  width: 101px;
  height: 82px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  color: #333;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 0.5rem;
}

.menu-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 0.25rem;
}

.menu-title {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  font-family: 'Poppins', sans-serif;
}
</style>
