<template>
  <div class="time-slots-manager">
    <div class="manager-content">
      <!-- Error Display -->
      <div v-if="error" class="error-message">
        <i class="pi pi-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Experience Info -->
      <div class="experience-info">
        <h4>{{ experience?.title }}</h4>
        <p>{{ experience?.short_description }}</p>
      </div>

      <!-- Time Slots Configuration -->
      <div class="time-slots-section">
        <h3>Horarios Disponibles</h3>
        
        <div class="slots-list">
          <div
            v-for="(slot, index) in form.time_slots"
            :key="index"
            class="slot-item"
          >
            <div class="slot-time">
              <input
                v-model="form.time_slots[index]"
                type="time"
                class="time-input"
                required
              />
            </div>
            
            <div class="slot-capacity">
              <label>Capacidad:</label>
              <input
                v-model.number="form.capacities[index]"
                type="number"
                class="capacity-input"
                min="1"
                max="100"
                placeholder="10"
              />
            </div>
            
            <button
              type="button"
              @click="removeTimeSlot(index)"
              class="remove-slot-button"
              :disabled="form.time_slots.length <= 1"
            >
              <i class="pi pi-trash"></i>
            </button>
          </div>
        </div>

        <button
          type="button"
          @click="addTimeSlot"
          class="add-slot-button"
        >
          <i class="pi pi-plus"></i>
          Añadir Horario
        </button>
      </div>

      <!-- General Settings -->
      <div class="general-settings">
        <h3>Configuración General</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="default_capacity">Capacidad por Defecto</label>
            <input
              id="default_capacity"
              v-model.number="form.default_capacity"
              type="number"
              class="form-input"
              min="1"
              max="100"
              placeholder="10"
            />
          </div>

          <div class="form-group">
            <label for="booking_window">Ventana de Reserva (días)</label>
            <input
              id="booking_window"
              v-model.number="form.booking_window"
              type="number"
              class="form-input"
              min="1"
              max="365"
              placeholder="30"
            />
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="form.allow_same_day_booking"
              type="checkbox"
              class="checkbox-input"
            />
            <span class="checkbox-text">Permitir reservas el mismo día</span>
          </label>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="form.auto_confirm"
              type="checkbox"
              class="checkbox-input"
            />
            <span class="checkbox-text">Confirmar reservas automáticamente</span>
          </label>
        </div>
      </div>

      <!-- Weekly Schedule -->
      <div class="weekly-schedule">
        <h3>Horario Semanal</h3>
        
        <div class="days-grid">
          <div
            v-for="day in weekDays"
            :key="day.value"
            class="day-item"
          >
            <label class="day-label">
              <input
                v-model="form.active_days"
                :value="day.value"
                type="checkbox"
                class="day-checkbox"
              />
              <span class="day-name">{{ day.label }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Special Dates -->
      <div class="special-dates">
        <h3>Fechas Especiales</h3>
        
        <div class="special-dates-list">
          <div
            v-for="(specialDate, index) in form.special_dates"
            :key="index"
            class="special-date-item"
          >
            <input
              v-model="specialDate.date"
              type="date"
              class="date-input"
            />
            
            <select
              v-model="specialDate.type"
              class="type-select"
            >
              <option value="closed">Cerrado</option>
              <option value="limited">Capacidad Limitada</option>
              <option value="special">Horario Especial</option>
            </select>
            
            <input
              v-if="specialDate.type === 'limited'"
              v-model.number="specialDate.capacity"
              type="number"
              class="capacity-input"
              placeholder="Capacidad"
              min="1"
            />
            
            <button
              type="button"
              @click="removeSpecialDate(index)"
              class="remove-button"
            >
              <i class="pi pi-trash"></i>
            </button>
          </div>
        </div>

        <button
          type="button"
          @click="addSpecialDate"
          class="add-button"
        >
          <i class="pi pi-plus"></i>
          Añadir Fecha Especial
        </button>
      </div>

      <!-- Actions -->
      <div class="actions">
        <button type="button" @click="$emit('cancel')" class="cancel-button">
          Cancelar
        </button>
        <button type="button" @click="handleSave" :disabled="loading" class="save-button">
          <i v-if="loading" class="pi pi-spin pi-spinner"></i>
          <i v-else class="pi pi-check"></i>
          {{ loading ? 'Guardando...' : 'Guardar Horarios' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';

// Props
interface Props {
  experience?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  save: [data: any];
  cancel: [];
}>();

// Reactive state
const loading = ref(false);
const error = ref<string | null>(null);

// Week days configuration
const weekDays = ref([
  { value: 'monday', label: 'Lunes' },
  { value: 'tuesday', label: 'Martes' },
  { value: 'wednesday', label: 'Miércoles' },
  { value: 'thursday', label: 'Jueves' },
  { value: 'friday', label: 'Viernes' },
  { value: 'saturday', label: 'Sábado' },
  { value: 'sunday', label: 'Domingo' }
]);

// Form data
const form = reactive({
  time_slots: ['09:00', '11:00', '14:00', '16:00'],
  capacities: [10, 10, 10, 10],
  default_capacity: 10,
  booking_window: 30,
  allow_same_day_booking: false,
  auto_confirm: true,
  active_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
  special_dates: [] as Array<{
    date: string;
    type: 'closed' | 'limited' | 'special';
    capacity?: number;
    note?: string;
  }>
});

// Methods
const addTimeSlot = () => {
  form.time_slots.push('12:00');
  form.capacities.push(form.default_capacity);
};

const removeTimeSlot = (index: number) => {
  if (form.time_slots.length > 1) {
    form.time_slots.splice(index, 1);
    form.capacities.splice(index, 1);
  }
};

const addSpecialDate = () => {
  form.special_dates.push({
    date: '',
    type: 'closed'
  });
};

const removeSpecialDate = (index: number) => {
  form.special_dates.splice(index, 1);
};

const validateForm = (): boolean => {
  error.value = null;

  if (form.time_slots.length === 0) {
    error.value = 'Debe haber al menos un horario disponible';
    return false;
  }

  // Check for duplicate time slots
  const uniqueSlots = new Set(form.time_slots);
  if (uniqueSlots.size !== form.time_slots.length) {
    error.value = 'No puede haber horarios duplicados';
    return false;
  }

  // Validate capacities
  for (let i = 0; i < form.capacities.length; i++) {
    if (!form.capacities[i] || form.capacities[i] < 1) {
      error.value = 'Todas las capacidades deben ser mayor a 0';
      return false;
    }
  }

  if (form.active_days.length === 0) {
    error.value = 'Debe seleccionar al menos un día de la semana';
    return false;
  }

  return true;
};

const handleSave = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    error.value = null;

    // Prepare data for API
    const timeSlotData = {
      time_slots: form.time_slots,
      capacities: form.capacities,
      default_capacity: form.default_capacity,
      booking_window: form.booking_window,
      allow_same_day_booking: form.allow_same_day_booking,
      auto_confirm: form.auto_confirm,
      active_days: form.active_days,
      special_dates: form.special_dates.filter(date => date.date)
    };

    emit('save', timeSlotData);
  } catch (err: any) {
    console.error('Error saving time slots:', err);
    error.value = err.message || 'Error al guardar los horarios';
  } finally {
    loading.value = false;
  }
};

const loadExperienceData = () => {
  if (props.experience) {
    // Load existing time slots if available
    if (props.experience.time_slots && props.experience.time_slots.length > 0) {
      form.time_slots = [...props.experience.time_slots];
    }

    // Load capacities
    if (props.experience.capacities && props.experience.capacities.length > 0) {
      form.capacities = [...props.experience.capacities];
    } else {
      // Initialize capacities array to match time slots
      form.capacities = form.time_slots.map(() => props.experience.capacity || form.default_capacity);
    }

    // Load other settings
    if (props.experience.default_capacity) {
      form.default_capacity = props.experience.default_capacity;
    }
    if (props.experience.booking_window) {
      form.booking_window = props.experience.booking_window;
    }
    if (props.experience.allow_same_day_booking !== undefined) {
      form.allow_same_day_booking = props.experience.allow_same_day_booking;
    }
    if (props.experience.auto_confirm !== undefined) {
      form.auto_confirm = props.experience.auto_confirm;
    }
    if (props.experience.active_days && props.experience.active_days.length > 0) {
      form.active_days = [...props.experience.active_days];
    }
    if (props.experience.special_dates && props.experience.special_dates.length > 0) {
      form.special_dates = [...props.experience.special_dates];
    }
  }
};

// Watchers
watch(() => props.experience, loadExperienceData, { immediate: true });

// Lifecycle
onMounted(() => {
  loadExperienceData();
});
</script>

<style scoped>
.time-slots-manager {
  max-width: 600px;
  margin: 0 auto;
}

.manager-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.experience-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.experience-info h4 {
  margin: 0 0 0.5rem 0;
  color: #2c5aa0;
}

.experience-info p {
  margin: 0;
  color: #6c757d;
}

.time-slots-section,
.general-settings,
.weekly-schedule,
.special-dates {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.time-slots-section h3,
.general-settings h3,
.weekly-schedule h3,
.special-dates h3 {
  margin: 0 0 1rem 0;
  color: #2c5aa0;
  font-size: 1.125rem;
  font-weight: 600;
}

.slots-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.slot-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.slot-time {
  flex: 1;
}

.time-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.slot-capacity {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.slot-capacity label {
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

.capacity-input {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.remove-slot-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.remove-slot-button:hover:not(:disabled) {
  background: #c0392b;
}

.remove-slot-button:disabled {
  background: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
}

.add-slot-button,
.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.add-slot-button:hover,
.add-button:hover {
  background: #218838;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-input {
  margin: 0;
}

.checkbox-text {
  font-weight: 500;
  color: #495057;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.day-item {
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

.day-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.day-label:hover {
  background: #e9ecef;
}

.day-checkbox {
  margin: 0;
}

.day-name {
  font-weight: 500;
  color: #495057;
}

.special-dates-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.special-date-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.date-input,
.type-select {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.remove-button:hover {
  background: #c0392b;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.cancel-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button {
  background: #6c757d;
  color: white;
}

.cancel-button:hover {
  background: #5a6268;
}

.save-button {
  background: #2c5aa0;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .slot-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .slot-capacity {
    justify-content: space-between;
  }
  
  .special-date-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .days-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
}
</style>
