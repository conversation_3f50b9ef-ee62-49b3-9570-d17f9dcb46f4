<template>
  <div class="group-edit-form">
    <form @submit.prevent="handleSubmit" class="form">
      <!-- Error Display -->
      <div v-if="error" class="error-message">
        <i class="pi pi-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Basic Information -->
      <div class="form-section">
        <h3>Información del Grupo</h3>

        <div class="form-group">
          <label for="name">Nombre del Grupo <span class="required">*</span></label>
          <input
            id="name"
            v-model="form.name"
            type="text"
            class="form-input"
            :class="{ error: errors.name }"
            placeholder="Ej: Familia García, Grupo Aventura..."
            required
          />
          <span v-if="errors.name" class="field-error">{{ errors.name }}</span>
        </div>

        <div class="form-group">
          <label for="description">Descripción</label>
          <textarea
            id="description"
            v-model="form.description"
            class="form-textarea"
            rows="3"
            placeholder="Descripción opcional del grupo..."
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="type">Tipo de Grupo <span class="required">*</span></label>
            <select
              id="type"
              v-model="form.type"
              class="form-select"
              :class="{ error: errors.type }"
              required
            >
              <option value="">Seleccionar tipo</option>
              <option value="normal">Grupo Normal</option>
              <option value="couple">Pareja</option>
              <option value="pet">Con Mascota (Perro)</option>
            </select>
            <span v-if="errors.type" class="field-error">{{ errors.type }}</span>
          </div>

          <div class="form-group">
            <label for="number_of_people">Número de Personas <span class="required">*</span></label>
            <input
              id="number_of_people"
              v-model.number="form.number_of_people"
              type="number"
              class="form-input"
              :class="{ error: errors.number_of_people }"
              min="1"
              max="50"
              required
            />
            <span v-if="errors.number_of_people" class="field-error">{{ errors.number_of_people }}</span>
          </div>
        </div>
      </div>

      <!-- Group Members -->
      <div class="form-section">
        <h3>Miembros del Grupo</h3>

        <div class="members-list">
          <div
            v-for="(member, index) in form.group_members"
            :key="index"
            class="member-item"
          >
            <input
              v-model="form.group_members[index]"
              type="text"
              class="form-input"
              :placeholder="`Nombre del miembro ${index + 1}`"
            />
            <button
              type="button"
              @click="removeMember(index)"
              class="remove-button"
              :disabled="form.group_members.length <= 1"
            >
              <i class="pi pi-trash"></i>
            </button>
          </div>
        </div>

        <button
          type="button"
          @click="addMember"
          class="add-member-button"
          :disabled="form.group_members.length >= form.number_of_people"
        >
          <i class="pi pi-plus"></i>
          Añadir Miembro
        </button>
      </div>

      <!-- Group Preferences -->
      <div class="form-section">
        <h3>Preferencias del Grupo</h3>

        <div class="form-group">
          <label for="special_requirements">Requisitos Especiales</label>
          <textarea
            id="special_requirements"
            v-model="form.special_requirements"
            class="form-textarea"
            rows="3"
            placeholder="Alergias, necesidades especiales, preferencias..."
          ></textarea>
        </div>

        <div class="form-group">
          <label for="preferred_language">Idioma Preferido</label>
          <select
            id="preferred_language"
            v-model="form.preferred_language"
            class="form-select"
          >
            <option value="">Sin preferencia</option>
            <option value="es">Español</option>
            <option value="en">Inglés</option>
            <option value="fr">Francés</option>
            <option value="de">Alemán</option>
            <option value="pt">Portugués</option>
          </select>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="difficulty_preference">Dificultad Preferida</label>
            <select
              id="difficulty_preference"
              v-model="form.difficulty_preference"
              class="form-select"
            >
              <option value="">Sin preferencia</option>
              <option value="easy">Fácil</option>
              <option value="medium">Medio</option>
              <option value="hard">Difícil</option>
            </select>
          </div>

          <div class="form-group">
            <label for="budget_range">Rango de Presupuesto (€)</label>
            <select
              id="budget_range"
              v-model="form.budget_range"
              class="form-select"
            >
              <option value="">Sin preferencia</option>
              <option value="low">Bajo (0-50€)</option>
              <option value="medium">Medio (50-100€)</option>
              <option value="high">Alto (100€+)</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Pet Information (only for pet groups) -->
      <div v-if="form.type === 'pet'" class="form-section">
        <h3>Información de la Mascota</h3>

        <div class="form-row">
          <div class="form-group">
            <label for="pet_name">Nombre de la Mascota</label>
            <input
              id="pet_name"
              v-model="form.pet_name"
              type="text"
              class="form-input"
              placeholder="Nombre del perro"
            />
          </div>

          <div class="form-group">
            <label for="pet_breed">Raza</label>
            <input
              id="pet_breed"
              v-model="form.pet_breed"
              type="text"
              class="form-input"
              placeholder="Raza del perro"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="pet_size">Tamaño</label>
            <select
              id="pet_size"
              v-model="form.pet_size"
              class="form-select"
            >
              <option value="">Seleccionar tamaño</option>
              <option value="small">Pequeño</option>
              <option value="medium">Mediano</option>
              <option value="large">Grande</option>
            </select>
          </div>

          <div class="form-group">
            <label for="pet_age">Edad (años)</label>
            <input
              id="pet_age"
              v-model.number="form.pet_age"
              type="number"
              class="form-input"
              min="0"
              max="20"
              placeholder="Edad del perro"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="pet_notes">Notas sobre la Mascota</label>
          <textarea
            id="pet_notes"
            v-model="form.pet_notes"
            class="form-textarea"
            rows="3"
            placeholder="Comportamiento, necesidades especiales, etc..."
          ></textarea>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" @click="$emit('cancel')" class="cancel-button">
          Cancelar
        </button>
        <button type="submit" :disabled="loading" class="save-button">
          <i v-if="loading" class="pi pi-spin pi-spinner"></i>
          <i v-else class="pi pi-check"></i>
          {{ loading ? 'Guardando...' : (group ? 'Actualizar Grupo' : 'Crear Grupo') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';

// Props
interface Props {
  group?: any;
  agencyConfig?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  save: [data: any];
  cancel: [];
}>();

// Reactive state
const loading = ref(false);
const error = ref<string | null>(null);
const errors = ref<Record<string, string>>({});

// Form data
const form = reactive({
  name: '',
  description: '',
  type: '',
  number_of_people: 1,
  group_members: [''],
  special_requirements: '',
  preferred_language: '',
  difficulty_preference: '',
  budget_range: '',
  pet_name: '',
  pet_breed: '',
  pet_size: '',
  pet_age: null as number | null,
  pet_notes: ''
});

// Methods
const validateForm = (): boolean => {
  errors.value = {};

  if (!form.name.trim()) {
    errors.value.name = 'El nombre del grupo es obligatorio';
  }

  if (!form.type) {
    errors.value.type = 'El tipo de grupo es obligatorio';
  }

  if (!form.number_of_people || form.number_of_people < 1) {
    errors.value.number_of_people = 'El número de personas debe ser mayor a 0';
  }

  if (form.number_of_people > 50) {
    errors.value.number_of_people = 'El número máximo de personas es 50';
  }

  return Object.keys(errors.value).length === 0;
};

const addMember = () => {
  if (form.group_members.length < form.number_of_people) {
    form.group_members.push('');
  }
};

const removeMember = (index: number) => {
  if (form.group_members.length > 1) {
    form.group_members.splice(index, 1);
  }
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    error.value = null;

    // Prepare form data
    const formData: any = { ...form };

    // Filter out empty member names
    formData.group_members = formData.group_members.filter((name: string) => name.trim());

    // Remove pet-related fields if not a pet group
    if (formData.type !== 'pet') {
      delete formData.pet_name;
      delete formData.pet_breed;
      delete formData.pet_size;
      delete formData.pet_age;
      delete formData.pet_notes;
    }

    // Remove null/empty values
    Object.keys(formData).forEach(key => {
      if (formData[key] === null || formData[key] === '') {
        delete formData[key];
      }
    });

    emit('save', formData);
  } catch (err: any) {
    console.error('Error submitting form:', err);
    error.value = err.message || 'Error al guardar el grupo';
  } finally {
    loading.value = false;
  }
};

const loadGroupData = () => {
  if (props.group) {
    Object.keys(form).forEach(key => {
      if (props.group[key] !== undefined) {
        (form as any)[key] = props.group[key];
      }
    });

    // Ensure we have at least one member slot
    if (!form.group_members || form.group_members.length === 0) {
      form.group_members = [''];
    }

    // Adjust member slots to match number of people
    while (form.group_members.length < form.number_of_people) {
      form.group_members.push('');
    }
  }
};

// Watchers
watch(() => props.group, loadGroupData, { immediate: true });

watch(() => form.number_of_people, (newValue) => {
  // Adjust member slots when number of people changes
  if (newValue > form.group_members.length) {
    while (form.group_members.length < newValue) {
      form.group_members.push('');
    }
  } else if (newValue < form.group_members.length) {
    form.group_members = form.group_members.slice(0, newValue);
  }
});

// Lifecycle
onMounted(() => {
  loadGroupData();
});
</script>

<style scoped>
.group-edit-form {
  max-width: 700px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.form-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c5aa0;
  font-size: 1.125rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.required {
  color: #e74c3c;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.form-input.error,
.form-select.error {
  border-color: #e74c3c;
}

.field-error {
  display: block;
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.member-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.member-item .form-input {
  flex: 1;
}

.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.remove-button:hover:not(:disabled) {
  background: #c0392b;
}

.remove-button:disabled {
  background: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
}

.add-member-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-member-button:hover:not(:disabled) {
  background: #218838;
}

.add-member-button:disabled {
  background: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.cancel-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.cancel-button {
  background: #6c757d;
  color: white;
}

.cancel-button:hover {
  background: #5a6268;
}

.save-button {
  background: #2c5aa0;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-section {
    padding: 1rem;
  }

  .member-item {
    flex-direction: column;
    align-items: stretch;
  }

  .remove-button {
    align-self: flex-end;
    width: auto;
    padding: 0.5rem 1rem;
  }
}
</style>
