<template>
  <div class="restaurant-assignment">
    <div class="assignment-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando restaurantes...</p>
      </div>

      <!-- Error Display -->
      <div v-else-if="error" class="error-message">
        <i class="pi pi-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Assignment Content -->
      <div v-else class="assignment-form">
        <!-- Experience Info -->
        <div class="experience-info">
          <h4>{{ experience?.title }}</h4>
          <p>Asignar restaurante para esta experiencia</p>
        </div>

        <!-- Current Assignment -->
        <div v-if="currentRestaurant" class="current-assignment">
          <h3>Restaurante Actual</h3>
          <div class="restaurant-card current">
            <div class="restaurant-image">
              <img 
                :src="currentRestaurant.image || '/assets/images/restaurants/default.jpeg'" 
                :alt="currentRestaurant.name"
              />
            </div>
            <div class="restaurant-info">
              <h4>{{ currentRestaurant.name }}</h4>
              <p>{{ currentRestaurant.description }}</p>
              <div class="restaurant-details">
                <span v-if="currentRestaurant.cuisine_type" class="cuisine-type">
                  {{ currentRestaurant.cuisine_type }}
                </span>
                <span v-if="currentRestaurant.rating" class="rating">
                  <i class="pi pi-star-fill"></i>
                  {{ currentRestaurant.rating }}
                </span>
              </div>
            </div>
            <button 
              @click="removeAssignment" 
              class="remove-button"
              title="Quitar asignación"
            >
              <i class="pi pi-times"></i>
            </button>
          </div>
        </div>

        <!-- Available Restaurants -->
        <div class="available-restaurants">
          <h3>Restaurantes Disponibles</h3>
          
          <!-- Search Bar -->
          <div class="search-bar">
            <i class="pi pi-search"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar restaurantes..."
              class="search-input"
            />
          </div>

          <!-- Filters -->
          <div class="filters">
            <select v-model="selectedCuisine" class="filter-select">
              <option value="">Todos los tipos de cocina</option>
              <option v-for="cuisine in cuisineTypes" :key="cuisine" :value="cuisine">
                {{ cuisine }}
              </option>
            </select>
            
            <select v-model="selectedRating" class="filter-select">
              <option value="">Todas las valoraciones</option>
              <option value="4">4+ estrellas</option>
              <option value="3">3+ estrellas</option>
              <option value="2">2+ estrellas</option>
            </select>
          </div>

          <!-- Restaurants List -->
          <div v-if="filteredRestaurants.length === 0" class="no-restaurants">
            <i class="pi pi-home"></i>
            <p>No se encontraron restaurantes que coincidan con los filtros.</p>
          </div>

          <div v-else class="restaurants-list">
            <div
              v-for="restaurant in filteredRestaurants"
              :key="restaurant.id"
              class="restaurant-card"
              :class="{ selected: selectedRestaurant?.id === restaurant.id }"
              @click="selectRestaurant(restaurant)"
            >
              <div class="restaurant-image">
                <img 
                  :src="restaurant.image || '/assets/images/restaurants/default.jpeg'" 
                  :alt="restaurant.name"
                />
              </div>
              
              <div class="restaurant-info">
                <h4>{{ restaurant.name }}</h4>
                <p>{{ restaurant.description || 'Sin descripción disponible' }}</p>
                
                <div class="restaurant-details">
                  <span v-if="restaurant.cuisine_type" class="cuisine-type">
                    {{ restaurant.cuisine_type }}
                  </span>
                  <span v-if="restaurant.rating" class="rating">
                    <i class="pi pi-star-fill"></i>
                    {{ restaurant.rating }}
                  </span>
                  <span v-if="restaurant.price_range" class="price-range">
                    {{ getPriceRangeDisplay(restaurant.price_range) }}
                  </span>
                </div>

                <div v-if="restaurant.address" class="restaurant-address">
                  <i class="pi pi-map-marker"></i>
                  {{ restaurant.address }}
                </div>

                <div v-if="restaurant.opening_hours" class="opening-hours">
                  <i class="pi pi-clock"></i>
                  {{ restaurant.opening_hours }}
                </div>

                <div class="restaurant-actions">
                  <a 
                    v-if="restaurant.website" 
                    :href="restaurant.website" 
                    target="_blank" 
                    class="action-link"
                  >
                    <i class="pi pi-external-link"></i>
                    Sitio Web
                  </a>
                  <a 
                    v-if="restaurant.menu_url" 
                    :href="restaurant.menu_url" 
                    target="_blank" 
                    class="action-link"
                  >
                    <i class="pi pi-file"></i>
                    Menú
                  </a>
                </div>
              </div>

              <div class="selection-indicator">
                <i class="pi pi-check"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="actions">
          <button type="button" @click="$emit('cancel')" class="cancel-button">
            Cancelar
          </button>
          <button 
            type="button" 
            @click="handleSave" 
            :disabled="saving || (!selectedRestaurant && !isRemoving)"
            class="save-button"
          >
            <i v-if="saving" class="pi pi-spin pi-spinner"></i>
            <i v-else class="pi pi-check"></i>
            {{ saving ? 'Guardando...' : getActionText() }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import apiService from '../../services/api';

// Props
interface Props {
  experience?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  save: [data: any];
  cancel: [];
}>();

// Reactive state
const loading = ref(true);
const saving = ref(false);
const error = ref<string | null>(null);
const restaurants = ref<any[]>([]);
const selectedRestaurant = ref<any>(null);
const currentRestaurant = ref<any>(null);
const isRemoving = ref(false);

// Search and filters
const searchQuery = ref('');
const selectedCuisine = ref('');
const selectedRating = ref('');

// Computed
const cuisineTypes = computed(() => {
  const types = restaurants.value
    .map(r => r.cuisine_type)
    .filter(Boolean)
    .filter((type, index, arr) => arr.indexOf(type) === index);
  return types.sort();
});

const filteredRestaurants = computed(() => {
  let filtered = restaurants.value;

  // Exclude currently assigned restaurant
  if (currentRestaurant.value) {
    filtered = filtered.filter(r => r.id !== currentRestaurant.value.id);
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(restaurant =>
      restaurant.name.toLowerCase().includes(query) ||
      restaurant.description?.toLowerCase().includes(query) ||
      restaurant.cuisine_type?.toLowerCase().includes(query)
    );
  }

  if (selectedCuisine.value) {
    filtered = filtered.filter(restaurant => 
      restaurant.cuisine_type === selectedCuisine.value
    );
  }

  if (selectedRating.value) {
    const minRating = parseFloat(selectedRating.value);
    filtered = filtered.filter(restaurant => 
      restaurant.rating && restaurant.rating >= minRating
    );
  }

  return filtered;
});

// Methods
const getPriceRangeDisplay = (priceRange: string): string => {
  const ranges: Record<string, string> = {
    'low': '€',
    'medium': '€€',
    'high': '€€€',
    'luxury': '€€€€'
  };
  return ranges[priceRange] || priceRange;
};

const selectRestaurant = (restaurant: any) => {
  selectedRestaurant.value = restaurant;
  isRemoving.value = false;
};

const removeAssignment = () => {
  selectedRestaurant.value = null;
  isRemoving.value = true;
};

const getActionText = (): string => {
  if (isRemoving.value) {
    return 'Quitar Restaurante';
  }
  if (selectedRestaurant.value) {
    return 'Asignar Restaurante';
  }
  return 'Seleccionar Restaurante';
};

const handleSave = async () => {
  try {
    saving.value = true;
    error.value = null;

    const data = {
      restaurant_id: isRemoving.value ? null : selectedRestaurant.value?.id,
      action: isRemoving.value ? 'remove' : 'assign'
    };

    emit('save', data);
  } catch (err: any) {
    console.error('Error saving restaurant assignment:', err);
    error.value = err.message || 'Error al asignar el restaurante';
  } finally {
    saving.value = false;
  }
};

const loadRestaurants = async () => {
  try {
    loading.value = true;
    error.value = null;

    const response = await apiService.guide.getAvailableRestaurants();
    restaurants.value = response.data || [];

    // Load current restaurant assignment
    if (props.experience?.restaurant) {
      currentRestaurant.value = props.experience.restaurant;
    }

  } catch (err: any) {
    console.error('Error loading restaurants:', err);
    error.value = err.message || 'Error al cargar los restaurantes';
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(() => {
  loadRestaurants();
});
</script>

<style scoped>
.restaurant-assignment {
  max-width: 700px;
  margin: 0 auto;
}

.assignment-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container i {
  font-size: 2rem;
  color: #2c5aa0;
  margin-bottom: 1rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.experience-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.experience-info h4 {
  margin: 0 0 0.5rem 0;
  color: #2c5aa0;
}

.experience-info p {
  margin: 0;
  color: #6c757d;
}

.current-assignment,
.available-restaurants {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.current-assignment h3,
.available-restaurants h3 {
  margin: 0 0 1rem 0;
  color: #2c5aa0;
  font-size: 1.125rem;
  font-weight: 600;
}

.search-bar {
  position: relative;
  margin-bottom: 1rem;
}

.search-bar i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 1rem;
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  min-width: 150px;
}

.no-restaurants {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.no-restaurants i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #dee2e6;
}

.restaurants-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.restaurant-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.restaurant-card:hover {
  border-color: #2c5aa0;
  box-shadow: 0 2px 8px rgba(44, 90, 160, 0.1);
}

.restaurant-card.selected {
  border-color: #2c5aa0;
  background: #f8f9ff;
}

.restaurant-card.current {
  border-color: #28a745;
  background: #f8fff8;
  cursor: default;
}

.restaurant-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.restaurant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.restaurant-info {
  flex: 1;
}

.restaurant-info h4 {
  margin: 0 0 0.5rem 0;
  color: #2c5aa0;
  font-size: 1.125rem;
}

.restaurant-info p {
  margin: 0 0 0.75rem 0;
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
}

.restaurant-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.cuisine-type,
.rating,
.price-range {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.rating {
  color: #f39c12;
}

.restaurant-address,
.opening-hours {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.restaurant-actions {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.action-link {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #2c5aa0;
  text-decoration: none;
  font-size: 0.875rem;
}

.action-link:hover {
  text-decoration: underline;
}

.selection-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 24px;
  height: 24px;
  background: #2c5aa0;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.restaurant-card.selected .selection-indicator {
  opacity: 1;
}

.remove-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 32px;
  height: 32px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-button:hover {
  background: #c0392b;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.cancel-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button {
  background: #6c757d;
  color: white;
}

.cancel-button:hover {
  background: #5a6268;
}

.save-button {
  background: #2c5aa0;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .restaurant-card {
    flex-direction: column;
  }
  
  .restaurant-image {
    width: 100%;
    height: 150px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .restaurant-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
