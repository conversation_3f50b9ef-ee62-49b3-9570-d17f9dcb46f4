<template>
  <div class="experience-edit-form">
    <form @submit.prevent="handleSubmit" class="form">
      <!-- <PERSON><PERSON><PERSON> Display -->
      <div v-if="error" class="error-message">
        <i class="pi pi-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Basic Information -->
      <div class="form-section">
        <h3>Información Básica</h3>

        <div class="form-group">
          <label for="title">Título <span class="required">*</span></label>
          <input
            id="title"
            v-model="form.title"
            type="text"
            class="form-input"
            :class="{ error: errors.title }"
            required
          />
          <span v-if="errors.title" class="field-error">{{ errors.title }}</span>
        </div>

        <div class="form-group">
          <label for="short_description">Descripción Corta</label>
          <textarea
            id="short_description"
            v-model="form.short_description"
            class="form-textarea"
            rows="3"
            placeholder="Descripción breve de la experiencia..."
          ></textarea>
        </div>

        <div class="form-group">
          <label for="description">Descripción Completa</label>
          <textarea
            id="description"
            v-model="form.description"
            class="form-textarea"
            rows="5"
            placeholder="Descripción detallada de la experiencia..."
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="type">Tipo <span class="required">*</span></label>
            <select
              id="type"
              v-model="form.type"
              class="form-select"
              :class="{ error: errors.type }"
              required
            >
              <option value="">Seleccionar tipo</option>
              <option value="tour">Tour</option>
              <option value="restaurant">Restaurante</option>
              <option value="hotel">Hotel</option>
              <option value="activity">Actividad</option>
            </select>
            <span v-if="errors.type" class="field-error">{{ errors.type }}</span>
          </div>

          <div class="form-group">
            <label for="status">Estado</label>
            <select
              id="status"
              v-model="form.status"
              class="form-select"
            >
              <option value="active">Activo</option>
              <option value="inactive">Inactivo</option>
              <option value="draft">Borrador</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Capacity and Pricing -->
      <div class="form-section">
        <h3>Capacidad y Precios</h3>

        <div class="form-row">
          <div class="form-group">
            <label for="capacity">Capacidad Máxima</label>
            <input
              id="capacity"
              v-model.number="form.capacity"
              type="number"
              class="form-input"
              min="1"
              placeholder="Sin límite"
            />
          </div>

          <div class="form-group">
            <label for="price">Precio (€)</label>
            <input
              id="price"
              v-model.number="form.price"
              type="number"
              class="form-input"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="difficulty">Dificultad</label>
          <select
            id="difficulty"
            v-model="form.difficulty"
            class="form-select"
          >
            <option value="">Sin especificar</option>
            <option value="easy">Fácil</option>
            <option value="medium">Medio</option>
            <option value="hard">Difícil</option>
          </select>
        </div>
      </div>

      <!-- Location Information -->
      <div class="form-section">
        <h3>Ubicación</h3>

        <div class="form-group">
          <label for="location_name">Nombre del Lugar</label>
          <input
            id="location_name"
            v-model="form.location_name"
            type="text"
            class="form-input"
            placeholder="Ej: Plaza Mayor, Restaurante El Rincón..."
          />
        </div>

        <div class="form-group">
          <label for="address">Dirección</label>
          <input
            id="address"
            v-model="form.address"
            type="text"
            class="form-input"
            placeholder="Dirección completa..."
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="latitude">Latitud</label>
            <input
              id="latitude"
              v-model.number="form.latitude"
              type="number"
              class="form-input"
              step="any"
              placeholder="40.4168"
            />
          </div>

          <div class="form-group">
            <label for="longitude">Longitud</label>
            <input
              id="longitude"
              v-model.number="form.longitude"
              type="number"
              class="form-input"
              step="any"
              placeholder="-3.7038"
            />
          </div>
        </div>
      </div>

      <!-- Schedule Information -->
      <div class="form-section">
        <h3>Horarios</h3>

        <div class="form-row">
          <div class="form-group">
            <label for="start_date">Fecha de Inicio</label>
            <input
              id="start_date"
              v-model="form.start_date"
              type="date"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="end_date">Fecha de Fin</label>
            <input
              id="end_date"
              v-model="form.end_date"
              type="date"
              class="form-input"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="duration">Duración (minutos)</label>
          <input
            id="duration"
            v-model.number="form.duration"
            type="number"
            class="form-input"
            min="1"
            placeholder="120"
          />
        </div>
      </div>

      <!-- Additional Information -->
      <div class="form-section">
        <h3>Información Adicional</h3>

        <div class="form-group">
          <label for="requirements">Requisitos</label>
          <textarea
            id="requirements"
            v-model="form.requirements"
            class="form-textarea"
            rows="3"
            placeholder="Requisitos especiales, edad mínima, etc..."
          ></textarea>
        </div>

        <div class="form-group">
          <label for="included">Incluido</label>
          <textarea
            id="included"
            v-model="form.included"
            class="form-textarea"
            rows="3"
            placeholder="Qué está incluido en la experiencia..."
          ></textarea>
        </div>

        <div class="form-group">
          <label for="not_included">No Incluido</label>
          <textarea
            id="not_included"
            v-model="form.not_included"
            class="form-textarea"
            rows="3"
            placeholder="Qué no está incluido..."
          ></textarea>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" @click="$emit('cancel')" class="cancel-button">
          Cancelar
        </button>
        <button type="submit" :disabled="loading" class="save-button">
          <i v-if="loading" class="pi pi-spin pi-spinner"></i>
          <i v-else class="pi pi-check"></i>
          {{ loading ? 'Guardando...' : 'Guardar Cambios' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';

// Props
interface Props {
  experience?: any;
  agencyConfig?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  save: [data: any];
  cancel: [];
}>();

// Reactive state
const loading = ref(false);
const error = ref<string | null>(null);
const errors = ref<Record<string, string>>({});

// Form data
const form = reactive({
  title: '',
  short_description: '',
  description: '',
  type: '',
  status: 'active',
  capacity: null as number | null,
  price: null as number | null,
  difficulty: '',
  location_name: '',
  address: '',
  latitude: null as number | null,
  longitude: null as number | null,
  start_date: '',
  end_date: '',
  duration: null as number | null,
  requirements: '',
  included: '',
  not_included: ''
});

// Methods
const validateForm = (): boolean => {
  errors.value = {};

  if (!form.title.trim()) {
    errors.value.title = 'El título es obligatorio';
  }

  if (!form.type) {
    errors.value.type = 'El tipo es obligatorio';
  }

  if (form.start_date && form.end_date && form.start_date > form.end_date) {
    errors.value.end_date = 'La fecha de fin debe ser posterior a la fecha de inicio';
  }

  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    error.value = null;

    // Prepare form data
    const formData: any = { ...form };

    // Remove null values
    Object.keys(formData).forEach(key => {
      if (formData[key] === null || formData[key] === '') {
        delete formData[key];
      }
    });

    emit('save', formData);
  } catch (err: any) {
    console.error('Error submitting form:', err);
    error.value = err.message || 'Error al guardar la experiencia';
  } finally {
    loading.value = false;
  }
};

const loadExperienceData = () => {
  if (props.experience) {
    Object.keys(form).forEach(key => {
      if (props.experience[key] !== undefined) {
        (form as any)[key] = props.experience[key];
      }
    });

    // Handle location data
    if (props.experience.location) {
      form.location_name = props.experience.location.name || '';
      form.address = props.experience.location.address || '';
      form.latitude = props.experience.location.latitude || null;
      form.longitude = props.experience.location.longitude || null;
    }

    // Format dates
    if (props.experience.start_date) {
      form.start_date = props.experience.start_date.split('T')[0];
    }
    if (props.experience.end_date) {
      form.end_date = props.experience.end_date.split('T')[0];
    }
  }
};

// Watchers
watch(() => props.experience, loadExperienceData, { immediate: true });

// Lifecycle
onMounted(() => {
  loadExperienceData();
});
</script>

<style scoped>
.experience-edit-form {
  max-width: 800px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.form-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c5aa0;
  font-size: 1.125rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.required {
  color: #e74c3c;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.form-input.error,
.form-select.error {
  border-color: #e74c3c;
}

.field-error {
  display: block;
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.cancel-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.cancel-button {
  background: #6c757d;
  color: white;
}

.cancel-button:hover {
  background: #5a6268;
}

.save-button {
  background: #2c5aa0;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-section {
    padding: 1rem;
  }
}
</style>
