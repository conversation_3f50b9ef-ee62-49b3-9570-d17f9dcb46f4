<template>
  <div class="group-members-manager">
    <div class="manager-content">
      <!-- Error Display -->
      <div v-if="error" class="error-message">
        <i class="pi pi-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Group Info -->
      <div class="group-info">
        <h4>{{ group?.name }}</h4>
        <div class="group-details">
          <span class="group-type">{{ getGroupTypeLabel(group?.type) }}</span>
          <span class="group-capacity">{{ group?.number_of_people }} personas</span>
        </div>
      </div>

      <!-- Current Members -->
      <div class="current-members-section">
        <h3>Miembros Actuales</h3>

        <div v-if="currentMembers.length === 0" class="no-members">
          <i class="pi pi-users"></i>
          <p>No hay miembros en este grupo</p>
        </div>

        <div v-else class="members-list">
          <div
            v-for="(member, index) in currentMembers"
            :key="member.id || index"
            class="member-item"
          >
            <div class="member-avatar">
              {{ getInitials(member.name) }}
            </div>

            <div class="member-info">
              <h5>{{ member.name }}</h5>
              <p v-if="member.email">{{ member.email }}</p>
              <p v-if="member.phone">{{ member.phone }}</p>
              <div v-if="member.role" class="member-role">
                {{ getRoleLabel(member.role) }}
              </div>
            </div>

            <div class="member-actions">
              <button
                @click="editMember(member, index)"
                class="action-button edit-btn"
                title="Editar miembro"
              >
                <i class="pi pi-pencil"></i>
              </button>
              <button
                @click="removeMember(index)"
                class="action-button remove-btn"
                title="Eliminar miembro"
                :disabled="currentMembers.length <= 1"
              >
                <i class="pi pi-trash"></i>
              </button>
            </div>
          </div>
        </div>

        <button
          @click="addNewMember"
          class="add-member-button"
          :disabled="currentMembers.length >= (group?.number_of_people || 1)"
        >
          <i class="pi pi-plus"></i>
          Añadir Miembro
        </button>
      </div>

      <!-- Member Form Modal -->
      <div v-if="showMemberForm" class="member-form-overlay">
        <div class="member-form">
          <h3>{{ editingMember ? 'Editar Miembro' : 'Nuevo Miembro' }}</h3>

          <form @submit.prevent="saveMember" class="form">
            <div class="form-group">
              <label for="member_name">Nombre <span class="required">*</span></label>
              <input
                id="member_name"
                v-model="memberForm.name"
                type="text"
                class="form-input"
                :class="{ error: memberErrors.name }"
                required
              />
              <span v-if="memberErrors.name" class="field-error">{{ memberErrors.name }}</span>
            </div>

            <div class="form-group">
              <label for="member_email">Email</label>
              <input
                id="member_email"
                v-model="memberForm.email"
                type="email"
                class="form-input"
                :class="{ error: memberErrors.email }"
              />
              <span v-if="memberErrors.email" class="field-error">{{ memberErrors.email }}</span>
            </div>

            <div class="form-group">
              <label for="member_phone">Teléfono</label>
              <input
                id="member_phone"
                v-model="memberForm.phone"
                type="tel"
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label for="member_age">Edad</label>
              <input
                id="member_age"
                v-model.number="memberForm.age"
                type="number"
                class="form-input"
                min="0"
                max="120"
              />
            </div>

            <div class="form-group">
              <label for="member_role">Rol en el Grupo</label>
              <select
                id="member_role"
                v-model="memberForm.role"
                class="form-select"
              >
                <option value="">Sin rol específico</option>
                <option value="leader">Líder del grupo</option>
                <option value="contact">Persona de contacto</option>
                <option value="participant">Participante</option>
              </select>
            </div>

            <div class="form-group">
              <label for="member_notes">Notas</label>
              <textarea
                id="member_notes"
                v-model="memberForm.notes"
                class="form-textarea"
                rows="3"
                placeholder="Notas adicionales sobre el miembro..."
              ></textarea>
            </div>

            <div class="form-actions">
              <button type="button" @click="cancelMemberForm" class="cancel-button">
                Cancelar
              </button>
              <button type="submit" :disabled="memberSaving" class="save-button">
                <i v-if="memberSaving" class="pi pi-spin pi-spinner"></i>
                <i v-else class="pi pi-check"></i>
                {{ memberSaving ? 'Guardando...' : 'Guardar' }}
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Group Statistics -->
      <div class="group-stats">
        <h3>Estadísticas del Grupo</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Total de miembros:</span>
            <span class="stat-value">{{ currentMembers.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Capacidad máxima:</span>
            <span class="stat-value">{{ group?.number_of_people || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Espacios disponibles:</span>
            <span class="stat-value">{{ (group?.number_of_people || 0) - currentMembers.length }}</span>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="actions">
        <button type="button" @click="$emit('cancel')" class="cancel-button">
          Cancelar
        </button>
        <button type="button" @click="handleSave" :disabled="saving" class="save-button">
          <i v-if="saving" class="pi pi-spin pi-spinner"></i>
          <i v-else class="pi pi-check"></i>
          {{ saving ? 'Guardando...' : 'Guardar Cambios' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import apiService from '../../services/api';

// Props
interface Props {
  group?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  save: [];
  cancel: [];
}>();

// Reactive state
const saving = ref(false);
const memberSaving = ref(false);
const error = ref<string | null>(null);
const currentMembers = ref<any[]>([]);
const showMemberForm = ref(false);
const editingMember = ref<any>(null);
const editingIndex = ref(-1);

// Member form
const memberForm = reactive({
  name: '',
  email: '',
  phone: '',
  age: null as number | null,
  role: '',
  notes: ''
});

const memberErrors = ref<Record<string, string>>({});

// Methods
const getGroupTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    'normal': 'Grupo Normal',
    'couple': 'Pareja',
    'pet': 'Con Mascota'
  };
  return labels[type] || type;
};

const getRoleLabel = (role: string): string => {
  const labels: Record<string, string> = {
    'leader': 'Líder',
    'contact': 'Contacto',
    'participant': 'Participante'
  };
  return labels[role] || role;
};

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const addNewMember = () => {
  editingMember.value = null;
  editingIndex.value = -1;
  resetMemberForm();
  showMemberForm.value = true;
};

const editMember = (member: any, index: number) => {
  editingMember.value = member;
  editingIndex.value = index;
  loadMemberForm(member);
  showMemberForm.value = true;
};

const removeMember = (index: number) => {
  if (currentMembers.value.length > 1) {
    currentMembers.value.splice(index, 1);
  }
};

const resetMemberForm = () => {
  memberForm.name = '';
  memberForm.email = '';
  memberForm.phone = '';
  memberForm.age = null;
  memberForm.role = '';
  memberForm.notes = '';
  memberErrors.value = {};
};

const loadMemberForm = (member: any) => {
  memberForm.name = member.name || '';
  memberForm.email = member.email || '';
  memberForm.phone = member.phone || '';
  memberForm.age = member.age || null;
  memberForm.role = member.role || '';
  memberForm.notes = member.notes || '';
  memberErrors.value = {};
};

const validateMemberForm = (): boolean => {
  memberErrors.value = {};

  if (!memberForm.name.trim()) {
    memberErrors.value.name = 'El nombre es obligatorio';
  }

  if (memberForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(memberForm.email)) {
    memberErrors.value.email = 'Email inválido';
  }

  // Check for duplicate names (excluding current member being edited)
  const duplicateName = currentMembers.value.some((member, index) =>
    member.name.toLowerCase() === memberForm.name.toLowerCase() &&
    index !== editingIndex.value
  );

  if (duplicateName) {
    memberErrors.value.name = 'Ya existe un miembro con este nombre';
  }

  return Object.keys(memberErrors.value).length === 0;
};

const saveMember = async () => {
  if (!validateMemberForm()) {
    return;
  }

  try {
    memberSaving.value = true;
    error.value = null;

    const memberData: any = { ...memberForm };

    // Remove null/empty values
    Object.keys(memberData).forEach(key => {
      if (memberData[key] === null || memberData[key] === '') {
        delete memberData[key];
      }
    });

    if (editingMember.value) {
      // Update existing member
      currentMembers.value[editingIndex.value] = {
        ...editingMember.value,
        ...memberData
      };
    } else {
      // Add new member
      currentMembers.value.push({
        id: Date.now(), // Temporary ID
        ...memberData
      });
    }

    showMemberForm.value = false;
    resetMemberForm();
  } catch (err: any) {
    console.error('Error saving member:', err);
    error.value = err.message || 'Error al guardar el miembro';
  } finally {
    memberSaving.value = false;
  }
};

const cancelMemberForm = () => {
  showMemberForm.value = false;
  resetMemberForm();
};

const handleSave = async () => {
  try {
    saving.value = true;
    error.value = null;

    // Save all member changes
    for (const member of currentMembers.value) {
      if (member.id && typeof member.id === 'number' && member.id > 1000000000000) {
        // New member (temporary ID)
        await apiService.guide.addGroupMember(props.group.id, member);
      }
      // Note: Updates to existing members would be handled here if needed
    }

    emit('save');
  } catch (err: any) {
    console.error('Error saving members:', err);
    error.value = err.message || 'Error al guardar los miembros';
  } finally {
    saving.value = false;
  }
};

const loadGroupMembers = () => {
  if (props.group) {
    // Load existing members or create from group_members array
    if (props.group.members && props.group.members.length > 0) {
      currentMembers.value = [...props.group.members];
    } else if (props.group.group_members && props.group.group_members.length > 0) {
      // Convert string array to member objects
      currentMembers.value = props.group.group_members
        .filter((name: string) => name.trim())
        .map((name: string, index: number) => ({
          id: index + 1,
          name: name.trim(),
          role: index === 0 ? 'leader' : 'participant'
        }));
    } else {
      currentMembers.value = [];
    }
  }
};

// Watchers
watch(() => props.group, loadGroupMembers, { immediate: true });

// Lifecycle
onMounted(() => {
  loadGroupMembers();
});
</script>

<style scoped>
.group-members-manager {
  max-width: 600px;
  margin: 0 auto;
}

.manager-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.group-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.group-info h4 {
  margin: 0 0 0.5rem 0;
  color: #2c5aa0;
}

.group-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.current-members-section,
.group-stats {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.current-members-section h3,
.group-stats h3 {
  margin: 0 0 1rem 0;
  color: #2c5aa0;
  font-size: 1.125rem;
  font-weight: 600;
}

.no-members {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.no-members i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #dee2e6;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #2c5aa0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
}

.member-info {
  flex: 1;
}

.member-info h5 {
  margin: 0 0 0.25rem 0;
  color: #2c5aa0;
  font-size: 1rem;
}

.member-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.875rem;
}

.member-role {
  display: inline-block;
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

.member-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-btn {
  background: #f8f9fa;
  color: #2c5aa0;
}

.edit-btn:hover {
  background: #e9ecef;
}

.remove-btn {
  background: #f8f9fa;
  color: #e74c3c;
}

.remove-btn:hover:not(:disabled) {
  background: #f5c6cb;
}

.remove-btn:disabled {
  color: #dee2e6;
  cursor: not-allowed;
}

.add-member-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.add-member-button:hover:not(:disabled) {
  background: #218838;
}

.add-member-button:disabled {
  background: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
}

.member-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.member-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.member-form h3 {
  margin: 0 0 1.5rem 0;
  color: #2c5aa0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.required {
  color: #e74c3c;
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
}

.field-error {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-label {
  color: #6c757d;
  font-size: 0.875rem;
}

.stat-value {
  color: #2c5aa0;
  font-weight: 600;
  font-size: 1.125rem;
}

.form-actions,
.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.cancel-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button {
  background: #6c757d;
  color: white;
}

.cancel-button:hover {
  background: #5a6268;
}

.save-button {
  background: #2c5aa0;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .member-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .member-actions {
    align-self: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .form-actions,
  .actions {
    flex-direction: column;
  }
}
</style>
