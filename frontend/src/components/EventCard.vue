<template>
  <div class="event-card">
    <div class="event-image-container">
      <img :src="event.image" alt="Event" class="event-image" />
      <div class="event-date">
        <div class="event-month">{{ getMonth() }}</div>
        <div class="event-day">{{ getDay() }}</div>
      </div>
    </div>
    <div class="event-details">
      <h3 class="event-title">{{ event.title }}</h3>
      <p class="event-summary">{{ event.summary }}</p>
      <div class="event-footer">
        <div class="event-info">
          <div class="event-time">
            <i class="pi pi-clock"></i> {{ formatTime() }}
          </div>
          <div class="event-location">
            <i class="pi pi-map-marker"></i> {{ event.location }}
          </div>
        </div>
        <div class="event-rating">
          <span class="stars">{{ event.stars }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Event {
  id: number;
  title: string;
  summary: string;
  date: string;
  location: string;
  stars: number;
  image: string;
}

interface Props {
  event: Event;
}

const props = defineProps<Props>();

const getMonth = () => {
  const date = new Date(props.event.date);
  return date.toLocaleString('default', { month: 'short' });
};

const getDay = () => {
  const date = new Date(props.event.date);
  return date.getDate();
};

const formatTime = () => {
  const date = new Date(props.event.date);
  return date.toLocaleTimeString('default', { hour: '2-digit', minute: '2-digit' });
};
</script>

<style scoped>
.event-card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.event-image-container {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-date {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.event-month {
  font-size: 0.7rem;
  text-transform: uppercase;
  font-weight: 500;
}

.event-day {
  font-size: 1.2rem;
  font-weight: 700;
  color: #DC8960;
}

.event-details {
  padding: 0.75rem;
}

.event-title {
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #333;
}

.event-summary {
  font-size: 0.75rem;
  margin: 0 0 0.75rem 0;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.7rem;
  color: #777;
}

.event-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.event-time, .event-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.event-rating {
  font-weight: 600;
  color: #DC8960;
}

.stars {
  display: flex;
  align-items: center;
}

.stars::after {
  content: "★";
  margin-left: 0.25rem;
}
</style>
