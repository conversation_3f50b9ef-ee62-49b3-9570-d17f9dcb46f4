<template>
  <Teleport to="body">
    <div 
      v-if="visible" 
      class="toast-container"
      :class="{ 'toast-success': type === 'success', 'toast-error': type === 'error', 'toast-info': type === 'info' }"
    >
      <div class="toast-icon">
        <i v-if="type === 'success'" class="pi pi-check-circle"></i>
        <i v-else-if="type === 'error'" class="pi pi-exclamation-circle"></i>
        <i v-else class="pi pi-info-circle"></i>
      </div>
      <div class="toast-content">
        <div v-if="title" class="toast-title">{{ title }}</div>
        <div class="toast-message">{{ message }}</div>
      </div>
      <button class="toast-close" @click="close">
        <i class="pi pi-times"></i>
      </button>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info',
    validator: (value: string) => ['success', 'error', 'info'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    required: true
  },
  duration: {
    type: Number,
    default: 5000 // 5 seconds
  },
  autoClose: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['close']);

const close = () => {
  emit('close');
};

let timer: number | null = null;

const startTimer = () => {
  if (props.autoClose && props.duration > 0) {
    timer = window.setTimeout(() => {
      close();
    }, props.duration);
  }
};

const clearTimer = () => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
};

watch(() => props.visible, (newValue) => {
  if (newValue) {
    clearTimer();
    startTimer();
  } else {
    clearTimer();
  }
});

onMounted(() => {
  if (props.visible) {
    startTimer();
  }
});
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 350px;
  min-width: 300px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  padding: 16px;
  z-index: 9999;
  animation: slide-in 0.3s ease-out;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.toast-success {
  border-left: 4px solid #10b981;
}

.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-info {
  border-left: 4px solid #3b82f6;
}

.toast-icon {
  margin-right: 12px;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
}

.toast-success .toast-icon {
  color: #10b981;
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #1f2937;
}

.toast-message {
  color: #4b5563;
  font-size: 0.9rem;
}

.toast-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.toast-close:hover {
  color: #4b5563;
}

@media (max-width: 640px) {
  .toast-container {
    top: auto;
    bottom: 20px;
    left: 20px;
    right: 20px;
    max-width: none;
  }
}
</style>
