import { useAuthStore } from '../stores/auth';
import { authToken, isAuthenticated, currentUser, getCurrentUser } from '../services/auth';

/**
 * Initialize the auth store and fetch user data if a token exists
 */
export async function initializeAuth() {
  const authStore = useAuthStore();

  // If we have a token but no user data, fetch the user data
  if (authStore.token && !authStore.user) {
    console.log('Token exists but no user data, fetching user data...');
    try {
      await authStore.fetchUser();
    } catch (error) {
      console.error('Failed to fetch user data during initialization:', error);
    }
  }

  // Also initialize the auth service if needed
  if (authToken.value && !currentUser.value) {
    console.log('Auth token exists but no user data in auth service, fetching user data...');
    try {
      await getCurrentUser();
    } catch (error) {
      console.error('Failed to fetch user data for auth service during initialization:', error);
    }
  }

  // Log the current auth state
  console.log('Auth initialization complete:', {
    'Auth Store': {
      token: authStore.token ? 'exists' : 'none',
      user: authStore.user ? 'exists' : 'none',
      isAuthenticated: authStore.isAuthenticated
    },
    'Auth Service': {
      token: authToken.value ? 'exists' : 'none',
      user: currentUser.value ? 'exists' : 'none',
      isAuthenticated: isAuthenticated.value
    }
  });

  return authStore;
}
