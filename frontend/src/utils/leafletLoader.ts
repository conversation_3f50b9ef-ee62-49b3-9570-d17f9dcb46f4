// Import Leaflet
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Import MarkerCluster
import 'leaflet.markercluster';

// Define MarkerClusterGroup type
declare module 'leaflet' {
  export interface MarkerClusterGroupOptions {
    showCoverageOnHover?: boolean;
    zoomToBoundsOnClick?: boolean;
    spiderfyOnMaxZoom?: boolean;
    removeOutsideVisibleBounds?: boolean;
    animate?: boolean;
    maxClusterRadius?: number;
    disableClusteringAtZoom?: number;
    iconCreateFunction?: (cluster: any) => L.DivIcon;
  }

  export class MarkerClusterGroup extends L.FeatureGroup {
    constructor(options?: MarkerClusterGroupOptions);
    addLayer(layer: L.Layer): this;
    addLayers(layers: L.Layer[]): this;
    removeLayers(layers: L.Layer[]): this;
    clearLayers(): this;
  }

  export function markerClusterGroup(options?: MarkerClusterGroupOptions): MarkerClusterGroup;
}

// Fix for the default icon issue in Leaflet with webpack
// This is needed because Leaflet's default marker icons reference images from the library's directory structure
const fixLeafletIcon = () => {
  // Fix marker icons
  delete (L.Icon.Default.prototype as any)._getIconUrl;

  // Use the marker icons directly from the leaflet dist folder
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
    iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
  });

  console.log('Leaflet icon paths fixed');
};

// Initialize Leaflet
export const initLeaflet = () => {
  // Fix the icon issue
  fixLeafletIcon();

  // Return the Leaflet instance
  return L;
};

// Create a custom marker icon
export const createMarkerIcon = (color: string = '#22d3ee', size: number = 10) => {
  return L.divIcon({
    className: 'custom-map-marker',
    html: `<div style="background-color: ${color}; width: ${size}px; height: ${size}px; border-radius: 50%; border: 2px solid white;"></div>`,
    iconSize: [size, size],
    iconAnchor: [size/2, size/2]
  });
};

// Create a custom cluster icon
export const createClusterIcon = (cluster: any) => {
  const count = cluster.getChildCount();
  let size = 40;
  let color = '#334960';

  // Adjust size and color based on the number of markers in the cluster
  if (count < 10) {
    size = 40;
    color = '#4a6583';
  } else if (count < 50) {
    size = 50;
    color = '#334960';
  } else {
    size = 60;
    color = '#1e2a36';
  }

  return L.divIcon({
    html: `<div style="background-color: ${color}; color: white; width: ${size}px; height: ${size}px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: ${size/3}px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">${count}</div>`,
    className: 'marker-cluster-custom',
    iconSize: L.point(size, size),
    iconAnchor: L.point(size/2, size/2)
  });
};

// Create a marker cluster group
export const createMarkerClusterGroup = () => {
  return L.markerClusterGroup({
    showCoverageOnHover: false,
    zoomToBoundsOnClick: true,
    spiderfyOnMaxZoom: true,
    removeOutsideVisibleBounds: true,
    disableClusteringAtZoom: 16,
    maxClusterRadius: 60,
    animate: true,
    iconCreateFunction: createClusterIcon
  });
};

// Spain coordinates
export const SPAIN_CENTER = { lat: 40.4168, lng: -3.7038 };

// Default zoom level
export const DEFAULT_ZOOM = 6;
