declare module '@/services/api' {
  export function fetchFromApi(endpoint: string, options?: RequestInit): Promise<any>;

  export interface NewsItem {
    id: number;
    title: string;
    summary: string;
    datetime: string;
    location: string;
    image: string | null;
    created_at: string;
    updated_at: string;
  }

  export interface EventItem {
    id: number;
    title: string;
    description: string;
    start_datetime: string;
    end_datetime: string | null;
    location: string;
    image: string | null;
    rating: number | null;
    is_featured: boolean;
    created_at: string;
    updated_at: string;
  }

  export interface LocationItem {
    id: number;
    name: string;
    description: string;
    address: string;
    latitude: number;
    longitude: number;
    image: string | null;
    type: string;
    is_featured: boolean;
    created_at: string;
    updated_at: string;
  }

  export interface ExperienceItem {
    id: number;
    title: string;
    description: string;
    short_description: string | null;
    location_id: number | null;
    location?: {
      id: number;
      name: string;
    };
    agency_id: number | null;
    agency?: {
      id: number;
      name: string;
    };
    type: string;
    duration: string | null;
    distance: string | null;
    difficulty: string | null;
    price: number | null;
    image: string | null;
    start_date: string | null;
    end_date: string | null;
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }

  export interface StoryTourItem {
    id: number;
    title: string;
    description: string;
    image: string | null;
    type: string;
    territory: string | null;
    modality: string | null;
    has_crafts: boolean;
    has_artists: boolean;
    year: number | null;
    audio_file: string | null;
    video_file: string | null;
    ar_model_file: string | null;
    location_id: number | null;
    location?: {
      id: number;
      name: string;
      latitude: number;
      longitude: number;
    };
    is_featured: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }

  const apiService: {
    news: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
    };
    events: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
    };
    locations: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
      getByType: (type: string) => Promise<any>;
      getFeatured: () => Promise<any>;
    };
    experiences: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
      getByType: (type: string) => Promise<any>;
      getFeatured: () => Promise<any>;
      getActive: () => Promise<any>;
      getByAgency: (id: number | string) => Promise<any>;
    };
    accommodations: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
    },
    routes: {
      getAll: () => Promise<any>;
      getById: (id: string | string[]) => Promise<any>;
    },
    storyTours: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
      getByType: (type: string) => Promise<any>;
      getFilterOptions: () => Promise<any>;
      filter: (params: any) => Promise<any>;
      fixImageUrls: (storyTours: StoryTourItem[] | any) => StoryTourItem[];
    },
    agencies: {
      getAll: () => Promise<any>;
      getById: (id: number | string) => Promise<any>;
    }
  };

  export default apiService;
}
