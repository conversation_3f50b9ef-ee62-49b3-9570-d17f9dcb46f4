import './assets/main.scss'

// Import PrimeIcons CSS
import 'primeicons/primeicons.css'

// Import Leaflet CSS
import 'leaflet/dist/leaflet.css'
// Import MarkerCluster CSS
import 'leaflet.markercluster/dist/MarkerCluster.css'
import 'leaflet.markercluster/dist/MarkerCluster.Default.css'

import { createPinia } from 'pinia'
import { createApp } from 'vue'
import PrimeVue from 'primevue/config'

import App from './App.vue'
import router from './router'
import { initializeAuth } from './utils/auth-init'

// Create the app
const app = createApp(App)

// Create and use Pinia store
const pinia = createPinia()
app.use(pinia)

// Initialize auth store
initializeAuth().then(() => {
  console.log('Auth initialized')

  // Auth is now initialized
}).catch(error => {
  console.error('Auth initialization failed:', error)
})

// Use other plugins
app.use(router)
app.use(PrimeVue)

// Mount the app
app.mount('#app')
