<template>
  <div class="reservations-view">
    <!-- Header -->
    <AppHeader
      title="Mis Reservas"
      :showBackButton="true"
      @goBack="goBack"
    />

    <div class="reservations-container">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-container">
        <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
        <p>Cargando reservas...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-message">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="fetchUserReservations" class="retry-button">
          Intentar de nuevo
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="!userReservations.length" class="empty-state">
        <div class="empty-icon">
          <i class="pi pi-calendar-times"></i>
        </div>
        <h3>No tienes reservas</h3>
        <p>Explora nuestras experiencias y haz tu primera reserva.</p>
        <router-link to="/experiencias" class="explore-button">
          Explorar experiencias
        </router-link>
      </div>

      <!-- Reservations List -->
      <div v-else class="reservations-list">
        <h2 class="section-title">Tus reservas</h2>

        <!-- Filters -->
        <div class="filters">
          <div class="filter-group">
            <label for="status-filter">Estado:</label>
            <select id="status-filter" v-model="statusFilter" class="filter-select">
              <option value="all">Todos</option>
              <option value="pending">Pendientes</option>
              <option value="confirmed">Confirmadas</option>
              <option value="completed">Completadas</option>
              <option value="cancelled">Canceladas</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="sort-by">Ordenar por:</label>
            <select id="sort-by" v-model="sortBy" class="filter-select">
              <option value="date-asc">Fecha (más antigua)</option>
              <option value="date-desc">Fecha (más reciente)</option>
              <option value="status">Estado</option>
            </select>
          </div>
        </div>

        <!-- Use the ReservationsList component -->
        <ReservationsList
          :reservations="filteredReservations"
          :loading="false"
          @cancel-reservation="confirmCancelReservation"
          @view-experience="viewExperience"
        />
      </div>
    </div>

    <!-- Confirmation Dialog for Cancellation -->
    <Dialog
      v-model:visible="showCancelConfirmation"
      modal
      :showHeader="false"
      :style="{ width: '90%', maxWidth: '450px', backgroundColor: 'white' }"
      :closable="false"
      contentClass="dialog-content"
      class="cancel-dialog"
    >
      <div class="confirmation-dialog">
        <p class="confirmation-question">¿Estás seguro de que deseas cancelar esta reserva?</p>
        <p>Esta acción no se puede deshacer.</p>

        <div v-if="selectedReservation" class="reservation-details">
          <p><strong>Detalles de la reserva:</strong></p>
          <ul>
            <li>Experiencia: {{ selectedReservation.experience?.title }}</li>
            <li>Fecha: {{ formatDate(selectedReservation.reservation_date) }}</li>
            <li v-if="selectedReservation.reservation_time">Hora: {{ selectedReservation.reservation_time }}</li>
            <li>Personas: {{ selectedReservation.num_people }}</li>
          </ul>
        </div>

        <div class="dialog-actions">
          <Button
            label="No, mantener"
            class="p-button-text p-button-secondary"
            @click="showCancelConfirmation = false"
            style="font-weight: 500;"
          />
          <Button
            label="Sí, cancelar"
            class="p-button-danger"
            :loading="cancelling"
            @click="cancelReservation"
            style="font-weight: 500;"
          />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import AppHeader from '../components/layout/AppHeader.vue';
import ReservationsList from '../components/reservation/ReservationsList.vue';
import apiService from '../services/api';
import { isAuthenticated } from '../services/auth';
import type { ReservationItem } from '../services/api';

// Router
const router = useRouter();

// State
const isLoading = ref(true);
const error = ref('');
const userReservations = ref<ReservationItem[]>([]);
const showCancelConfirmation = ref(false);
const selectedReservation = ref<ReservationItem | null>(null);
const cancelling = ref(false);

// Filters
const statusFilter = ref('all');
const sortBy = ref('date-desc');

// Computed: Filtered and sorted reservations
const filteredReservations = computed(() => {
  let filtered = [...userReservations.value];

  // Apply status filter
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(r => r.status === statusFilter.value);
  }

  // Apply sorting
  filtered.sort((a, b) => {
    if (sortBy.value === 'date-asc') {
      return new Date(a.reservation_date).getTime() - new Date(b.reservation_date).getTime();
    } else if (sortBy.value === 'date-desc') {
      return new Date(b.reservation_date).getTime() - new Date(a.reservation_date).getTime();
    } else if (sortBy.value === 'status') {
      const statusOrder = { 'pending': 1, 'confirmed': 2, 'completed': 3, 'cancelled': 4 };
      return statusOrder[a.status as keyof typeof statusOrder] - statusOrder[b.status as keyof typeof statusOrder];
    }
    return 0;
  });

  return filtered;
});

// Go back function
const goBack = () => {
  router.back();
};

// Check if user is authenticated
onMounted(async () => {
  if (!isAuthenticated.value) {
    router.push('/login?redirect=/reservas');
    return;
  }

  try {
    isLoading.value = true;
    await fetchUserReservations();
  } catch (err) {
    console.error('Error loading reservations:', err);
    error.value = 'Error al cargar las reservas. Por favor, inténtelo de nuevo.';
  } finally {
    isLoading.value = false;
  }
});

// Fetch user reservations
const fetchUserReservations = async () => {
  try {
    const response = await apiService.reservations.getUserReservations();
    // Check if the response has a data property (API wrapper)
    if (response && response.data) {
      userReservations.value = response.data;
    } else if (Array.isArray(response)) {
      userReservations.value = response;
    } else {
      userReservations.value = [];
      console.warn('Unexpected response format:', response);
    }
    console.log('User reservations:', userReservations.value);
  } catch (err) {
    console.error('Error fetching reservations:', err);
    error.value = 'Error al cargar las reservas. Por favor, inténtelo de nuevo.';
    throw err;
  }
};

// Format date for display
const formatDate = (dateString: string) => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString;
  }
};

// View experience details
const viewExperience = (experienceId: number | undefined) => {
  if (!experienceId) {
    console.error('Experience ID is undefined');
    // Show an error message to the user
    error.value = 'No se pudo encontrar la experiencia. Por favor, inténtelo de nuevo.';
    return;
  }
  router.push(`/experiencias/${experienceId}`);
};

// Confirm cancel reservation
const confirmCancelReservation = (reservation: ReservationItem) => {
  selectedReservation.value = reservation;
  showCancelConfirmation.value = true;
};

// Cancel reservation
const cancelReservation = async () => {
  if (!selectedReservation.value) return;

  try {
    cancelling.value = true;
    await apiService.reservations.cancel(selectedReservation.value.id);

    // Update the reservation status in the local list
    const index = userReservations.value.findIndex(r => r.id === selectedReservation.value?.id);
    if (index !== -1) {
      userReservations.value[index].status = 'cancelled';
    }

    // Close dialog
    showCancelConfirmation.value = false;
  } catch (err) {
    console.error('Error cancelling reservation:', err);
    error.value = 'Error al cancelar la reserva. Por favor, inténtelo de nuevo.';
  } finally {
    cancelling.value = false;
  }
};
</script>

<style scoped>
.reservations-view {
  min-height: 100vh;
  background-color: #f5f8fa;
  display: flex;
  flex-direction: column;
}

.reservations-container {
  padding: 1rem;
  flex: 1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  height: 50vh;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.error-message i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.retry-button {
  background-color: #b91c1c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-top: 0.5rem;
  cursor: pointer;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  height: 50vh;
}

.empty-icon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.empty-state p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.explore-button {
  background-color: #DC8960;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.explore-button:hover {
  background-color: #c77a53;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #374151;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  background-color: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 150px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: white;
}

.reservation-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reservation-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.reservation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.reservation-card.cancelled {
  opacity: 0.7;
}

.reservation-header {
  padding: 1rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-confirmed {
  background-color: #dcfce7;
  color: #166534;
}

.status-completed {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #b91c1c;
}

.reservation-date {
  font-size: 0.875rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reservation-body {
  padding: 1rem;
}

.experience-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.reservation-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.detail-item i {
  color: #6b7280;
}

.booking-code {
  background-color: #f3f4f6;
  padding: 0.5rem;
  border-radius: 4px;
}

.special-requests {
  font-style: italic;
  color: #6b7280;
}

.reservation-actions {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.view-button {
  background-color: #f3f4f6;
  color: #4b5563;
}

.view-button:hover {
  background-color: #e5e7eb;
}

.cancel-button {
  background-color: #fee2e2;
  color: #b91c1c;
}

.cancel-button:hover {
  background-color: #fecaca;
}

.confirmation-dialog {
  padding: 1.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 1px rgba(0, 0, 0, 0.05);
  background-color: white;
}

.confirmation-question {
  font-size: 1.1rem;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

/* Override PrimeVue Dialog styles to ensure background is not transparent */
:deep(.p-dialog) {
  background-color: white !important;
}

:deep(.p-dialog-content) {
  background-color: white !important;
  padding: 0 !important;
}

:deep(.cancel-dialog .p-dialog-mask) {
  background-color: rgba(0, 0, 0, 0.75) !important;
}

:deep(.cancel-dialog .p-dialog) {
  background-color: white !important;
  border: none !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2), 0 0 1px rgba(0, 0, 0, 0.1) !important;
  padding: 0.5rem !important;
}

.dialog-content {
  background-color: white !important;
  border-radius: 12px;
  overflow: hidden;
  padding: 0 !important;
}

.confirmation-dialog .reservation-details {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.25rem;
  margin: 1.25rem 0;
  border: 1px solid #e5e7eb;
}

.confirmation-dialog .reservation-details ul {
  list-style: none;
  padding-left: 0.5rem;
  margin: 0.5rem 0;
}

.confirmation-dialog .reservation-details li {
  margin-bottom: 0.5rem;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

@media (max-width: 640px) {
  .reservation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .reservation-actions {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
    justify-content: center;
  }
}
</style>
