<template>
  <div class="guide-groups">
    <!-- Header -->
    <AppHeader
      title="Gestión de Grupos"
      :showBackButton="true"
      :showMenuButton="false"
      @goBack="router.back()"
    />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando grupos...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadGroups" class="retry-button">Reintentar</button>
      </div>

      <!-- Groups Content -->
      <div v-else class="groups-content">
        <!-- Header Actions -->
        <div class="header-actions">
          <div class="search-bar">
            <i class="pi pi-search"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar grupos..."
              class="search-input"
            />
          </div>

          <button @click="createNewGroup" class="create-button">
            <i class="pi pi-plus"></i>
            Nuevo Grupo
          </button>
        </div>

        <!-- Group Type Filters -->
        <div class="group-filters">
          <button
            v-for="type in groupTypes"
            :key="type.value"
            @click="selectedGroupType = type.value"
            :class="['filter-button', {
              active: selectedGroupType === type.value,
              inactive: selectedGroupType !== '' && selectedGroupType !== type.value
            }]"
          >
            <i :class="type.icon"></i>
            {{ type.label }}
          </button>
        </div>

        <!-- Groups List -->
        <div v-if="filteredGroups.length === 0" class="no-groups">
          <i class="pi pi-users"></i>
          <h3>No hay grupos</h3>
          <p>No se encontraron grupos que coincidan con los filtros seleccionados.</p>
          <button @click="createNewGroup" class="create-button">
            <i class="pi pi-plus"></i>
            Crear primer grupo
          </button>
        </div>

        <div v-else class="groups-list">
          <div
            v-for="group in filteredGroups"
            :key="group.id"
            class="group-card"
          >
            <div class="group-header">
              <div class="group-info">
                <h3 class="group-name">{{ group.name }}</h3>
                <div class="group-type" :class="group.type">
                  <i :class="getGroupTypeIcon(group.type)"></i>
                  {{ getGroupTypeLabel(group.type) }}
                </div>
              </div>

              <div class="group-actions">
                <button @click="editGroup(group)" class="action-button edit-btn">
                  <i class="pi pi-pencil"></i>
                </button>
                <button @click="manageMembers(group)" class="action-button members-btn">
                  <i class="pi pi-users"></i>
                </button>
                <button @click="deleteGroup(group)" class="action-button delete-btn">
                  <i class="pi pi-trash"></i>
                </button>
              </div>
            </div>

            <p v-if="group.description" class="group-description">{{ group.description }}</p>

            <div class="group-stats">
              <div class="stat-item">
                <i class="pi pi-users"></i>
                <span>{{ group.members?.length || 0 }} miembros</span>
              </div>

              <div class="stat-item">
                <i class="pi pi-calendar"></i>
                <span>{{ group.active_reservations || 0 }} reservas activas</span>
              </div>

              <div class="stat-item">
                <i class="pi pi-clock"></i>
                <span>Creado {{ formatDate(group.created_at) }}</span>
              </div>
            </div>

            <div v-if="group.members?.length" class="group-members">
              <h4>Miembros:</h4>
              <div class="members-list">
                <div
                  v-for="member in group.members.slice(0, 3)"
                  :key="member.id"
                  class="member-avatar"
                  :title="member.name"
                >
                  {{ getInitials(member.name) }}
                </div>
                <div v-if="group.members.length > 3" class="more-members">
                  +{{ group.members.length - 3 }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Group Edit Modal -->
    <Dialog
      v-model:visible="showEditModal"
      :header="editingGroup ? 'Editar Grupo' : 'Nuevo Grupo'"
      :modal="true"
      :closable="true"
      class="group-modal"
    >
      <GroupEditForm
        v-if="showEditModal"
        :group="editingGroup"
        :agency-config="agencyConfig"
        @save="handleGroupSave"
        @cancel="closeEditModal"
      />
    </Dialog>

    <!-- Members Management Modal -->
    <Dialog
      v-model:visible="showMembersModal"
      header="Gestionar Miembros"
      :modal="true"
      :closable="true"
      class="members-modal"
    >
      <GroupMembersManager
        v-if="showMembersModal"
        :group="selectedGroup"
        @save="handleMembersSave"
        @cancel="closeMembersModal"
      />
    </Dialog>

    <!-- Delete Confirmation Modal -->
    <Dialog
      v-model:visible="showDeleteModal"
      header="Confirmar Eliminación"
      :modal="true"
      :closable="true"
      class="delete-modal"
    >
      <div class="delete-confirmation">
        <i class="pi pi-exclamation-triangle warning-icon"></i>
        <h3>¿Estás seguro?</h3>
        <p>Esta acción eliminará permanentemente el grupo "{{ groupToDelete?.name }}" y no se puede deshacer.</p>

        <div class="delete-actions">
          <button @click="showDeleteModal = false" class="cancel-button">
            Cancelar
          </button>
          <button @click="confirmDelete" class="delete-button">
            Eliminar Grupo
          </button>
        </div>
      </div>
    </Dialog>

    <!-- Toast Notifications -->
    <Toast v-if="toast.show" :message="toast.message" :type="toast.type" @close="hideToast" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import Toast from '../components/ui/Toast.vue';
import Dialog from 'primevue/dialog';
import GroupEditForm from '../components/guide/GroupEditForm.vue';
import GroupMembersManager from '../components/guide/GroupMembersManager.vue';
import apiService from '../services/api';
import { currentUser, isAuthenticated } from '../services/auth';

const router = useRouter();

// Reactive state
const loading = ref(true);
const error = ref<string | null>(null);
const groups = ref<any[]>([]);
const agencyConfig = ref<any>(null);

// Search and filters
const searchQuery = ref('');
const selectedGroupType = ref('');

// Modals
const showEditModal = ref(false);
const showMembersModal = ref(false);
const showDeleteModal = ref(false);
const editingGroup = ref<any>(null);
const selectedGroup = ref<any>(null);
const groupToDelete = ref<any>(null);

// Toast state
const toast = ref({
  show: false,
  message: '',
  type: 'info' as 'success' | 'error' | 'warning' | 'info'
});

// Group types configuration
const groupTypes = ref([
  { value: '', label: 'Mostrar todos', icon: 'pi pi-list' },
  { value: 'normal', label: 'Grupo Normal', icon: 'pi pi-users' },
  { value: 'couple', label: 'Pareja', icon: 'pi pi-heart' },
  { value: 'family', label: 'Familia', icon: 'pi pi-home' },
  { value: 'mixed', label: 'Mixto', icon: 'pi pi-sitemap' },
  { value: 'pet', label: 'Con Mascota', icon: 'pi pi-star' }
]);

// Computed
const filteredGroups = computed(() => {
  let filtered = groups.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(group =>
      group.name.toLowerCase().includes(query) ||
      group.description?.toLowerCase().includes(query)
    );
  }

  if (selectedGroupType.value) {
    filtered = filtered.filter(group => group.type === selectedGroupType.value);
  }

  return filtered;
});

// Methods
const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  toast.value = { show: true, message, type };
};

const hideToast = () => {
  toast.value.show = false;
};

const getGroupTypeIcon = (type: string): string => {
  const icons: Record<string, string> = {
    'normal': 'pi pi-users',
    'couple': 'pi pi-heart',
    'family': 'pi pi-home',
    'mixed': 'pi pi-sitemap',
    'pet': 'pi pi-star'
  };
  return icons[type] || 'pi pi-users';
};

const getGroupTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    'normal': 'Grupo Normal',
    'couple': 'Pareja',
    'family': 'Familia',
    'mixed': 'Mixto',
    'pet': 'Con Mascota'
  };
  return labels[type] || type;
};

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'hoy';
  } else if (diffInDays === 1) {
    return 'ayer';
  } else if (diffInDays < 7) {
    return `hace ${diffInDays} días`;
  } else {
    return date.toLocaleDateString('es-ES');
  }
};

const loadGroups = async () => {
  try {
    loading.value = true;
    error.value = null;

    if (!isAuthenticated.value) {
      router.push('/home');
      return;
    }

    const [groupsResponse, configResponse] = await Promise.all([
      apiService.guide.getMyGroups(),
      apiService.guide.getGuideProfile()
    ]);

    groups.value = groupsResponse.data || [];
    agencyConfig.value = configResponse.data;

  } catch (err: any) {
    console.error('Error loading groups:', err);
    error.value = err.message || 'Error al cargar los grupos';
    showToast('Error al cargar los grupos', 'error');
  } finally {
    loading.value = false;
  }
};

const createNewGroup = () => {
  editingGroup.value = null;
  showEditModal.value = true;
};

const editGroup = (group: any) => {
  editingGroup.value = group;
  showEditModal.value = true;
};

const closeEditModal = () => {
  showEditModal.value = false;
  editingGroup.value = null;
};

const handleGroupSave = async (groupData: any) => {
  try {
    if (editingGroup.value) {
      await apiService.guide.updateGroup(editingGroup.value.id, groupData);
      showToast('Grupo actualizado correctamente', 'success');
    } else {
      await apiService.guide.createGroup(groupData);
      showToast('Grupo creado correctamente', 'success');
    }

    closeEditModal();
    await loadGroups();
  } catch (err: any) {
    console.error('Error saving group:', err);
    showToast('Error al guardar el grupo', 'error');
  }
};

const manageMembers = (group: any) => {
  selectedGroup.value = group;
  showMembersModal.value = true;
};

const closeMembersModal = () => {
  showMembersModal.value = false;
  selectedGroup.value = null;
};

const handleMembersSave = async () => {
  try {
    showToast('Miembros actualizados correctamente', 'success');
    closeMembersModal();
    await loadGroups();
  } catch (err: any) {
    console.error('Error updating members:', err);
    showToast('Error al actualizar los miembros', 'error');
  }
};

const deleteGroup = (group: any) => {
  groupToDelete.value = group;
  showDeleteModal.value = true;
};

const confirmDelete = async () => {
  try {
    if (groupToDelete.value) {
      await apiService.guide.deleteGroup(groupToDelete.value.id);
      showToast('Grupo eliminado correctamente', 'success');
      showDeleteModal.value = false;
      groupToDelete.value = null;
      await loadGroups();
    }
  } catch (err: any) {
    console.error('Error deleting group:', err);
    showToast('Error al eliminar el grupo', 'error');
  }
};

// Lifecycle
onMounted(() => {
  loadGroups();
});
</script>

<style scoped>
.guide-groups {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: 'Poppins', sans-serif;
}

.main-content {
  padding: 1rem;
  margin-top: 60px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container i,
.error-container i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-container i {
  color: #334960;
}

.error-container i {
  color: #e74c3c;
}

.retry-button {
  background-color: #334960;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #2a3d52;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.search-bar {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-bar i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  font-family: 'Poppins', sans-serif;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #334960;
  box-shadow: 0 0 0 3px rgba(51, 73, 96, 0.1);
}

.create-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #334960;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  white-space: nowrap;
  font-family: 'Poppins', sans-serif;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.create-button:hover {
  background: #2a3d52;
  transform: translateY(-1px);
}

.group-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.875rem;
  white-space: nowrap;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  opacity: 1;
}

.filter-button:hover {
  background: #f8f9fa;
  border-color: #334960;
  transform: translateY(-1px);
}

.filter-button.active {
  background: #334960;
  color: white;
  border-color: #334960;
  box-shadow: 0 2px 8px rgba(51, 73, 96, 0.2);
}

.filter-button.inactive {
  opacity: 0.6;
  background: #f8f9fa;
  color: #6c757d;
  border-color: #e9ecef;
}

.filter-button.inactive:hover {
  opacity: 0.8;
  background: #e9ecef;
  border-color: #334960;
}

.no-groups {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.no-groups i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #dee2e6;
}

.groups-list {
  display: grid;
  gap: 1.5rem;
}

.group-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f3f4;
  padding: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

.group-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(51, 73, 96, 0.15);
  border-color: #334960;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  background: rgba(248, 249, 250, 0.5);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.group-info {
  flex: 1;
}

.group-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #334960;
  font-family: 'Poppins', sans-serif;
}

.group-type {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  font-family: 'Poppins', sans-serif;
}

.group-type.normal {
  background: rgba(51, 73, 96, 0.1);
  color: #334960;
  border: 1px solid rgba(51, 73, 96, 0.2);
}

.group-type.couple {
  background: rgba(220, 137, 96, 0.1);
  color: #DC8960;
  border: 1px solid rgba(220, 137, 96, 0.2);
}

.group-type.pet {
  background: rgba(139, 69, 19, 0.1);
  color: #8B4513;
  border: 1px solid rgba(139, 69, 19, 0.2);
}

.group-type.mixed {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.group-type.family {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.group-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-btn {
  background: rgba(51, 73, 96, 0.1);
  color: #334960;
  border-color: rgba(51, 73, 96, 0.2);
}

.edit-btn:hover {
  background: #334960;
  color: white;
  transform: translateY(-1px);
}

.members-btn {
  background: rgba(220, 137, 96, 0.1);
  color: #DC8960;
  border-color: rgba(220, 137, 96, 0.2);
}

.members-btn:hover {
  background: #DC8960;
  color: white;
  transform: translateY(-1px);
}

.delete-btn {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.2);
}

.delete-btn:hover {
  background: #e74c3c;
  color: white;
  transform: translateY(-1px);
}

.group-description {
  color: #495057;
  margin: 0 0 1rem 0;
  line-height: 1.6;
  font-family: 'Poppins', sans-serif;
  background: rgba(248, 249, 250, 0.8);
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 3px solid #334960;
}

.group-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  background: rgba(248, 249, 250, 0.8);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
}

.stat-item i {
  color: #334960;
  font-size: 1rem;
}

.group-members {
  background: rgba(248, 249, 250, 0.8);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.group-members h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #334960;
  font-family: 'Poppins', sans-serif;
}

.members-list {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #334960;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.more-members {
  background: #DC8960;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
}

.delete-confirmation {
  text-align: center;
  padding: 1rem;
  font-family: 'Poppins', sans-serif;
}

.warning-icon {
  font-size: 3rem;
  color: #DC8960;
  margin-bottom: 1rem;
}

.delete-confirmation h3 {
  margin: 0 0 1rem 0;
  color: #334960;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
}

.delete-confirmation p {
  color: #495057;
  margin-bottom: 2rem;
  line-height: 1.6;
  font-family: 'Poppins', sans-serif;
}

.delete-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.cancel-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.cancel-button:hover {
  background: #5a6268;
}

.delete-button {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.delete-button:hover {
  background: #c0392b;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar {
    max-width: none;
  }

  .group-header {
    flex-direction: column;
    gap: 1rem;
  }

  .group-actions {
    align-self: flex-end;
  }

  .group-stats {
    flex-direction: column;
    gap: 0.75rem;
  }

  .delete-actions {
    flex-direction: column;
  }
}
</style>
