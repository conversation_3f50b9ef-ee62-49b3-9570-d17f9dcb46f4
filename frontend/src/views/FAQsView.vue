<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import AppHeader from '@/components/layout/AppHeader.vue'

const router = useRouter()

// No sidebar state needed

// Sample data for FAQs
const faqs = ref([
  {
    question: '¿Cuándo es la mejor época para visitar un alcornocal?',
    answer: 'La mejor época es de mayo a agosto, cuando se realiza la "saca del corcho" (extracción manual de la corteza del alcornoque).',
    isOpen: true
  },
  {
    question: '¿Es sostenible la extracción del corcho?',
    answer: 'Sí, es un proceso 100% sostenible, ya que el árbol no es talado, sino que su corteza se regenera cada 9-12 años. Además, el alcornocal es un ecosistema de gran valor ecológico.',
    isOpen: false
  },
  {
    question: '¿Qué actividades se pueden realizar en un tour corchero?',
    answer: 'Senderismo por alcornocales. Observación del proceso de extracción del corcho. Visitas a fábricas y talleres artesanales. Catas de vino con tapones de corcho natural. Experiencias gastronómicas con productos de la dehesa. Actividades de turismo rural y ecológico.',
    isOpen: false
  }
])

// Toggle FAQ open/closed state
const toggleFAQ = (index: number) => {
  faqs.value[index].isOpen = !faqs.value[index].isOpen
}
</script>

<template>
  <div class="faqs-view">
    <!-- No Sidebar Menu needed -->

    <!-- Header -->
    <AppHeader
      title="FAQS"
      :showBackButton="true"
      :showFavoriteButton="true"
      :showLogo="false"
      @goBack="router.back()"
    />

    <!-- FAQs Content -->
    <div class="faqs-content">
      <div v-for="(faq, index) in faqs" :key="index" class="faq-item">
        <div class="faq-question" @click="toggleFAQ(index)">
          <h3>{{ faq.question }}</h3>
          <i class="pi" :class="faq.isOpen ? 'pi-chevron-up' : 'pi-chevron-down'"></i>
        </div>
        <div class="faq-answer" v-if="faq.isOpen">
          <p>{{ faq.answer }}</p>
        </div>
      </div>
    </div>

    <!-- No browser bar as it's just part of the Figma mockup -->
  </div>
</template>

<style scoped>
.faqs-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* FAQs Content Styles */
.faqs-content {
  padding: 1.5rem 1rem;
  flex: 1;
}

.faq-item {
  margin-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  cursor: pointer;
}

.faq-question h3 {
  color: #334960;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.faq-question i {
  color: #334960;
  font-size: 1rem;
  margin-left: 1rem;
}

.faq-answer {
  padding: 0 0 1rem 0;
}

.faq-answer p {
  color: #334960;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

/* Browser bar styles removed as it's just part of the Figma mockup */
</style>
