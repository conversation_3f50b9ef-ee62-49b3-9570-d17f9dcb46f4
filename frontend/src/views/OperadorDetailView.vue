<template>
  <div class="operador-detail-view">
    <!-- Header with back button -->
    <AppHeader
      :title="operador ? operador.name : 'Detalles de Operador'"
      :showBackButton="true"
      :showShareButton="true"
      @goBack="goBack"
    />

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando información del operador...</p>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchOperador" class="retry-button">Reintentar</button>
    </div>

    <!-- Operador Details -->
    <div v-else-if="operador" class="operador-content">
      <!-- Logo and basic info -->
      <div class="operador-header">
        <div class="operador-logo-container">
          <img v-if="operador.logo" :src="operador.logo" :alt="operador.name" class="operador-logo" />
          <div v-else class="operador-logo-placeholder">
            {{ operador.name.charAt(0) }}
          </div>
        </div>
        <div class="operador-basic-info">
          <h1 class="operador-name">{{ operador.name }}</h1>
          <div v-if="operador.city || operador.country" class="operador-location">
            <i class="pi pi-map-marker"></i>
            <span>{{ operador.city }}{{ operador.city && operador.country ? ', ' : '' }}{{ operador.country }}</span>
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="operador-description-container">
        <h2 class="section-title">Acerca de</h2>
        <p class="operador-description">{{ operador.description || 'No hay descripción disponible.' }}</p>
      </div>

      <!-- Contact Information -->
      <div class="operador-contact-container">
        <h2 class="section-title">Información de Contacto</h2>

        <div class="contact-grid">
          <div v-if="operador.address" class="contact-item">
            <i class="pi pi-map-marker contact-icon"></i>
            <div class="contact-info">
              <span class="contact-label">Dirección</span>
              <span class="contact-value">{{ operador.address }}</span>
            </div>
          </div>

          <div v-if="operador.phone" class="contact-item">
            <i class="pi pi-phone contact-icon"></i>
            <div class="contact-info">
              <span class="contact-label">Teléfono</span>
              <a :href="`tel:${operador.phone}`" class="contact-value contact-link">{{ operador.phone }}</a>
            </div>
          </div>

          <div v-if="operador.email" class="contact-item">
            <i class="pi pi-envelope contact-icon"></i>
            <div class="contact-info">
              <span class="contact-label">Email</span>
              <a :href="`mailto:${operador.email}`" class="contact-value contact-link">{{ operador.email }}</a>
            </div>
          </div>

          <div v-if="operador.website" class="contact-item">
            <i class="pi pi-globe contact-icon"></i>
            <div class="contact-info">
              <span class="contact-label">Sitio Web</span>
              <a :href="operador.website" target="_blank" rel="noopener noreferrer" class="contact-value contact-link">{{ operador.website }}</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Operador Experiences -->
      <div v-if="operadorExperiences.length > 0" class="operador-experiences-container">
        <h2 class="section-title">Experiencias</h2>

        <div class="experiences-list">
          <div
            v-for="experience in operadorExperiences"
            :key="experience.id"
            class="experience-card"
            @click="viewExperienceDetails(experience)"
          >
            <div class="experience-image-container">
              <img :src="experience.image" :alt="experience.title" class="experience-image" />
              <div v-if="experience.distance" class="experience-distance">{{ experience.distance }}</div>
            </div>
            <div class="experience-details">
              <h3 class="experience-title">{{ experience.title }}</h3>
              <p class="experience-description">{{ experience.shortDescription }}</p>
              <div class="experience-info">
                <div v-if="experience.date" class="experience-date">
                  <i class="pi pi-calendar"></i>
                  <span>{{ experience.date }}</span>
                </div>
                <div v-if="experience.location" class="experience-location">
                  <i class="pi pi-map-marker"></i>
                  <span>{{ experience.location }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Buttons -->
      <div class="contact-buttons">
        <a v-if="operador.phone" :href="`tel:${operador.phone}`" class="contact-button phone-button">
          <i class="pi pi-phone"></i>
          <span>Llamar</span>
        </a>
        <a v-if="operador.email" :href="`mailto:${operador.email}`" class="contact-button email-button">
          <i class="pi pi-envelope"></i>
          <span>Email</span>
        </a>
        <a v-if="operador.website" :href="operador.website" target="_blank" rel="noopener noreferrer" class="contact-button website-button">
          <i class="pi pi-globe"></i>
          <span>Web</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const route = useRoute();
const router = useRouter();
const operadorId = computed(() => route.params.id as string);

const operador = ref<any>(null);
const experiences = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Function to fetch operador details from the API
const fetchOperador = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;

    // Use the API service to fetch operador details
    const data = await apiService.agencies.getById(operadorId.value);

    // Transform the API data
    operador.value = {
      id: data.data.id,
      name: data.data.name,
      description: data.data.description,
      logo: data.data.logo ? (data.data.logo.startsWith('/') ? data.data.logo : `/storage/${data.data.logo}`) : null,
      city: data.data.city,
      country: data.data.country,
      address: data.data.address,
      phone: data.data.phone,
      email: data.data.email,
      website: data.data.website
    };

    // Fetch experiences for this operador
    await fetchOperadorExperiences();
  } catch (err) {
    console.error('Error fetching operador details:', err);
    error.value = 'Error al cargar los detalles del operador. Por favor, inténtelo de nuevo más tarde.';

    // Fallback to sample data if API fails
    operador.value = {
      id: 1,
      name: 'Albatros Tours',
      description: 'Agencia especializada en turismo sostenible y experiencias en la naturaleza. Ofrecemos tours guiados, actividades al aire libre y experiencias únicas relacionadas con el corcho y el entorno natural de Extremadura.',
      logo: '/assets/images/agencies/albatrostours.png',
      city: 'Badajoz',
      country: 'España',
      address: 'Calle Principal 123, 06001 Badajoz',
      phone: '+34 924 123 456',
      email: '<EMAIL>',
      website: 'https://albatrostours.com'
    };

    // Fallback experiences
    experiences.value = [
      {
        id: 1,
        title: 'Alcornocales con ebike',
        shortDescription: 'El más grande alcornocal de la península ibérica y uno de los más importantes del mundo.',
        location: 'Castellar de la Frontera',
        date: '30/03/2025',
        distance: '12 km',
        image: '/assets/images/experiences/alcornocales.jpeg'
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Function to fetch experiences for this operador
const fetchOperadorExperiences = async (): Promise<void> => {
  try {
    // Use the API service to fetch experiences for this operador
    const data = await apiService.experiences.getByAgency(operadorId.value);

    // Transform the API data
    experiences.value = data.data.map((item: any) => ({
      id: item.id,
      title: item.title,
      shortDescription: item.short_description || '',
      location: item.location ? item.location.name : '',
      date: item.start_date ? formatDate(item.start_date) : '',
      distance: item.distance || '',
      image: item.image ? (item.image.startsWith('/') ? item.image : `/storage/${item.image}`) : '/assets/images/experiences/default.jpeg',
      type: item.type,
      difficulty: item.difficulty,
      price: item.price,
      duration: item.duration
    }));
  } catch (err) {
    console.error('Error fetching operador experiences:', err);
    // We already have fallback data in the main fetch function
  }
};

// Format date from API (YYYY-MM-DD) to DD/MM/YYYY
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
};

// Computed property for operador experiences
const operadorExperiences = computed(() => {
  return experiences.value;
});

// Fetch operador details when component is mounted
onMounted(() => {
  fetchOperador();
});

const goBack = () => {
  router.back();
};

const viewExperienceDetails = (experience: any) => {
  router.push(`/experiencias/${experience.id}`);
};
</script>

<style scoped>
.operador-detail-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.operador-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.agency-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.agency-logo-container {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 8px;
  overflow: hidden;
}

.agency-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.agency-logo-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  background-color: #334960;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
}

.agency-basic-info {
  flex: 1;
}

.agency-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.25rem;
}

.agency-location {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #666;
}

.agency-location i {
  margin-right: 0.25rem;
  color: #597694;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.operador-description-container,
.operador-contact-container,
.operador-experiences-container {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.operador-description {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #444;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.contact-icon {
  font-size: 1.2rem;
  color: #597694;
  margin-top: 0.25rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 0.8rem;
  color: #888;
  margin-bottom: 0.25rem;
}

.contact-value {
  font-size: 0.95rem;
  color: #444;
}

.contact-link {
  color: #597694;
  text-decoration: none;
}

.contact-link:hover {
  text-decoration: underline;
}

.experiences-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.experience-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.experience-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.experience-image-container {
  height: 140px;
  position: relative;
}

.experience-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.experience-distance {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(51, 73, 96, 0.8);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.experience-details {
  padding: 0.75rem;
}

.experience-title {
  font-size: 1rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.25rem;
}

.experience-description {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

.experience-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.8rem;
  color: #888;
}

.experience-date,
.experience-location {
  display: flex;
  align-items: center;
}

.experience-date i,
.experience-location i {
  margin-right: 0.25rem;
  font-size: 0.9rem;
}

.contact-buttons {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0 0.5rem;
}

.contact-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #334960;
  color: white;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.contact-button:hover {
  background-color: #263a4d;
}

.contact-button i {
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
}

.contact-button span {
  font-size: 0.85rem;
}

.phone-button {
  background-color: #4CAF50;
}

.phone-button:hover {
  background-color: #3e8e41;
}

.email-button {
  background-color: #2196F3;
}

.email-button:hover {
  background-color: #0b7dda;
}

.website-button {
  background-color: #9C27B0;
}

.website-button:hover {
  background-color: #7B1FA2;
}

/* Loading state styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #597694;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #597694;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #4a6380;
}

/* Responsive Styles */
@media (min-width: 640px) {
  .contact-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .experiences-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .experiences-list {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
