<template>
  <div class="guide-experiences">
    <!-- Header -->
    <AppHeader
      title="Gestión de Experiencias"
      :showBackButton="true"
      :showMenuButton="false"
      @goBack="router.back()"
    />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando experiencias...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadExperiences" class="retry-button">Reintentar</button>
      </div>

      <!-- Experiences Content -->
      <div v-else class="experiences-content">
        <!-- Search and Filters -->
        <div class="search-section">
          <div class="search-bar">
            <i class="pi pi-search"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar experiencias..."
              class="search-input"
            />
          </div>

          <div class="filters">
            <select v-model="selectedType" class="filter-select">
              <option value="">Todos los tipos</option>
              <option value="tour">Tours</option>
              <option value="restaurant">Restaurantes</option>
              <option value="hotel">Hoteles</option>
              <option value="activity">Actividades</option>
            </select>

            <select v-model="selectedStatus" class="filter-select">
              <option value="">Todos los estados</option>
              <option value="active">Activo</option>
              <option value="inactive">Inactivo</option>
              <option value="draft">Borrador</option>
            </select>
          </div>
        </div>

        <!-- Experiences List -->
        <div v-if="filteredExperiences.length === 0" class="no-experiences">
          <i class="pi pi-calendar"></i>
          <h3>No hay experiencias</h3>
          <p>No se encontraron experiencias que coincidan con los filtros seleccionados.</p>
        </div>

        <div v-else class="experiences-list">
          <div
            v-for="experience in filteredExperiences"
            :key="experience.id"
            class="experience-card"
            @click="editExperience(experience)"
          >
            <div class="experience-image">
              <img :src="experience.image || '/assets/images/experiences/default.jpeg'" :alt="experience.title" />
              <div class="experience-status" :class="experience.status">
                {{ getStatusLabel(experience.status) }}
              </div>
            </div>

            <div class="experience-content">
              <div class="experience-header">
                <h3 class="experience-title">{{ experience.title }}</h3>
                <div class="experience-type">{{ getTypeLabel(experience.type) }}</div>
              </div>

              <p class="experience-description">{{ experience.short_description || 'Sin descripción' }}</p>

              <div class="experience-details">
                <div class="detail-item">
                  <i class="pi pi-map-marker"></i>
                  <span>{{ experience.location?.name || 'Sin ubicación' }}</span>
                </div>

                <div class="detail-item">
                  <i class="pi pi-users"></i>
                  <span>Capacidad: {{ experience.capacity || 'Sin límite' }}</span>
                </div>

                <div class="detail-item">
                  <i class="pi pi-clock"></i>
                  <span>{{ experience.time_slots?.length || 0 }} horarios</span>
                </div>
              </div>

              <div class="experience-actions">
                <button
                  @click.stop="editExperience(experience)"
                  class="action-button edit-btn"
                >
                  <i class="pi pi-pencil"></i>
                  Editar
                </button>

                <button
                  @click.stop="manageTimeSlots(experience)"
                  class="action-button time-btn"
                >
                  <i class="pi pi-clock"></i>
                  Horarios
                </button>

                <button
                  v-if="experience.type === 'restaurant'"
                  @click.stop="manageRestaurant(experience)"
                  class="action-button restaurant-btn"
                >
                  <i class="pi pi-home"></i>
                  Restaurante
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Experience Edit Modal -->
    <Dialog
      v-model:visible="showEditModal"
      :header="editingExperience ? 'Editar Experiencia' : 'Nueva Experiencia'"
      :modal="true"
      :closable="true"
      class="experience-modal"
    >
      <ExperienceEditForm
        v-if="showEditModal"
        :experience="editingExperience"
        :agency-config="agencyConfig"
        @save="handleExperienceSave"
        @cancel="closeEditModal"
      />
    </Dialog>

    <!-- Time Slots Modal -->
    <Dialog
      v-model:visible="showTimeSlotsModal"
      header="Gestionar Horarios"
      :modal="true"
      :closable="true"
      class="time-slots-modal"
    >
      <TimeSlotsManager
        v-if="showTimeSlotsModal"
        :experience="selectedExperience"
        @save="handleTimeSlotsSave"
        @cancel="closeTimeSlotsModal"
      />
    </Dialog>

    <!-- Restaurant Assignment Modal -->
    <Dialog
      v-model:visible="showRestaurantModal"
      header="Asignar Restaurante"
      :modal="true"
      :closable="true"
      class="restaurant-modal"
    >
      <RestaurantAssignment
        v-if="showRestaurantModal"
        :experience="selectedExperience"
        @save="handleRestaurantSave"
        @cancel="closeRestaurantModal"
      />
    </Dialog>

    <!-- Toast Notifications -->
    <Toast v-if="toast.show" :message="toast.message" :type="toast.type" @close="hideToast" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import Toast from '../components/ui/Toast.vue';
import Dialog from 'primevue/dialog';
import ExperienceEditForm from '../components/guide/ExperienceEditForm.vue';
import TimeSlotsManager from '../components/guide/TimeSlotsManager.vue';
import RestaurantAssignment from '../components/guide/RestaurantAssignment.vue';
import apiService from '../services/api';
import { currentUser, isAuthenticated } from '../services/auth';

const router = useRouter();

// Reactive state
const loading = ref(true);
const error = ref<string | null>(null);
const experiences = ref<any[]>([]);
const agencyConfig = ref<any>(null);

// Search and filters
const searchQuery = ref('');
const selectedType = ref('');
const selectedStatus = ref('');

// Modals
const showEditModal = ref(false);
const showTimeSlotsModal = ref(false);
const showRestaurantModal = ref(false);
const editingExperience = ref<any>(null);
const selectedExperience = ref<any>(null);

// Toast state
const toast = ref({
  show: false,
  message: '',
  type: 'info' as 'success' | 'error' | 'warning' | 'info'
});

// Computed
const filteredExperiences = computed(() => {
  let filtered = experiences.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(exp =>
      exp.title.toLowerCase().includes(query) ||
      exp.short_description?.toLowerCase().includes(query) ||
      exp.location?.name?.toLowerCase().includes(query)
    );
  }

  if (selectedType.value) {
    filtered = filtered.filter(exp => exp.type === selectedType.value);
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(exp => exp.status === selectedStatus.value);
  }

  return filtered;
});

// Methods
const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  toast.value = { show: true, message, type };
};

const hideToast = () => {
  toast.value.show = false;
};

const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    'active': 'Activo',
    'inactive': 'Inactivo',
    'draft': 'Borrador',
    'pending': 'Pendiente'
  };
  return labels[status] || status;
};

const getTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    'tour': 'Tour',
    'restaurant': 'Restaurante',
    'hotel': 'Hotel',
    'activity': 'Actividad'
  };
  return labels[type] || type;
};

const loadExperiences = async () => {
  try {
    loading.value = true;
    error.value = null;

    if (!isAuthenticated.value) {
      router.push('/home');
      return;
    }

    const [experiencesResponse, configResponse] = await Promise.all([
      apiService.guide.getMyExperiences(),
      apiService.guide.getGuideProfile()
    ]);

    experiences.value = experiencesResponse.data || [];
    agencyConfig.value = configResponse.data;

  } catch (err: any) {
    console.error('Error loading experiences:', err);
    error.value = err.message || 'Error al cargar las experiencias';
    showToast('Error al cargar las experiencias', 'error');
  } finally {
    loading.value = false;
  }
};

const editExperience = (experience: any) => {
  editingExperience.value = experience;
  showEditModal.value = true;
};

const closeEditModal = () => {
  showEditModal.value = false;
  editingExperience.value = null;
};

const handleExperienceSave = async (experienceData: any) => {
  try {
    if (editingExperience.value) {
      await apiService.guide.updateExperience(editingExperience.value.id, experienceData);
      showToast('Experiencia actualizada correctamente', 'success');
    }

    closeEditModal();
    await loadExperiences();
  } catch (err: any) {
    console.error('Error saving experience:', err);
    showToast('Error al guardar la experiencia', 'error');
  }
};

const manageTimeSlots = (experience: any) => {
  selectedExperience.value = experience;
  showTimeSlotsModal.value = true;
};

const closeTimeSlotsModal = () => {
  showTimeSlotsModal.value = false;
  selectedExperience.value = null;
};

const handleTimeSlotsSave = async (timeSlotsData: any) => {
  try {
    await apiService.guide.updateExperienceTimeSlots(selectedExperience.value.id, timeSlotsData);
    showToast('Horarios actualizados correctamente', 'success');
    closeTimeSlotsModal();
    await loadExperiences();
  } catch (err: any) {
    console.error('Error saving time slots:', err);
    showToast('Error al guardar los horarios', 'error');
  }
};

const manageRestaurant = (experience: any) => {
  selectedExperience.value = experience;
  showRestaurantModal.value = true;
};

const closeRestaurantModal = () => {
  showRestaurantModal.value = false;
  selectedExperience.value = null;
};

const handleRestaurantSave = async (restaurantData: any) => {
  try {
    await apiService.guide.assignRestaurantToExperience(
      selectedExperience.value.id,
      restaurantData.restaurant_id
    );
    showToast('Restaurante asignado correctamente', 'success');
    closeRestaurantModal();
    await loadExperiences();
  } catch (err: any) {
    console.error('Error assigning restaurant:', err);
    showToast('Error al asignar el restaurante', 'error');
  }
};

// Lifecycle
onMounted(() => {
  loadExperiences();
});
</script>

<style scoped>
.guide-experiences {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.main-content {
  padding: 1rem;
  margin-top: 60px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container i,
.error-container i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-container i {
  color: #2c5aa0;
}

.error-container i {
  color: #e74c3c;
}

.retry-button {
  background-color: #2c5aa0;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

.search-section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.search-bar {
  position: relative;
  margin-bottom: 1rem;
}

.search-bar i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 1rem;
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  min-width: 150px;
}

.no-experiences {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.no-experiences i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #dee2e6;
}

.experiences-list {
  display: grid;
  gap: 1.5rem;
}

.experience-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.experience-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.experience-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.experience-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.experience-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.experience-status.active {
  background: #d4edda;
  color: #155724;
}

.experience-status.inactive {
  background: #f8d7da;
  color: #721c24;
}

.experience-status.draft {
  background: #fff3cd;
  color: #856404;
}

.experience-content {
  padding: 1.5rem;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.experience-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c5aa0;
  flex: 1;
}

.experience-type {
  background: #f8f9fa;
  color: #6c757d;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 1rem;
}

.experience-description {
  color: #6c757d;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.experience-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
}

.detail-item i {
  color: #2c5aa0;
  width: 16px;
}

.experience-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.edit-btn {
  background: #2c5aa0;
  color: white;
}

.edit-btn:hover {
  background: #1e3a8a;
}

.time-btn {
  background: #f39c12;
  color: white;
}

.time-btn:hover {
  background: #e67e22;
}

.restaurant-btn {
  background: #27ae60;
  color: white;
}

.restaurant-btn:hover {
  background: #229954;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }

  .filter-select {
    min-width: auto;
  }

  .experience-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .experience-type {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .experience-actions {
    justify-content: center;
  }
}
</style>
