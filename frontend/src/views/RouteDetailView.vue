<template>
<!--  <div class="route-detail-view">-->
<!--    &lt;!&ndash; Header &ndash;&gt;-->
<!--    <AppHeader-->
<!--      :title="route?.title || 'Detalle de Ruta'"-->
<!--      :showBackButton="true"-->
<!--      :showFavoriteButton="true"-->
<!--      :showLogo="false"-->
<!--      @goBack="router.back()"-->
<!--    />-->

<!--    &lt;!&ndash; Loading State &ndash;&gt;-->
<!--    <div v-if="isLoading" class="loading-container">-->
<!--      <i class="pi pi-spin pi-spinner loading-icon"></i>-->
<!--      <p>Cargando información de la ruta...</p>-->
<!--    </div>-->

<!--    &lt;!&ndash; Error State &ndash;&gt;-->
<!--    <div v-else-if="error" class="error-container">-->
<!--      <i class="pi pi-exclamation-triangle error-icon"></i>-->
<!--      <p>{{ error }}</p>-->
<!--      <button @click="fetchRouteDetail" class="retry-button">Reintentar</button>-->
<!--    </div>-->

<!--    &lt;!&ndash; Route Content &ndash;&gt;-->
<!--    <div v-else-if="route" class="route-content">-->
<!--      &lt;!&ndash; Route Image &ndash;&gt;-->
<!--      <div class="route-image-container">-->
<!--        <img-->
<!--          :src="route.image || '/assets/images/placeholder.jpg'"-->
<!--          :alt="route.title"-->
<!--          class="route-image"-->
<!--        >-->
<!--        <div v-if="route.is_featured" class="featured-badge">-->
<!--          <i class="pi pi-star"></i>-->
<!--        </div>-->
<!--      </div>-->

<!--      &lt;!&ndash; Route Info &ndash;&gt;-->
<!--      <div class="route-info">-->
<!--        <h1 class="route-title">{{ route.title }}</h1>-->

<!--        &lt;!&ndash; Route Details &ndash;&gt;-->
<!--        <div class="route-details">-->
<!--          <div v-if="route.duration" class="route-detail">-->
<!--            <i class="pi pi-clock"></i>-->
<!--            <span>{{ route.duration }}</span>-->
<!--          </div>-->
<!--          <div v-if="route.distance" class="route-detail">-->
<!--            <i class="pi pi-map-marker"></i>-->
<!--            <span>{{ route.distance }}</span>-->
<!--          </div>-->
<!--          <div v-if="route.difficulty" class="route-detail">-->
<!--            <i class="pi pi-chart-line"></i>-->
<!--            <span>{{ route.difficulty }}</span>-->
<!--          </div>-->
<!--        </div>-->

<!--        &lt;!&ndash; Agency Info if available &ndash;&gt;-->
<!--        <div v-if="route.agency" class="agency-info">-->
<!--          <img-->
<!--            v-if="route.agency.logo"-->
<!--            :src="route.agency.logo"-->
<!--            :alt="route.agency.name"-->
<!--            class="agency-logo"-->
<!--          >-->
<!--          <span class="agency-name">{{ route.agency.name }}</span>-->
<!--        </div>-->

<!--        &lt;!&ndash; Route Description &ndash;&gt;-->
<!--        <div class="route-description">-->
<!--          <h2 class="section-title">Descripción</h2>-->
<!--          <p>{{ route.description }}</p>-->
<!--        </div>-->

<!--        &lt;!&ndash; Route Map &ndash;&gt;-->
<!--        <div class="route-map">-->
<!--          <h2 class="section-title">Mapa de la Ruta</h2>-->
<!--          <div class="map-container">-->
<!--            <img src="/assets/images/map/map.png" alt="Mapa de la ruta" class="map-placeholder">-->
<!--            <div class="map-overlay">-->
<!--              <p>Mapa interactivo próximamente</p>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

<!--        &lt;!&ndash; Route Points &ndash;&gt;-->
<!--        <div v-if="route.points && route.points.length > 0" class="route-points">-->
<!--          <h2 class="section-title">Puntos de la Ruta</h2>-->
<!--          <div class="points-list">-->
<!--            <div-->
<!--              v-for="(point, index) in route.points"-->
<!--              :key="point.id"-->
<!--              class="point-item"-->
<!--            >-->
<!--              <div class="point-number">{{ index + 1 }}</div>-->
<!--              <div class="point-content">-->
<!--                <div v-if="point.image" class="point-image-container">-->
<!--                  <img :src="point.image" :alt="point.location?.name || 'Punto de ruta'" class="point-image">-->
<!--                </div>-->
<!--                <div class="point-info">-->
<!--                  <h3 class="point-title">{{ point.location?.name || 'Punto ' + (index + 1) }}</h3>-->
<!--                  <p v-if="point.description" class="point-description">{{ point.description }}</p>-->
<!--                  <div v-if="point.location" class="point-location">-->
<!--                    <i class="pi pi-map-marker"></i>-->
<!--                    <span>{{ point.location.address }}</span>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const route = useRoute();
const router = useRouter();
const routeData = ref<any>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Fetch route detail from API
const fetchRouteDetail = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    const routeId = route.params.id;
    const response = await apiService.routes.getById(routeId);
    routeData.value = response.data;
  } catch (err) {
    console.error('Error fetching route detail:', err);
    error.value = 'No se pudo cargar la información de la ruta. Por favor, inténtalo de nuevo.';
  } finally {
    isLoading.value = false;
  }
};

// Fetch route detail on component mount
onMounted(() => {
  fetchRouteDetail();
});
</script>

<style scoped>
.route-detail-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #334960;
  height: 50vh;
}

.loading-icon,
.error-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #DC8960;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #DC8960;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.route-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.route-image-container {
  position: relative;
  height: 200px;
  width: 100%;
}

.route-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #DC8960;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.route-info {
  padding: 1.5rem;
}

.route-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 1rem;
  font-family: 'Poppins', sans-serif;
}

.route-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.route-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f0f0f0;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.route-detail i {
  color: #DC8960;
}

.agency-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.agency-logo {
  width: 2rem;
  height: 2rem;
  object-fit: contain;
}

.agency-name {
  font-weight: 500;
  color: #334960;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.75rem;
  font-family: 'Poppins', sans-serif;
}

.route-description {
  margin-bottom: 1.5rem;
}

.route-description p {
  line-height: 1.6;
  color: #555;
}

.map-container {
  position: relative;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
}

.points-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.point-item {
  display: flex;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.point-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  background-color: #DC8960;
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
}

.point-content {
  flex: 1;
  display: flex;
  padding: 0.75rem;
  gap: 0.75rem;
}

.point-image-container {
  width: 4rem;
  height: 4rem;
}

.point-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.point-info {
  flex: 1;
}

.point-title {
  font-size: 1rem;
  font-weight: 500;
  color: #334960;
  margin-bottom: 0.25rem;
}

.point-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.point-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #777;
}

.point-location i {
  color: #DC8960;
}

@media (min-width: 768px) {
  .route-image-container {
    height: 300px;
  }

  .map-container {
    height: 300px;
  }
}
</style>
