<template>
  <div class="operadores-view">
    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Operadores"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Filter Tabs for Operator Types -->
    <div class="filter-tabs">
      <button
        v-for="type in operatorTypes"
        :key="type.key"
        :class="['filter-tab', { active: activeFilter === type.key }]"
        @click="setActiveFilter(type.key)"
      >
        {{ type.label }}
      </button>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando operadores...</p>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchOperadores" class="retry-button">Reintentar</button>
    </div>

    <!-- Operadores List -->
    <div v-else class="operadores-list">
      <div v-if="filteredOperadores.length === 0" class="no-results">
        No se encontraron operadores para "{{ operatorTypes.find(t => t.key === activeFilter)?.label }}"
      </div>
      <div v-else class="operadores-grid">
        <div
          v-for="operador in filteredOperadores"
          :key="operador.id"
          class="operador-card"
          @click="viewOperadorDetails(operador)"
        >
          <div class="operador-logo-container">
            <img v-if="operador.logo" :src="operador.logo" :alt="operador.name" class="operador-logo" />
            <div v-else class="operador-logo-placeholder">
              {{ operador.name.charAt(0) }}
            </div>
          </div>
          <div class="operador-details">
            <div class="operador-type-badge">{{ getOperatorTypeLabel(operador.type) }}</div>
            <h3 class="operador-name">{{ operador.name }}</h3>
            <p v-if="operador.description" class="operador-description">{{ operador.description }}</p>
            <div class="operador-location" v-if="operador.city || operador.country">
              <i class="pi pi-map-marker location-icon"></i>
              <span>{{ operador.city }}{{ operador.city && operador.country ? ', ' : '' }}{{ operador.country }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const router = useRouter();
const isSidebarOpen = ref(false);
const operadores = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const activeFilter = ref('todos');

// Operator types as specified in requirements
const operatorTypes = [
  { key: 'todos', label: 'Todos' },
  { key: 'agencia', label: 'Agencias' },
  { key: 'institucion', label: 'Instituciones' },
  { key: 'entidad', label: 'Entidades' }
];

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

const setActiveFilter = (filterKey: string) => {
  activeFilter.value = filterKey;
};

const getOperatorTypeLabel = (type: string): string => {
  const operatorType = operatorTypes.find(t => t.key === type);
  return operatorType ? operatorType.label : 'Operador';
};

// Function to fetch operadores from the API
const fetchOperadores = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;

    // Use the API service to fetch agencies (will be updated to operadores endpoint)
    const data = await apiService.agencies.getAll();

    // Transform the API data to match our frontend structure
    operadores.value = data.data.map((item: any) => ({
      id: item.id,
      name: item.name,
      description: item.short_description || item.description,
      logo: item.logo ? (item.logo.startsWith('/') ? item.logo : `/storage/${item.logo}`) : null,
      city: item.city,
      country: item.country,
      address: item.address,
      phone: item.phone,
      email: item.email,
      website: item.website,
      type: item.type || 'agencia' // Default to agencia for existing data
    }));
  } catch (err) {
    console.error('Error fetching operadores:', err);
    error.value = 'Error al cargar operadores. Por favor, inténtelo de nuevo más tarde.';

    // Fallback to sample data if API fails
    operadores.value = [
      {
        id: 1,
        name: 'Albatros Tours',
        description: 'Agencia especializada en turismo sostenible y experiencias en la naturaleza.',
        logo: '/assets/images/agencies/albatrostours.png',
        city: 'Badajoz',
        country: 'España',
        type: 'agencia'
      },
      {
        id: 2,
        name: 'Cork Experience',
        description: 'La agencia oficial de Cork Experience, ofreciendo las mejores experiencias relacionadas con el corcho.',
        logo: '/assets/logos/CorkExpLogoBlack.png',
        city: 'Mérida',
        country: 'España',
        type: 'agencia'
      },
      {
        id: 3,
        name: 'Instituto del Corcho',
        description: 'Institución dedicada a la investigación y promoción del sector corchero.',
        logo: null,
        city: 'Cáceres',
        country: 'España',
        type: 'institucion'
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Fetch operadores when component is mounted
onMounted(() => {
  fetchOperadores();
});

// Computed property for filtered operadores based on active filter
const filteredOperadores = computed(() => {
  if (activeFilter.value === 'todos') return operadores.value;

  return operadores.value.filter(operador => operador.type === activeFilter.value);
});

const viewOperadorDetails = (operador: any) => {
  router.push(`/operadores/${operador.id}`);
};
</script>

<style scoped>
.operadores-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem;
}

.filter-tabs {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  overflow-x: auto;
}

.filter-tab {
  flex: 1;
  padding: 1rem;
  border: none;
  background: none;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
}

.filter-tab.active {
  color: #334960;
  border-bottom-color: #334960;
  background-color: #f8f9fa;
}

.filter-tab:hover {
  background-color: #f8f9fa;
  color: #334960;
}

.operadores-list {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.operadores-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.operador-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.operador-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.operador-logo-container {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  padding: 1rem;
}

.operador-logo {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.operador-logo-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #334960;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
}

.operador-details {
  padding: 1rem;
  position: relative;
}

.operador-type-badge {
  position: absolute;
  top: -0.5rem;
  right: 1rem;
  background-color: #597694;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.operador-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
}

.operador-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

.operador-location {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
}

.location-icon {
  margin-right: 0.25rem;
  color: #597694;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Loading and error states */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #597694;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #597694;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #4a6380;
}

/* Responsive Styles */
@media (min-width: 640px) {
  .operadores-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .operadores-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
