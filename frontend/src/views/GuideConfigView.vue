<template>
  <div class="guide-config">
    <!-- Header -->
    <AppHeader
      title="Configuración de Agencia"
      :showBackButton="true"
      :showMenuButton="false"
      @goBack="router.back()"
    />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando configuración...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadConfig" class="retry-button">Reintentar</button>
      </div>

      <!-- Configuration Content -->
      <div v-else class="config-content">
        <form @submit.prevent="handleSubmit" class="config-form">
          <!-- Agency Branding Section -->
          <div class="config-section">
            <h3>
              <i class="pi pi-palette"></i>
              Marca y Apariencia
            </h3>

            <div class="form-group">
              <label for="agency_name">Nombre de la Agencia</label>
              <input
                id="agency_name"
                v-model="form.name"
                type="text"
                class="form-input"
                :class="{ error: errors.name }"
                placeholder="Nombre de tu agencia"
              />
              <span v-if="errors.name" class="field-error">{{ errors.name }}</span>
            </div>

            <div class="form-group">
              <label for="welcome_message">Mensaje de Bienvenida</label>
              <textarea
                id="welcome_message"
                v-model="form.welcome_message"
                class="form-textarea"
                rows="3"
                placeholder="Mensaje personalizado para el panel de guía..."
              ></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="primary_color">Color Primario</label>
                <div class="color-input-group">
                  <input
                    id="primary_color"
                    v-model="form.primary_color"
                    type="color"
                    class="color-input"
                  />
                  <input
                    v-model="form.primary_color"
                    type="text"
                    class="form-input color-text"
                    placeholder="#2c5aa0"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="secondary_color">Color Secundario</label>
                <div class="color-input-group">
                  <input
                    id="secondary_color"
                    v-model="form.secondary_color"
                    type="color"
                    class="color-input"
                  />
                  <input
                    v-model="form.secondary_color"
                    type="text"
                    class="form-input color-text"
                    placeholder="#f39c12"
                  />
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="logo_url">URL del Logo</label>
              <input
                id="logo_url"
                v-model="form.logo"
                type="url"
                class="form-input"
                placeholder="https://ejemplo.com/logo.png"
              />
              <small class="form-help">URL completa del logo de tu agencia</small>
            </div>
          </div>

          <!-- Contact Information Section -->
          <div class="config-section">
            <h3>
              <i class="pi pi-phone"></i>
              Información de Contacto
            </h3>

            <div class="form-row">
              <div class="form-group">
                <label for="contact_email">Email de Contacto</label>
                <input
                  id="contact_email"
                  v-model="form.contact_email"
                  type="email"
                  class="form-input"
                  placeholder="<EMAIL>"
                />
              </div>

              <div class="form-group">
                <label for="contact_phone">Teléfono de Contacto</label>
                <input
                  id="contact_phone"
                  v-model="form.contact_phone"
                  type="tel"
                  class="form-input"
                  placeholder="+34 123 456 789"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="website">Sitio Web</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="form-input"
                placeholder="https://www.tuagencia.com"
              />
            </div>

            <div class="form-group">
              <label for="address">Dirección</label>
              <textarea
                id="address"
                v-model="form.address"
                class="form-textarea"
                rows="2"
                placeholder="Dirección completa de la agencia..."
              ></textarea>
            </div>
          </div>

          <!-- Feature Settings Section -->
          <div class="config-section">
            <h3>
              <i class="pi pi-cog"></i>
              Configuración de Funciones
            </h3>

            <div class="feature-toggles">
              <div class="toggle-item">
                <label class="toggle-label">
                  <input
                    v-model="form.allow_group_creation"
                    type="checkbox"
                    class="toggle-input"
                  />
                  <span class="toggle-slider"></span>
                  <span class="toggle-text">Permitir creación de grupos</span>
                </label>
                <small class="toggle-help">Los guías pueden crear nuevos grupos</small>
              </div>

              <div class="toggle-item">
                <label class="toggle-label">
                  <input
                    v-model="form.allow_experience_editing"
                    type="checkbox"
                    class="toggle-input"
                  />
                  <span class="toggle-slider"></span>
                  <span class="toggle-text">Permitir edición de experiencias</span>
                </label>
                <small class="toggle-help">Los guías pueden modificar experiencias</small>
              </div>

              <div class="toggle-item">
                <label class="toggle-label">
                  <input
                    v-model="form.require_approval"
                    type="checkbox"
                    class="toggle-input"
                  />
                  <span class="toggle-slider"></span>
                  <span class="toggle-text">Requerir aprobación para cambios</span>
                </label>
                <small class="toggle-help">Los cambios necesitan aprobación del administrador</small>
              </div>

              <div class="toggle-item">
                <label class="toggle-label">
                  <input
                    v-model="form.enable_notifications"
                    type="checkbox"
                    class="toggle-input"
                  />
                  <span class="toggle-slider"></span>
                  <span class="toggle-text">Habilitar notificaciones</span>
                </label>
                <small class="toggle-help">Recibir notificaciones por email</small>
              </div>
            </div>
          </div>

          <!-- Language and Localization Section -->
          <div class="config-section">
            <h3>
              <i class="pi pi-globe"></i>
              Idioma y Localización
            </h3>

            <div class="form-row">
              <div class="form-group">
                <label for="default_language">Idioma por Defecto</label>
                <select
                  id="default_language"
                  v-model="form.default_language"
                  class="form-select"
                >
                  <option value="es">Español</option>
                  <option value="en">Inglés</option>
                  <option value="fr">Francés</option>
                  <option value="de">Alemán</option>
                  <option value="pt">Portugués</option>
                </select>
              </div>

              <div class="form-group">
                <label for="timezone">Zona Horaria</label>
                <select
                  id="timezone"
                  v-model="form.timezone"
                  class="form-select"
                >
                  <option value="Europe/Madrid">Madrid (CET)</option>
                  <option value="Europe/London">Londres (GMT)</option>
                  <option value="Europe/Paris">París (CET)</option>
                  <option value="Europe/Berlin">Berlín (CET)</option>
                  <option value="Europe/Lisbon">Lisboa (WET)</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="currency">Moneda</label>
              <select
                id="currency"
                v-model="form.currency"
                class="form-select"
              >
                <option value="EUR">Euro (€)</option>
                <option value="USD">Dólar Americano ($)</option>
                <option value="GBP">Libra Esterlina (£)</option>
                <option value="CHF">Franco Suizo (CHF)</option>
              </select>
            </div>
          </div>

          <!-- Preview Section -->
          <div class="config-section">
            <h3>
              <i class="pi pi-eye"></i>
              Vista Previa
            </h3>

            <div class="preview-container">
              <div
                class="preview-header"
                :style="{
                  backgroundColor: form.primary_color || '#2c5aa0',
                  color: 'white'
                }"
              >
                <img
                  v-if="form.logo"
                  :src="form.logo"
                  :alt="form.name"
                  class="preview-logo"
                  @error="handleLogoError"
                />
                <h4>{{ form.name || 'Tu Agencia' }}</h4>
              </div>
              <div class="preview-content">
                <p>{{ form.welcome_message || 'Mensaje de bienvenida personalizado' }}</p>
                <button
                  class="preview-button"
                  :style="{ backgroundColor: form.secondary_color || '#f39c12' }"
                >
                  Botón de Ejemplo
                </button>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <button type="button" @click="resetForm" class="reset-button">
              <i class="pi pi-refresh"></i>
              Restablecer
            </button>
            <button type="submit" :disabled="saving" class="save-button">
              <i v-if="saving" class="pi pi-spin pi-spinner"></i>
              <i v-else class="pi pi-check"></i>
              {{ saving ? 'Guardando...' : 'Guardar Configuración' }}
            </button>
          </div>
        </form>
      </div>
    </main>

    <!-- Toast Notifications -->
    <Toast v-if="toast.show" :message="toast.message" :type="toast.type" @close="hideToast" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import Toast from '../components/ui/Toast.vue';
import apiService from '../services/api';
import { currentUser, isAuthenticated } from '../services/auth';

const router = useRouter();

// Reactive state
const loading = ref(true);
const saving = ref(false);
const error = ref<string | null>(null);
const errors = ref<Record<string, string>>({});

// Toast state
const toast = ref({
  show: false,
  message: '',
  type: 'info' as 'success' | 'error' | 'warning' | 'info'
});

// Form data
const form = reactive({
  name: '',
  welcome_message: '',
  primary_color: '#2c5aa0',
  secondary_color: '#f39c12',
  logo: '',
  contact_email: '',
  contact_phone: '',
  website: '',
  address: '',
  allow_group_creation: true,
  allow_experience_editing: true,
  require_approval: false,
  enable_notifications: true,
  default_language: 'es',
  timezone: 'Europe/Madrid',
  currency: 'EUR'
});

// Store original form data for reset
const originalForm = ref<any>({});

// Methods
const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  toast.value = { show: true, message, type };
};

const hideToast = () => {
  toast.value.show = false;
};

const handleLogoError = () => {
  showToast('Error al cargar el logo. Verifica la URL.', 'warning');
};

const validateForm = (): boolean => {
  errors.value = {};

  if (!form.name.trim()) {
    errors.value.name = 'El nombre de la agencia es obligatorio';
  }

  if (form.contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.contact_email)) {
    errors.value.contact_email = 'Email inválido';
  }

  if (form.website && !/^https?:\/\/.+/.test(form.website)) {
    errors.value.website = 'URL del sitio web inválida';
  }

  if (form.logo && !/^https?:\/\/.+/.test(form.logo)) {
    errors.value.logo = 'URL del logo inválida';
  }

  return Object.keys(errors.value).length === 0;
};

const loadConfig = async () => {
  try {
    loading.value = true;
    error.value = null;

    if (!isAuthenticated.value) {
      router.push('/home');
      return;
    }

    const response = await apiService.guide.getGuideProfile();
    const config = response.data;

    // Load configuration into form
    Object.keys(form).forEach(key => {
      if ((config as any)[key] !== undefined) {
        (form as any)[key] = (config as any)[key];
      }
    });

    // Store original form data
    originalForm.value = { ...form };

  } catch (err: any) {
    console.error('Error loading config:', err);
    error.value = err.message || 'Error al cargar la configuración';
    showToast('Error al cargar la configuración', 'error');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  if (!validateForm()) {
    showToast('Por favor, corrige los errores en el formulario', 'error');
    return;
  }

  try {
    saving.value = true;
    error.value = null;

    // Note: Guides can only view profile, not update agency configuration
    // This would require a separate guide profile update endpoint
    showToast('Vista de perfil actualizada (solo lectura para guías)', 'info');

    // Update original form data
    originalForm.value = { ...form };

  } catch (err: any) {
    console.error('Error saving config:', err);
    error.value = err.message || 'Error al guardar la configuración';
    showToast('Error al guardar la configuración', 'error');
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  Object.keys(originalForm.value).forEach(key => {
    (form as any)[key] = originalForm.value[key];
  });
  errors.value = {};
  showToast('Formulario restablecido', 'info');
};

// Lifecycle
onMounted(() => {
  loadConfig();
});
</script>

<style scoped>
.guide-config {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.main-content {
  padding: 1rem;
  margin-top: 60px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container i,
.error-container i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-container i {
  color: #2c5aa0;
}

.error-container i {
  color: #e74c3c;
}

.retry-button {
  background-color: #2c5aa0;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

.config-content {
  max-width: 800px;
  margin: 0 auto;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.config-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-section h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.5rem 0;
  color: #2c5aa0;
  font-size: 1.25rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
}

.field-error {
  display: block;
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-help {
  display: block;
  color: #6c757d;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.color-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.color-input {
  width: 50px;
  height: 40px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  cursor: pointer;
}

.color-text {
  flex: 1;
}

.feature-toggles {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.toggle-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 50px;
  height: 24px;
  background: #ccc;
  border-radius: 24px;
  transition: background 0.2s;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.2s;
}

.toggle-input:checked + .toggle-slider {
  background: #2c5aa0;
}

.toggle-input:checked + .toggle-slider::before {
  transform: translateX(26px);
}

.toggle-text {
  font-weight: 500;
  color: #495057;
}

.toggle-help {
  color: #6c757d;
  font-size: 0.875rem;
  margin-left: 66px;
}

.preview-container {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.preview-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.preview-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  background: white;
  border-radius: 4px;
  padding: 4px;
}

.preview-header h4 {
  margin: 0;
  font-size: 1.125rem;
}

.preview-content {
  padding: 1rem;
  background: #f8f9fa;
}

.preview-content p {
  margin: 0 0 1rem 0;
  color: #6c757d;
}

.preview-button {
  background: #f39c12;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.reset-button,
.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.reset-button {
  background: #6c757d;
  color: white;
}

.reset-button:hover {
  background: #5a6268;
}

.save-button {
  background: #2c5aa0;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .config-section {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .color-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .toggle-help {
    margin-left: 0;
    margin-top: 0.25rem;
  }
}
</style>
