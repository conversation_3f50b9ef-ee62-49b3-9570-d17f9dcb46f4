<template>
  <div class="agenda-view">
    <!-- Sidebar Menu (reused from HomeView) -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Agenda"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Date Navigation -->
    <div class="date-navigation-container">
      <div class="current-date" v-html="formattedCurrentDate"></div>
      <div class="right-controls">
        <button class="nav-button prev-button" @click="previousDay">
          <i class="pi pi-chevron-left"></i>
        </button>
        <button class="today-button" @click="goToToday">Hoy</button>
        <button class="nav-button next-button" @click="nextDay">
          <i class="pi pi-chevron-right"></i>
        </button>
        <button class="nav-button view-selector" @click="toggleViewOptions">
          <i class="pi pi-chevron-down"></i>
        </button>

        <!-- View Options Dropdown -->
        <div v-if="showViewOptions" class="view-options">
          <div class="view-option" @click="selectView('day')">Día</div>
          <div class="view-option" @click="selectView('week')">Semana</div>
          <div class="view-option" @click="selectView('month')">Mes</div>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando eventos...</p>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchEvents" class="retry-button">Reintentar</button>
    </div>

    <!-- Day View -->
    <div v-else class="day-view">
      <div v-if="dayEvents.length === 0" class="no-events-message">
        <p>No hay eventos para este día.</p>
      </div>
      <div v-for="event in dayEvents" :key="event.id" class="time-slot">
        <div class="time-column">
          <div class="time start-time">{{ event.startTime }}</div>
          <div class="time-spacer"></div>
          <div class="time end-time">{{ event.endTime }}</div>
        </div>
        <div class="event-card" @click="navigateToEventDetail(event.id)">
          <div class="event-details">
            <div class="event-title">{{ event.title }}</div>
            <div class="event-datetime">{{ event.dateTime }} · {{ event.location }}</div>
            <div class="event-description">{{ event.description }}</div>
          </div>
          <div class="event-image" v-if="event.image">
            <img :src="event.image" :alt="event.title" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';

const router = useRouter();

// State for sidebar
const isSidebarOpen = ref(false);

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Current date state
const currentDate = ref(new Date());
const showViewOptions = ref(false);
const currentView = ref('day');

// Format the current date as "Sábado Mar,01 2025" with only day of week in bold
const formattedCurrentDate = computed(() => {
  const date = currentDate.value;
  const dayOfWeek = new Intl.DateTimeFormat('es-ES', { weekday: 'long' }).format(date);
  const month = new Intl.DateTimeFormat('en-US', { month: 'short' }).format(date);
  const day = date.getDate();
  const year = date.getFullYear();

  // Capitalize first letter of day
  const capitalizedDay = dayOfWeek.charAt(0).toUpperCase() + dayOfWeek.slice(1);

  return `<span style="font-weight: 600;">${capitalizedDay}</span>&nbsp;${month}, ${day.toString().padStart(2, '0')} ${year}`;
});

// Navigation methods are now defined below with the fetchEvents call

const toggleViewOptions = () => {
  showViewOptions.value = !showViewOptions.value;
};

const selectView = (view: string) => {
  currentView.value = view;
  showViewOptions.value = false;
};

// Navigation to event detail
const navigateToEventDetail = (eventId: number) => {
  router.push({ name: 'event-detail', params: { id: eventId.toString() } });
};

// State for events
const dayEvents = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Function to fetch events from the API
const fetchEvents = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    // Format the current date for filtering
    const formattedDate = currentDate.value.toISOString().split('T')[0];

    // Use the API service to fetch events
    const data = await apiService.events.getAll();

    // Transform the API data to match our frontend structure
    dayEvents.value = data.data.map((item: any) => {
      // Parse start and end times
      const startDateTime = new Date(item.start_datetime);
      const endDateTime = item.end_datetime ? new Date(item.end_datetime) : new Date(startDateTime.getTime() + 60 * 60 * 1000);

      const startTime = startDateTime.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' });
      const endTime = endDateTime.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' });

      // Format date for display
      const dateStr = startDateTime.toLocaleDateString('es-ES');
      const dateTime = `${dateStr} ${startTime} - ${endTime}`;

      // Prepare image path
      const imagePath = item.image?.startsWith('/')
        ? item.image
        : `/assets/images/events/${item.image || 'CorkExpEvent.jpeg'}`;

      return {
        id: item.id,
        startTime,
        endTime,
        title: item.title,
        dateTime,
        location: item.location,
        description: item.description,
        image: imagePath
      };
    });

    // Filter events for the current day if needed
    // This can be enhanced to filter by the selected date

  } catch (err) {
    console.error('Error fetching events:', err);
    error.value = 'Failed to load events. Please try again later.';

    // Fallback to sample data if API fails
    dayEvents.value = [
      {
        id: 1,
        startTime: '10:00',
        endTime: '11:00',
        title: 'Sierra de San Pedro',
        dateTime: '24/03/2025 10:00 - 11:00',
        location: 'Sierra San Pedro',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Consectetur euismod mi, turpis enim, egestas placerat non.',
        image: '/assets/images/agenda/sierradesanpedro.jpeg'
      },
      {
        id: 2,
        startTime: '12:00',
        endTime: '13:00',
        title: 'Nombre del evento',
        dateTime: '24/03/2025 12:00 - 13:00',
        location: 'Ubicación',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Consectetur euismod mi, turpis enim, egestas placerat non.',
        image: '/assets/images/events/CorkExpEvent2.jpeg'
      },
      {
        id: 3,
        startTime: '13:30',
        endTime: '14:00',
        title: 'Nombre del evento',
        dateTime: '24/03/2025 13:30 - 14:00',
        location: 'Ubicación',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Consectetur euismod mi, turpis enim, egestas placerat non.',
        image: '/assets/images/events/CorkExpEvent3.jpeg'
      }
    ];
  } finally {
    isLoading.value = false;
  }
};

// Fetch events when component is mounted
onMounted(() => {
  fetchEvents();
});

// Refetch events when the date changes
const goToToday = () => {
  currentDate.value = new Date();
  fetchEvents();
};

const previousDay = () => {
  const newDate = new Date(currentDate.value);
  newDate.setDate(newDate.getDate() - 1);
  currentDate.value = newDate;
  fetchEvents();
};

const nextDay = () => {
  const newDate = new Date(currentDate.value);
  newDate.setDate(newDate.getDate() + 1);
  currentDate.value = newDate;
  fetchEvents();
};
</script>

<style scoped>
.agenda-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem;
}

/* Date Navigation Styles */
.date-navigation-container {
  width: 100%;
  background-color: #edf1f7;
  border-bottom: 1px solid #e0e0e0;
  padding: 1rem 1rem;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.current-date {
  font-size: 0.9rem;
  font-weight: 400;
  color: #334960;
  flex: 1;
  display: flex;
  align-items: center;
  height: 30px;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  height: 30px; /* Match the height of the date */
}

.nav-button {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #59749461;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #597694;
  padding: 0;
}

.today-button {
  padding: 0 1.5rem;
  border-radius: 4px;
  border: 1px solid #8BA8C7;
  background-color: #fff;
  color: #8BA8C7;
  font-size: 0.8rem;
  cursor: pointer;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-selector {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #59749461;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #597694;
  padding: 0;
}

.view-options {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  width: 120px;
}

.view-option {
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #334960;
}

.view-option:hover {
  background-color: #f0f2f5;
}

/* Day View Styles */
.day-view {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.time-slot {
  display: flex;
  align-items: stretch;
  margin-bottom: 1rem;
}

.time-column {
  display: flex;
  flex-direction: column;
  width: 50px;
  height: 100%;
}

.time {
  width: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #334960;
}

.start-time {
  padding-top: 0.5rem;
}

.time-spacer {
  flex-grow: 1;
  min-height: 100px;
}

.end-time {
  padding-bottom: 0.5rem;
}

.event-card {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0.75rem;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.event-details {
  flex: 1;
}

.event-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 0.25rem;
}

.event-datetime {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.event-description {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.3;
}

.event-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  margin-left: 0.75rem;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Loading state styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #597694;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #597694;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #4a6380;
}

.no-events-message {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .date-navigation {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .navigation-controls {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
