<script setup lang="ts">
defineOptions({
  name: 'StoryToursView'
})
import { ref, onMounted, computed, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import AppHeader from '@/components/layout/AppHeader.vue'
import SidebarMenu from '@/components/SidebarMenu.vue'
import apiService from '@/services/api'
import type { StoryTourItem } from '@/services/api'

// Router
const router = useRouter()

// Sidebar state
const isSidebarOpen = ref(false)

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

const closeSidebar = () => {
  isSidebarOpen.value = false
}

// Search functionality
const searchQuery = ref('')

// Filter state
const isFilterOpen = ref(false)
const territories = ref<string[]>([])
const modalities = ref<string[]>([])
const types = ref<string[]>([])
const yearRange = ref({ min: 1900, max: 2025 })

// Filter values
const selectedTerritory = ref('todos')
const selectedModality = ref('todos')
const selectedType = ref('todos')
const selectedYearRange = ref<[number, number]>([1900, 2025])

// StoryTours data
const storyTours = ref<StoryTourItem[]>([])
const loading = ref(false)
const error = ref('')
const noResults = ref(false)

// Sample data to use when API returns empty
const sampleStoryTours: StoryTourItem[] = [
  {
    id: 1,
    title: 'Paisaje del Corcho',
    description: 'Explora los hermosos paisajes naturales de la región corchera',
    image: '/assets/images/storytours/paisaje.png',
    type: 'paisaje',
    territory: 'Extremadura',
    modality: 'Información',
    has_crafts: false,
    has_artists: false,
    year: 2020,
    audio_file: null,
    video_file: null,
    ar_model_file: null,
    location_id: 1,
    is_featured: true,
    is_active: true,
    created_at: '2023-01-01',
    updated_at: '2023-01-01'
  },
  {
    id: 2,
    title: 'Patrimonio del Corcho',
    description: 'Descubre el rico patrimonio cultural e histórico del corcho',
    image: '/assets/images/storytours/patrimonio.png',
    type: 'patrimonio',
    territory: 'Andalucía',
    modality: 'Testimonio',
    has_crafts: false,
    has_artists: false,
    year: 2021,
    audio_file: null,
    video_file: null,
    ar_model_file: null,
    location_id: 2,
    is_featured: false,
    is_active: true,
    created_at: '2023-01-01',
    updated_at: '2023-01-01'
  },
  {
    id: 3,
    title: 'Cultura del Corcho',
    description: 'Sumérgete en la cultura y tradiciones relacionadas con el corcho',
    image: '/assets/images/storytours/cultura.png',
    type: 'cultura',
    territory: 'Extremadura',
    modality: 'Juego',
    has_crafts: false,
    has_artists: false,
    year: 2022,
    audio_file: null,
    video_file: null,
    ar_model_file: null,
    location_id: 3,
    is_featured: false,
    is_active: true,
    created_at: '2023-01-01',
    updated_at: '2023-01-01'
  },
  {
    id: 4,
    title: 'Gastronomía y Corcho',
    description: 'Descubre la relación entre la gastronomía local y la industria del corcho',
    image: '/assets/images/storytours/gastronomia.png',
    type: 'gastronomia',
    territory: 'Andalucía',
    modality: 'Información',
    has_crafts: false,
    has_artists: false,
    year: 2023,
    audio_file: null,
    video_file: null,
    ar_model_file: null,
    location_id: 4,
    is_featured: false,
    is_active: true,
    created_at: '2023-01-01',
    updated_at: '2023-01-01'
  },
  {
    id: 5,
    title: 'Naturaleza y Corcho',
    description: 'Explora la relación entre la naturaleza y la producción sostenible de corcho',
    image: '/assets/images/storytours/naturaleza.png',
    type: 'naturaleza',
    territory: 'Extremadura',
    modality: 'Paisaje',
    has_crafts: false,
    has_artists: false,
    year: 2024,
    audio_file: null,
    video_file: null,
    ar_model_file: null,
    location_id: 5,
    is_featured: false,
    is_active: true,
    created_at: '2023-01-01',
    updated_at: '2023-01-01'
  }
]

// Toggle filter panel
const toggleFilter = () => {
  isFilterOpen.value = !isFilterOpen.value
}

// Load filter options
const loadFilterOptions = async () => {
  try {
    console.log('Fetching filter options...')
    const response = await apiService.storyTours.getFilterOptions()
    console.log('Filter options response:', response)

    // The response is directly the data object
    if (response && response.data) {
      console.log('Processing filter options:', response.data)

      // Set default values even if the response is not as expected
      territories.value = []
      modalities.value = []
      types.value = ['paisaje', 'patrimonio', 'cultura', 'gastronomia', 'naturaleza', 'easy', 'medium']

      // Try to extract data from the response if available
      if (typeof response.data === 'object') {
        if (Array.isArray(response.data.territories)) {
          territories.value = response.data.territories
        }

        if (Array.isArray(response.data.modalities)) {
          modalities.value = response.data.modalities
        }

        if (Array.isArray(response.data.types)) {
          types.value = [...new Set([...types.value, ...response.data.types])]
        }

        if (response.data.year_range) {
          yearRange.value = response.data.year_range
          selectedYearRange.value = [response.data.year_range.min, response.data.year_range.max]
        }

        console.log('Using filter options:', {
          territories: territories.value,
          modalities: modalities.value,
          types: types.value,
          yearRange: yearRange.value
        })
      }
    } else {
      console.error('Invalid response format:', response)
    }
  } catch (err) {
    console.error('Error loading filter options:', err)
  }
}

// Load story tours with filters
const loadStoryTours = async () => {
  loading.value = true
  error.value = ''

  // Reset the no results state
  noResults.value = false

  try {
    console.log('Fetching story tours with filters:', {
      territory: selectedTerritory.value,
      modality: selectedModality.value,
      type: selectedType.value,
      year_min: selectedYearRange.value[0],
      year_max: selectedYearRange.value[1]
    })

    const response = await apiService.storyTours.getAll()
    console.log('Story tours response:', response)

    if (response && response.data) {
      // Check if the data is wrapped in a data property (Laravel API Resource collection)
      console.log('Processing story tours response:', response.data)

      if (response.data.data !== undefined) {
        // Laravel API Resource collection format
        if (Array.isArray(response.data.data)) {
          if (response.data.data.length > 0) {
            console.log(`API returned ${response.data.data.length} story tours`)
            storyTours.value = apiService.storyTours.fixImageUrls(response.data.data)
          } else {
            console.log('API returned empty array, using sample data')
            storyTours.value = sampleStoryTours
          }
        } else {
          console.warn('Data property is not an array:', response.data.data)
          storyTours.value = sampleStoryTours
        }
      } else {
        // Direct array format
        if (Array.isArray(response.data)) {
          if (response.data.length > 0) {
            console.log(`API returned ${response.data.length} story tours`)
            storyTours.value = apiService.storyTours.fixImageUrls(response.data)
          } else {
            console.log('API returned empty array, using sample data')
            storyTours.value = sampleStoryTours
          }
        } else {
          console.warn('Response data is not an array:', response.data)
          storyTours.value = sampleStoryTours
        }
      }
    } else {
      console.error('Invalid response format:', response)
      storyTours.value = sampleStoryTours
    }
  } catch (err) {
    console.error('Error loading story tours:', err)
    error.value = 'Error al cargar los StoryTours. Por favor, inténtalo de nuevo.'
    storyTours.value = sampleStoryTours
  } finally {
    loading.value = false

    // Check if we have any results after filtering
    if (storyTours.value.length === 0) {
      noResults.value = true
    } else {
      noResults.value = false
    }
  }
}

// Reset filters
const resetFilters = () => {
  selectedTerritory.value = 'todos'
  selectedModality.value = 'todos'
  selectedType.value = 'todos'
  selectedYearRange.value = [yearRange.value.min, yearRange.value.max]

  // Just use the sample data directly
  storyTours.value = sampleStoryTours
  loading.value = false
  error.value = ''
}

// Filter story tours by search query and other filters
const filteredStoryTours = computed(() => {
  // If we're loading, return empty array
  if (loading.value) {
    return []
  }

  if (!storyTours.value || !Array.isArray(storyTours.value)) {
    return []
  }

  // Apply all filters
  let filtered = storyTours.value

  // Filter by territory if not 'todos'
  if (selectedTerritory.value !== 'todos') {
    filtered = filtered.filter(tour =>
      tour.territory === selectedTerritory.value
    )
  }

  // Filter by modality if not 'todos'
  if (selectedModality.value !== 'todos') {
    filtered = filtered.filter(tour =>
      tour.modality === selectedModality.value
    )
  }

  // Filter by type if not 'todos'
  if (selectedType.value !== 'todos') {
    filtered = filtered.filter(tour =>
      tour.type === selectedType.value
    )
  }

  // Filter by year range
  filtered = filtered.filter(tour =>
    !tour.year ||
    (tour.year >= selectedYearRange.value[0] &&
     tour.year <= selectedYearRange.value[1])
  )

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((tour: StoryTourItem) =>
      tour && tour.title && tour.description && (
        tour.title.toLowerCase().includes(query) ||
        tour.description.toLowerCase().includes(query)
      )
    )
  }

  return filtered
})

// Handle image loading errors
const handleImageError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  if (imgElement) {
    imgElement.src = '/assets/images/placeholder.png'
  }
}

// Navigate to StoryTour detail view
const viewStoryTourDetails = (id: number) => {
  router.push(`/storytours/${id}`)
}

// Function to load all necessary data
const loadAllData = async () => {
  // Only load filter options if they haven't been loaded yet
  if (territories.value.length === 0 || modalities.value.length === 0 || types.value.length === 0) {
    await loadFilterOptions()
  }

  // Always reload story tours
  await loadStoryTours()

  // If no story tours were loaded, use sample data
  if (storyTours.value.length === 0) {
    console.log('No story tours loaded, using sample data')
    storyTours.value = sampleStoryTours
  }
}

// Initialize on component mount
onMounted(async () => {
  await loadAllData()
})

// Reload data when navigating back to this view
onActivated(async () => {
  console.log('StoryToursView activated, reloading data')
  await loadAllData()
})
</script>

<template>
  <div class="storytours-view">
    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      :showLogo="true"
      logoSrc="/assets/logos/CorkExpLogoBlack.png"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Main Content -->
    <main class="main-content">
      <div class="header-container">
        <h1 class="view-title">StoryTours</h1>
        <button class="filter-button" @click="toggleFilter">
          <i class="pi pi-filter"></i>
          Filtrar
        </button>
      </div>

      <!-- Search Bar -->
      <div class="search-container">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="Buscar"
          class="search-input"
        >
        <button class="search-button">
          <i class="pi pi-search"></i>
        </button>
      </div>

      <!-- Filter Panel -->
      <div class="filter-panel" :class="{ 'open': isFilterOpen }">
        <div class="filter-header">
          <h2>STORYTOUR</h2>
          <button class="close-button" @click="toggleFilter">
            <i class="pi pi-times"></i>
          </button>
        </div>

        <div class="filter-content">
          <div class="filter-section">
            <h3>FILTRAR POR</h3>

            <!-- Territory Filter -->
            <div class="filter-group">
              <label>Territorio</label>
              <select v-model="selectedTerritory" class="filter-select" @change="loadStoryTours">
                <option value="todos">Todos</option>
                <option v-for="territory in territories" :key="territory" :value="territory">
                  {{ territory }}
                </option>
              </select>
            </div>

            <!-- Modality Filter -->
            <div class="filter-group">
              <label>Modalidad</label>
              <select v-model="selectedModality" class="filter-select" @change="loadStoryTours">
                <option value="todos">Todos</option>
                <option v-for="modality in modalities" :key="modality" :value="modality">
                  {{ modality }}
                </option>
              </select>
            </div>

            <!-- Type Filter -->
            <div class="filter-group">
              <label>Tipo</label>
              <select v-model="selectedType" class="filter-select" @change="loadStoryTours">
                <option value="todos">Todos</option>
                <option v-for="type in types" :key="type" :value="type">
                  {{ type.charAt(0).toUpperCase() + type.slice(1) }}
                </option>
              </select>
            </div>
          </div>

          <!-- Year Range Filter -->
          <div class="filter-section">
            <h3>FECHA</h3>
            <div class="year-range">
              <span>{{ selectedYearRange[0] }}</span>
              <input
                type="range"
                class="range-slider"
                :min="yearRange.min"
                :max="yearRange.max"
                v-model.number="selectedYearRange[0]"
                @change="loadStoryTours"
              >
              <input
                type="range"
                class="range-slider"
                :min="yearRange.min"
                :max="yearRange.max"
                v-model.number="selectedYearRange[1]"
                @change="loadStoryTours"
              >
              <span>{{ selectedYearRange[1] }}</span>
            </div>
          </div>

          <!-- Reset Filters Button -->
          <button class="reset-filters-button" @click="resetFilters">
            Borrar filtros
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando StoryTours...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredStoryTours.length === 0 && !loading" class="empty-container">
        <i class="pi pi-info-circle"></i>
        <p>No se encontraron StoryTours con los filtros seleccionados.</p>
        <button class="reset-filters-button" @click="resetFilters">
          Borrar filtros
        </button>
      </div>

      <!-- StoryTours Cards -->
      <div v-else class="storytours-cards">
        <div v-for="tour in filteredStoryTours" :key="tour.id" class="storytour-card" @click="viewStoryTourDetails(tour.id)">
          <div class="card-image-container">
            <img
              :src="tour.image || `/assets/images/storytours/${tour.type || 'default'}.png`"
              :alt="tour.title"
              class="card-image"
              crossorigin="anonymous"
              @error="handleImageError"
            >
            <div class="card-overlay">
              <h3 class="card-title">{{ tour.title }}</h3>
              <div class="card-actions" @click.stop>
                <!-- Audio button -->
                <button class="action-button" title="Audio">
                  <i class="pi pi-volume-up"></i>
                </button>

                <!-- Video button -->
                <button class="action-button" title="Video">
                  <i class="pi pi-video"></i>
                </button>

                <!-- AR button -->
                <button class="action-button" title="Realidad Aumentada">
                  <i class="pi pi-mobile"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.storytours-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.main-content {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.view-title {
  color: #334960;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #fff;
  border: 1px solid #E4EBF2;
  border-radius: 8px;
  color: #334960;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background-color: #f0f5fa;
}

/* Search Bar Styles */
.search-container {
  position: relative;
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #E4EBF2;
  border-radius: 8px;
  background-color: #fff;
  font-size: 0.9rem;
  color: #334960;
  outline: none;
}

.search-input::placeholder {
  color: #8BA8C7;
}

.search-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #8BA8C7;
  font-size: 1rem;
  cursor: pointer;
}

/* Filter Panel Styles */
.filter-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 85%;
  max-width: 350px;
  height: 100vh;
  background-color: #fff;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.filter-panel.open {
  right: 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #E4EBF2;
}

.filter-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
}

.close-button {
  background: none;
  border: none;
  color: #334960;
  font-size: 1.2rem;
  cursor: pointer;
}

.filter-content {
  padding: 1rem;
}

.filter-section {
  margin-bottom: 1.5rem;
}

.filter-section h3 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 1rem;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group label {
  display: block;
  font-size: 0.9rem;
  color: #334960;
  margin-bottom: 0.5rem;
}

.filter-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #E4EBF2;
  border-radius: 8px;
  background-color: #fff;
  font-size: 0.9rem;
  color: #334960;
  outline: none;
}

.filter-toggle {
  margin-bottom: 1rem;
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.toggle-label {
  font-size: 0.9rem;
  color: #334960;
}

/* Toggle Switch Styles */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #E91E63;
}

input:focus + .slider {
  box-shadow: 0 0 1px #E91E63;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Year Range Slider Styles */
.year-range {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0 0.5rem;
}

.range-slider {
  width: 100%;
  height: 5px;
  -webkit-appearance: none;
  appearance: none;
  background: #E4EBF2;
  outline: none;
  border-radius: 5px;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #E91E63;
  cursor: pointer;
}

.range-slider::-moz-range-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #E91E63;
  cursor: pointer;
}

.reset-filters-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-filters-button:hover {
  background-color: #333;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #334960;
}

.loading-container i,
.error-container i,
.empty-container i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.error-container i {
  color: #f44336;
}

.empty-container i {
  color: #2196f3;
}

/* StoryTours Cards Styles */
.storytours-cards {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.storytour-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.storytour-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-image-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-overlay {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: rgba(240, 245, 250, 0.6);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
}

.card-title {
  color: #334960;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: nowrap;
  justify-content: flex-end;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.2rem;
  height: 2.2rem;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #E4EBF2;
  color: #334960;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  background-color: #f0f5fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button i {
  font-size: 1.1rem;
}


</style>
