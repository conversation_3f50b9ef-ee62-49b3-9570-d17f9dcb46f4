<template>
  <div class="experience-detail-view">
    <!-- Toast Notification -->
    <Toast
      :visible="showToast"
      :type="toastType"
      :title="toastTitle"
      :message="toastMessage"
      @close="showToast = false"
    />
    <!-- Header -->
    <AppHeader
      :title="experience?.type === 'hotel' ? 'Alojamiento' : 'Experiencias'"
      :showBackButton="true"
      :showFavoriteButton="true"
      :showMenuButton="false"
      :showCallButton="experience?.type === 'hotel'"
      :showShareButton="false"
      :leftAlignedTitle="true"
      @goBack="goBack"
    />

    <!-- Main Content -->
    <main class="main-content">
      <div v-if="loading" class="loading-container">
        <p>Cargando experiencia...</p>
      </div>
      <div v-else-if="!experience" class="error-container">
        <p>No se pudo encontrar la experiencia</p>
        <button class="primary-button" @click="goBack">Volver</button>
      </div>
      <div v-else class="experience-content">
        <!-- QR Code for Hotel -->
        <div v-if="experience.type === 'hotel'" class="qr-code-container">
          <img src="/assets/images/QR.svg" alt="QR Code" class="qr-code-image" />
        </div>

        <!-- Hero Image for non-hotel experiences -->
        <div v-else class="hero-image-container">
          <img :src="experience.image" :alt="experience.title" class="hero-image" />
        </div>

        <!-- Experience Info -->
        <div class="experience-info" :class="{ 'hotel-info': experience?.type === 'hotel' }">
          <h2 class="experience-title">{{ experience.title }}</h2>

          <!-- Hotel specific layout -->
          <div v-if="experience.type === 'hotel'" class="hotel-layout">
            <!-- Hotel Images -->
            <div class="hotel-images">
              <div class="hotel-main-image">
                <img :src="experience.image" :alt="experience.title" />
              </div>
              <div class="hotel-map-image" v-if="experience.mapImage">
                <img :src="experience.mapImage" :alt="'Map for ' + experience.title" />
              </div>
            </div>

            <!-- Hotel Info Sections - All in one container -->
            <div class="hotel-info-container">
              <div class="hotel-info-section">
                <h3 class="section-label">Dirección</h3>
                <p class="section-content">{{ experience.location }}</p>
              </div>

              <div class="hotel-info-section" v-if="experience.reservationDates">
                <h3 class="section-label">Fechas de reserva</h3>
                <p class="section-content">{{ experience.reservationDates }}</p>
              </div>

              <div class="hotel-info-section" v-if="experience.phone">
                <h3 class="section-label">Teléfono</h3>
                <p class="section-content">{{ experience.phone }}</p>
              </div>
            </div>
          </div>

          <!-- Park layout -->
          <div v-else-if="experience.type === 'park'" class="park-layout">
            <p class="park-description">{{ experience.description }}</p>

            <div class="park-info">
              <div class="park-info-item">
                <i class="pi pi-calendar"></i>
                <span>{{ experience.date }}</span>
              </div>
              <div class="park-info-item">
                <i class="pi pi-map-marker"></i>
                <span>{{ experience.location }}</span>
              </div>
            </div>

            <div class="park-buttons">
              <button class="park-button agency-button">Agencia</button>
              <button class="park-button reserve-button">Reserva</button>
            </div>
          </div>

          <!-- Workshop layout -->
          <div v-else-if="experience.type === 'workshop'" class="workshop-layout">
            <p class="workshop-description">{{ experience.description }}</p>

            <div class="workshop-info">
              <div class="workshop-info-item" v-if="experience.phone">
                <i class="pi pi-phone"></i>
                <span>{{ experience.phone }}</span>
              </div>
              <div class="workshop-info-item" v-if="experience.date">
                <i class="pi pi-calendar"></i>
                <span>{{ experience.date }}</span>
              </div>
              <div class="workshop-info-item">
                <i class="pi pi-map-marker"></i>
                <span>{{ experience.location }}</span>
              </div>
            </div>

            <div class="workshop-buttons">
              <button class="workshop-button agency-button">Agencia</button>
              <button class="workshop-button reserve-button">Reserva</button>
            </div>
          </div>

          <!-- Restaurant layout -->
          <div v-else-if="experience.type === 'restaurant'" class="restaurant-layout">
            <p class="restaurant-description">{{ experience.description }}</p>

            <div class="restaurant-info">
              <div class="restaurant-info-item" v-if="experience.address">
                <i class="pi pi-map-marker"></i>
                <span>{{ experience.address }}</span>
              </div>
              <div class="restaurant-info-item" v-if="experience.phone">
                <i class="pi pi-phone"></i>
                <span>{{ experience.phone }}</span>
              </div>
              <div class="restaurant-info-item" v-if="experience.email">
                <i class="pi pi-envelope"></i>
                <span>{{ experience.email }}</span>
              </div>
              <div class="restaurant-info-item" v-if="experience.openingHours">
                <i class="pi pi-clock"></i>
                <span>{{ experience.openingHours }}</span>
              </div>
              <div class="restaurant-info-item" v-if="experience.cuisineType">
                <i class="pi pi-tag"></i>
                <span>{{ experience.cuisineType }}</span>
              </div>
              <div class="restaurant-info-item" v-if="experience.price">
                <i class="pi pi-euro"></i>
                <span>{{ formatPrice(experience.price) }}</span>
              </div>
            </div>

            <div class="restaurant-buttons">
              <a v-if="experience.website" :href="experience.website" target="_blank" class="restaurant-button website-button">
                <i class="pi pi-globe"></i>
                <span>Sitio Web</span>
              </a>
              <a v-if="experience.menuUrl" :href="experience.menuUrl" target="_blank" class="restaurant-button menu-button">
                <i class="pi pi-list"></i>
                <span>Ver Menú</span>
              </a>
              <button class="restaurant-button reserve-button" @click="showReservationForm = true">
                <i class="pi pi-calendar"></i>
                <span>Reservar</span>
              </button>
            </div>
          </div>

          <!-- Default layout for new experience types -->
          <div v-else>
            <div class="location-info">
              <i class="pi pi-map-marker location-icon"></i>
              <span>{{ experience.location }}</span>
            </div>

            <!-- Action buttons based on experience type -->
            <div class="action-buttons">
              <!-- For Routes (Ruta): Show map button -->
              <button v-if="experience.type === 'ruta'" class="action-button primary" @click="viewOnMap">
                <i class="pi pi-map"></i>
                <span>Ver en mapa</span>
              </button>

              <!-- For Packages (Paquete): Show reserve button -->
              <button v-else-if="experience.type === 'paquete'" class="action-button primary" @click="showReservationForm = true">
                <i class="pi pi-calendar"></i>
                <span>Reservar</span>
              </button>

              <!-- For Activities: Show more info button or reserve if price exists -->
              <template v-else-if="experience.type === 'actividades'">
                <button v-if="experience.price" class="action-button primary" @click="showReservationForm = true">
                  <i class="pi pi-calendar"></i>
                  <span>Reservar</span>
                </button>
                <button v-else class="action-button secondary">
                  <i class="pi pi-info-circle"></i>
                  <span>Más información</span>
                </button>
              </template>

              <!-- Default fallback for other types -->
              <template v-else>
                <button class="action-button primary" @click="showReservationForm = true">
                  <i class="pi pi-calendar"></i>
                  <span>Reservar</span>
                </button>
                <button class="action-button secondary">
                  <i class="pi pi-info-circle"></i>
                  <span>Más información</span>
                </button>
              </template>
            </div>

            <div class="description-section">
              <h3>Descripción</h3>
              <p class="description-text">{{ experience.description }}</p>
            </div>
          </div>

          <!-- Contact section for non-hotel, non-park, non-workshop, non-restaurant experiences -->
          <div class="contact-section" v-if="(experience.phone || experience.website) && experience.type !== 'hotel' && experience.type !== 'park' && experience.type !== 'workshop' && experience.type !== 'restaurant'">
            <h3>Contacto</h3>
            <div class="contact-buttons">
              <a v-if="experience.phone" :href="`tel:${experience.phone}`" class="contact-button">
                <i class="pi pi-phone"></i>
                <span>Llamar</span>
              </a>
              <a v-if="experience.website" :href="experience.website" target="_blank" class="contact-button">
                <i class="pi pi-globe"></i>
                <span>Sitio web</span>
              </a>
            </div>
          </div>

          <!-- No gallery section in Figma designs -->

          <!-- Reservation Form -->
          <div v-if="showReservationForm && experience" class="reservation-form-wrapper">
            <div class="reservation-form-overlay" @click="showReservationForm = false"></div>
            <div class="reservation-form-modal">
              <button class="close-button" @click="showReservationForm = false">
                <i class="pi pi-times"></i>
              </button>

              <!-- Title is already shown in the modal header -->

              <!-- Individual Reservation Form (default) -->
              <ReservationForm
                v-if="!isAuthenticated || !hasGroups || reservationType === 'individual'"
                :experienceId="experience.id"
                :experienceType="experience.type"
                @reservationComplete="onReservationComplete"
              />

              <!-- Group Reservation Form (only shown when user has groups and selects it) -->
              <GroupReservationForm
                v-else-if="reservationType === 'group'"
                :experienceId="experience.id"
                :experienceType="experience.type"
                @reservationComplete="onReservationComplete"
              />

              <!-- Show group reservation option if user has groups -->
              <div v-if="isAuthenticated && hasGroups" class="reservation-option">
                <button
                  class="switch-reservation-type"
                  @click="reservationType = reservationType === 'individual' ? 'group' : 'individual'"
                >
                  {{ reservationType === 'individual' ? '¿Quieres hacer una reserva grupal?' : 'Volver a reserva individual' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- No browser navigation bar in all designs -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import ReservationForm from '../components/reservation/ReservationForm.vue';
import GroupReservationForm from '../components/reservation/GroupReservationForm.vue';
import Toast from '../components/ui/Toast.vue';
import apiService from '../services/api';
import { isAuthenticated, currentUser, getUserGroups } from '../services/auth';

interface Experience {
  id: number;
  type: string;
  title: string;
  shortDescription: string;
  description: string;
  location: string;
  distance: string;
  image: string;
  phone: string;
  website?: string;
  date?: string;
  reservationDates?: string;
  mapImage?: string;
  address?: string;
  email?: string;
  cuisineType?: string;
  openingHours?: string;
  menuUrl?: string;
  price?: number;
}

// Toast notification state
const showToast = ref(false);
const toastType = ref('success');
const toastTitle = ref('');
const toastMessage = ref('');

const router = useRouter();
const route = useRoute();
const loading = ref(true);
const error = ref<string | null>(null);
const experience = ref<Experience | null>(null);
const showReservationForm = ref(false);
const reservationType = ref('individual'); // 'individual' or 'group'

// Get image URL with proper path
const getImageUrl = (path: string | null): string => {
  if (!path) return '/assets/images/placeholder.png';
  return path.startsWith('http') || path.startsWith('/') ? path : `/assets/images/experiences/${path}`;
};

// Format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', { year: 'numeric', month: 'long', day: 'numeric' });
};

// Format price
const formatPrice = (price: number | null): string => {
  if (price === null || price === undefined) return 'No especificado';
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' }).format(price);
};

onMounted(async () => {
  try {
    const id = route.params.id as string;
    loading.value = true;

    // Fetch experience data from API
    const response = await apiService.experiences.getById(id);
    console.log('API Response:', response); // Debug log

    // Check if we have data in the response
    if (response && response.data) {
      // Handle both array and single object responses
      const experienceData = Array.isArray(response.data) ? response.data[0] : response.data;

      if (experienceData) {
        console.log('Experience data:', experienceData); // Debug log

        // Transform API data to match our component's structure
        experience.value = {
          id: experienceData.id,
          type: experienceData.type,
          title: experienceData.title,
          shortDescription: experienceData.short_description || '',
          description: experienceData.description,
          location: experienceData.location ? experienceData.location.name : experienceData.address || '',
          distance: experienceData.distance || '',
          image: getImageUrl(experienceData.image),
          phone: experienceData.phone || '',
          website: experienceData.website || '',
          date: formatDate(experienceData.start_date),
          reservationDates: experienceData.start_date ? `${formatDate(experienceData.start_date)} - ${formatDate(experienceData.end_date)}` : '',
          // Additional fields for restaurant type
          address: experienceData.address || '',
          email: experienceData.email || '',
          cuisineType: experienceData.cuisine_type || '',
          openingHours: experienceData.opening_hours || '',
          menuUrl: experienceData.menu_url || '',
          price: experienceData.price || undefined,
          // For hotel type, we need a map image
          mapImage: experienceData.type === 'hotel' ? '/assets/images/experiences/mapa.png' : undefined
        };
      } else {
        console.error('Experience data is empty');
      }
    } else {
      console.error('No experience data found in response');
    }
  } catch (err) {
    console.error('Error fetching experience:', err);
  } finally {
    loading.value = false;
  }
});

// Navigation function
const goBack = () => {
  router.back();
};

// View on map function for routes
const viewOnMap = () => {
  if (experience.value) {
    router.push(`/mapa?route=${experience.value.id}`);
  }
};

// Check if user has groups
const hasGroups = computed(() => {
  return isAuthenticated.value && currentUser.value?.groups && currentUser.value.groups.length > 0;
});

// Fetch user groups when component is mounted
onMounted(async () => {
  if (isAuthenticated.value) {
    try {
      // Only try to fetch groups if we don't already have them
      if (!currentUser.value?.groups || currentUser.value.groups.length === 0) {
        await getUserGroups();
      }
    } catch (error) {
      console.error('Error fetching user groups:', error);
    }
  }
});

// Handle reservation completion
const onReservationComplete = (reservation: any) => {
  showReservationForm.value = false;

  // Show success message with toast
  toastType.value = 'success';
  toastTitle.value = 'Reserva Confirmada';

  if (reservation?.booking_code?.code) {
    toastMessage.value = `Reserva grupal realizada con éxito. Código de reserva: ${reservation.booking_code.code}`;
  } else {
    toastMessage.value = 'Reserva realizada con éxito. Recibirás una confirmación por correo electrónico.';
  }

  showToast.value = true;
};
</script>

<style scoped>
.experience-detail-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  overflow-y: auto;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.primary-button {
  background-color: #334960;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 1rem;
}

/* QR Code Styles */
.qr-code-container {
  padding: 2rem 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  margin-top: 0;
  position: relative;
  z-index: 1; /* Lower z-index to ensure it doesn't overlap the header */
  pointer-events: none; /* Make the container not capture mouse events */
}

.qr-code-container * {
  pointer-events: auto; /* But allow its children to capture mouse events */
}

.qr-code-image {
  width: 150px;
  height: auto;
}

/* Hero Image Styles */
.hero-image-container {
  position: relative;
  width: 100%;
  height: 250px;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Experience Info Styles */
.experience-info {
  padding: 1.5rem;
  background-color: white;
  border-radius: 12px 12px 0 0;
  margin-top: -20px;
  position: relative;
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
}

/* Hotel Info Styles */
.hotel-info {
  margin-top: 1rem;
  border-radius: 0;
  box-shadow: none;
  padding-top: 1rem;
}

.experience-title {
  margin: 0 0 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #334960;
  font-family: 'Poppins', sans-serif;
}

.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  color: #666;
}

.location-icon {
  margin-right: 0.5rem;
  color: #334960;
}

/* Action Buttons Styles */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  font-family: 'Poppins', sans-serif;
}

.action-button.primary {
  background-color: #334960;
  color: white;
}

.action-button.secondary {
  background-color: #f0f2f5;
  color: #334960;
  border: 1px solid #ddd;
}

/* Description Section Styles */
.description-section, .contact-section {
  margin-bottom: 1.5rem;
}

.description-section h3, .contact-section h3 {
  font-size: 1.1rem;
  color: #334960;
  margin-bottom: 0.75rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
}

.description-text {
  line-height: 1.6;
  color: #444;
}

/* Contact Buttons Styles */
.contact-buttons {
  display: flex;
  gap: 1rem;
}

.contact-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 4px;
  background-color: #f0f2f5;
  color: #334960;
  text-decoration: none;
  font-weight: 500;
  border: 1px solid #ddd;
}

/* No gallery styles needed */

/* Hotel Layout Styles */
.hotel-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0;
  margin-top: 1.5rem;
}

.hotel-images {
  display: flex;
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
}

.hotel-main-image, .hotel-map-image {
  flex: 1;
  height: 150px;
  overflow: hidden;
}

.hotel-main-image {
  border-radius: 0;
}

.hotel-map-image {
  border-radius: 0;
}

.hotel-main-image img, .hotel-map-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hotel-info-container {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.5rem;
}

.hotel-info-section {
  padding: 0.25rem 0;
  margin-bottom: 0.75rem;
}

.hotel-info-section:last-child {
  margin-bottom: 0;
}

.section-label {
  color: #8BA8C7;
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
  font-weight: 400;
}

.section-content {
  color: #334960;
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.4;
}

/* Contact buttons removed as per Figma design */

/* Park Layout Styles */
.park-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.park-description {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #444;
  margin: 0;
  padding-right: 0.5rem;
}

.park-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.park-info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #444;
}

.park-info-item i {
  color: #334960;
  font-size: 1.1rem;
}

.park-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.park-button {
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
}

.agency-button {
  background-color: #334960;
  color: white;
}

.reserve-button {
  background-color: #334960;
  color: white;
}

/* Workshop Layout Styles */
.workshop-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.workshop-description {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #444;
  margin: 0;
  padding-right: 0.5rem;
}

.workshop-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.workshop-info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #444;
}

.workshop-info-item i {
  color: #334960;
  font-size: 1.1rem;
}

.workshop-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.workshop-button {
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
}

/* Restaurant Layout Styles */
.restaurant-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.restaurant-description {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #444;
  margin: 0;
  padding-right: 0.5rem;
}

.restaurant-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.restaurant-info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #444;
}

.restaurant-info-item i {
  color: #334960;
  font-size: 1.1rem;
}

.restaurant-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.restaurant-button {
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.menu-button, .website-button {
  background-color: #f0f2f5;
  color: #334960;
  border: 1px solid #ddd;
}

.agency-button {
  background-color: #334960;
  color: white;
}

.reserve-button {
  background-color: #334960;
  color: white;
}

/* Reservation Form Styles */
.reservation-form-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.reservation-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.reservation-form-modal {
  position: relative;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  padding: 20px;
}

.reservation-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: #333;
  text-align: center;
}

.reservation-option {
  margin-top: 1.5rem;
  text-align: center;
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.switch-reservation-type {
  background: none;
  border: none;
  color: #DC8960;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  padding: 0.5rem 1rem;
  transition: color 0.2s;
}

.switch-reservation-type:hover {
  color: #c77a53;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #666;
  cursor: pointer;
  z-index: 1002;
}

/* No browser navigation bar styles needed */

/* No bottom navigation styles needed */

/* Responsive Styles */
@media (min-width: 768px) {
  .hero-image-container {
    height: 350px;
  }

  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
