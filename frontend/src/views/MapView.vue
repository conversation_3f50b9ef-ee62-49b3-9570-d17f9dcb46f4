<template>
  <div class="map-view">
    <!-- Header -->
    <AppHeader
      title=""
      :showBackButton="false"
      :showFavoriteButton="false"
      class="map-header"
    >
      <template #left>
        <div class="title-with-icon">
          <i class="pi pi-map header-icon"></i>
          <span>Mapa</span>
        </div>
      </template>
      <template #actions>
        <div class="header-buttons">
          <button class="radius-button" @click="toggleRadius">{{ radiusKm }}kms</button>
          <button class="icon-round-button favorite-button">
            <i class="pi pi-heart"></i>
          </button>
          <button class="icon-round-button filter-button">
            <i class="pi pi-filter"></i>
          </button>
          <button class="icon-round-button back-button" @click="router.back()">
            <i class="pi pi-arrow-left"></i>
          </button>
        </div>
      </template>
    </AppHeader>

    <!-- Map Container -->
    <div class="map-container">
      <div v-if="isLoading && !loadError" class="map-loading">
        <div class="loading-spinner"></div>
        <p>Cargando mapa...</p>
      </div>
      <div v-if="loadError" class="map-error">
        <img src="/assets/images/map/map.png" alt="Mapa de España" class="fallback-map" />
        <div class="error-message">
          <p>No se pudo cargar el mapa interactivo. Mostrando versión estática.</p>
        </div>
      </div>

      <div id="map" ref="mapContainer" class="leaflet-map" style="height: 100%; width: 100%;"></div>
    </div>

    <!-- Map Tools -->
    <div class="map-tools">
      <button class="tool-button" @click="findUserLocation" :class="{ 'active': userLocationActive }">
        <i class="pi pi-user"></i>
      </button>
      <button class="tool-button">
        <i class="pi pi-car"></i>
      </button>
      <button class="tool-button">
        <i class="pi pi-list"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '@/services/api';
import { initLeaflet, createMarkerIcon, createMarkerClusterGroup, SPAIN_CENTER, DEFAULT_ZOOM } from '../utils/leafletLoader';
import type { Map as LeafletMap, Marker, MarkerClusterGroup } from 'leaflet';

const router = useRouter();
const mapContainer = ref<HTMLElement | null>(null);
let map: LeafletMap | null = null;
let markers: Marker[] = [];
let markerCluster: MarkerClusterGroup | null = null;
let userLocationMarker: Marker | null = null;
let userLocationCircle: any = null; // Circle showing accuracy
const isLoading = ref(true);
const loadError = ref(false);
const radiusKm = ref(30);
const apiLocations = ref<any[]>([]);
const apiError = ref<string | null>(null);
const userLocationActive = ref(false);
const userLocationError = ref<string | null>(null);

// Find user's location
const findUserLocation = () => {
  // Toggle active state
  userLocationActive.value = !userLocationActive.value;

  if (!userLocationActive.value) {
    // Remove user location marker and circle if they exist
    if (userLocationMarker && map) {
      map.removeLayer(userLocationMarker);
      userLocationMarker = null;
    }

    if (userLocationCircle && map) {
      map.removeLayer(userLocationCircle);
      userLocationCircle = null;
    }

    return;
  }

  // Check if geolocation is available
  if (!navigator.geolocation) {
    userLocationError.value = 'Tu navegador no soporta geolocalización';
    userLocationActive.value = false;
    return;
  }

  // Show loading state
  isLoading.value = true;

  // Get current position
  navigator.geolocation.getCurrentPosition(
    (position) => {
      // Success callback
      const { latitude, longitude, accuracy } = position.coords;

      if (!map) return;

      // Get Leaflet instance
      const L = initLeaflet();

      // Create a custom user location icon
      const userIcon = L.divIcon({
        className: 'user-location-marker',
        html: `<div class="user-dot"></div>`,
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      // Remove previous marker and circle if they exist
      if (userLocationMarker && map) {
        map.removeLayer(userLocationMarker);
      }

      if (userLocationCircle && map) {
        map.removeLayer(userLocationCircle);
      }

      // Create new marker for user location
      userLocationMarker = L.marker([latitude, longitude], {
        icon: userIcon,
        zIndexOffset: 1000 // Ensure it's on top of other markers
      }).addTo(map);

      // Create accuracy circle
      userLocationCircle = L.circle([latitude, longitude], {
        radius: accuracy,
        color: '#4a90e2',
        fillColor: '#4a90e280',
        fillOpacity: 0.2,
        weight: 1
      }).addTo(map);

      // Pan to user location
      map.setView([latitude, longitude], 15);

      // Hide loading state
      isLoading.value = false;
    },
    (error) => {
      // Error callback
      console.error('Geolocation error:', error);

      switch (error.code) {
        case error.PERMISSION_DENIED:
          userLocationError.value = 'Permiso de ubicación denegado';
          break;
        case error.POSITION_UNAVAILABLE:
          userLocationError.value = 'Información de ubicación no disponible';
          break;
        case error.TIMEOUT:
          userLocationError.value = 'Tiempo de espera agotado para obtener la ubicación';
          break;
        default:
          userLocationError.value = 'Error desconocido al obtener la ubicación';
      }

      userLocationActive.value = false;
      isLoading.value = false;
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    }
  );
};

// Toggle radius function
const toggleRadius = () => {
  // Change the radius
  if (radiusKm.value === 30) {
    radiusKm.value = 50;
  } else if (radiusKm.value === 50) {
    radiusKm.value = 100;
  } else {
    radiusKm.value = 30;
  }

  // Update the map if it's loaded
  if (map && !loadError.value) {
    // Adjust the map zoom based on the radius
    const zoomLevels = {
      30: 7,
      50: 6,
      100: 5
    };
    map.setZoom(zoomLevels[radiusKm.value as keyof typeof zoomLevels]);
  }
};

// We're using SPAIN_CENTER from leafletLoader.ts

// Function to fetch locations from the API
const fetchLocations = async () => {
  try {
    // Use the API service to fetch locations
    const data = await apiService.locations.getAll();

    // Transform the API data to match our map marker format
    apiLocations.value = data.data.map((item: any) => {
      // Determine color based on location type
      let color = '#22d3ee'; // Default cyan

      if (item.type === 'hotel') {
        color = '#4ade80'; // Green
      } else if (item.type === 'restaurant') {
        color = '#f97316'; // Orange
      } else if (item.type === 'museum') {
        color = '#8b5cf6'; // Purple
      }

      return {
        id: item.id,
        lat: parseFloat(item.latitude),
        lng: parseFloat(item.longitude),
        color: color,
        name: item.name,
        description: item.description,
        address: item.address,
        type: item.type,
        image: item.image
      };
    });
  } catch (err) {
    console.error('Error fetching locations:', err);
    apiError.value = 'Failed to load locations. Using default data.';

    // Fallback to sample data if API fails
    apiLocations.value = [
      { id: 1, lat: 43.3614, lng: -5.8593, color: '#22d3ee', name: 'Asturias Point', description: 'A beautiful location in Asturias', address: 'Asturias, Spain', type: 'point' },
      { id: 2, lat: 43.2627, lng: -2.9253, color: '#22d3ee', name: 'Bilbao Point', description: 'A beautiful location in Bilbao', address: 'Bilbao, Spain', type: 'point' },
      { id: 3, lat: 41.6488, lng: -0.8891, color: '#22d3ee', name: 'Zaragoza Point', description: 'A beautiful location in Zaragoza', address: 'Zaragoza, Spain', type: 'point' },
      { id: 4, lat: 39.4699, lng: -0.3763, color: '#22d3ee', name: 'Valencia Point', description: 'A beautiful location in Valencia', address: 'Valencia, Spain', type: 'point' },
      { id: 5, lat: 38.9954, lng: -1.8585, color: '#22d3ee', name: 'Albacete Point', description: 'A beautiful location in Albacete', address: 'Albacete, Spain', type: 'point' },
      { id: 6, lat: 39.4752, lng: -6.3729, color: '#22d3ee', name: 'Cáceres Point', description: 'A beautiful location in Cáceres', address: 'Cáceres, Spain', type: 'point' },
      { id: 7, lat: 37.3886, lng: -5.9826, color: '#22d3ee', name: 'Sevilla Point', description: 'A beautiful location in Sevilla', address: 'Sevilla, Spain', type: 'point' },
      { id: 8, lat: 35.8883, lng: -5.3162, color: '#4ade80', name: 'Ceuta Point', description: 'A beautiful location in Ceuta', address: 'Ceuta, Spain', type: 'hotel' }
    ];
  }
};

// Initialize map
onMounted(async () => {
  console.log('MapView mounted');
  isLoading.value = true;
  loadError.value = false;

  try {
    // Fetch locations from API
    console.log('Fetching locations...');
    await fetchLocations();

    // Wait for the next tick to ensure the DOM is updated
    await nextTick();
    console.log('DOM updated, initializing map...');

    // Initialize map
    initMap();

    // Set loading to false after a short delay to ensure map has time to render
    setTimeout(() => {
      isLoading.value = false;
      console.log('Map loading complete');
    }, 500);
  } catch (error) {
    console.error('Error initializing map:', error);
    isLoading.value = false;
    loadError.value = true;
  }
});

// Clean up when component is unmounted
onBeforeUnmount(() => {
  // Clear markers array
  markers = [];

  // Clean up marker cluster group
  if (markerCluster) {
    markerCluster.clearLayers();
    markerCluster = null;
  }

  // Clean up user location marker and circle
  if (userLocationMarker && map) {
    map.removeLayer(userLocationMarker);
    userLocationMarker = null;
  }

  if (userLocationCircle && map) {
    map.removeLayer(userLocationCircle);
    userLocationCircle = null;
  }

  // Remove map
  if (map) {
    map.remove();
    map = null;
  }

  // Reset state
  userLocationActive.value = false;
  userLocationError.value = null;
});

// Initialize the map
const initMap = () => {
  console.log('Initializing map...');
  if (!mapContainer.value) {
    console.error('Map container not found!');
    return;
  }

  try {
    // Initialize Leaflet
    const L = initLeaflet();
    console.log('Leaflet initialized');

    // Create the map
    map = L.map('map', {
      center: [SPAIN_CENTER.lat, SPAIN_CENTER.lng],
      zoom: DEFAULT_ZOOM,
      zoomControl: false, // Disable default zoom controls, we'll add custom positioned ones
      attributionControl: true
    });
    console.log('Map created', map);

    // Add zoom control in a better position (bottom right)
    L.control.zoom({
      position: 'bottomright'
    }).addTo(map);

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(map);
    console.log('Tile layer added');

    // Create a marker cluster group
    markerCluster = createMarkerClusterGroup();

    // Add the cluster group to the map
    if (map) {
      markerCluster.addTo(map);
    }

    // Add markers from API data
    if (apiLocations.value.length > 0) {
      console.log(`Adding ${apiLocations.value.length} markers to cluster group`);

      // Create markers and add them to the cluster group
      apiLocations.value.forEach(location => {
        // Create a custom marker icon based on location type
        const markerIcon = createMarkerIcon(location.color);

        // Create the marker
        const marker = L.marker([location.lat, location.lng], {
          icon: markerIcon
        });

        // Store the marker for later reference
        markers.push(marker);

        // Create popup content
        const popupContent = `
          <div class="marker-info-window">
            <h3>${location.name}</h3>
            <p>${location.description.substring(0, 50)}${location.description.length > 50 ? '...' : ''}</p>
            <p><strong>Dirección:</strong> ${location.address.substring(0, 30)}${location.address.length > 30 ? '...' : ''}</p>
            <p><strong>Tipo:</strong> ${location.type.charAt(0).toUpperCase() + location.type.slice(1)}</p>
            <button class="view-details-btn">Ver detalles</button>
          </div>
        `;

        // Create and bind the popup
        const popup = L.popup({
          maxWidth: 300,
          className: 'custom-popup'
        }).setContent(popupContent);

        marker.bindPopup(popup);

        // Add click event to marker
        marker.on('click', () => {
          console.log('Marker clicked:', location);
        });

        // Add click event to the "Ver detalles" button
        marker.on('popupopen', () => {
          setTimeout(() => {
            const button = document.querySelector('.view-details-btn');
            if (button) {
              button.addEventListener('click', () => {
                // Navigate to experience details with the location ID
                router.push(`/experiencias/${location.id}`);
                popup.close();
              });
            }
          }, 100);
        });

        // Add the marker to the cluster group instead of directly to the map
        if (markerCluster) {
          markerCluster.addLayer(marker);
        }
      });

      console.log('All markers added to cluster group');
    } else {
      console.log('No API locations to add as markers');
    }

    // Force a map resize after initialization to fix any display issues
    setTimeout(() => {
      if (map) {
        console.log('Invalidating map size...');
        map.invalidateSize();
      }
    }, 100);
  } catch (error) {
    console.error('Error in map initialization:', error);
    loadError.value = true;
  }
};
</script>

<style scoped>
.map-view {
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Custom styles for the map header */
.map-header :deep(.header-left) {
  width: 0; /* Reduce width to give more space for title */
}

.map-header :deep(.title-container) {
  width: auto;
  max-width: 120px;
  margin-left: -40px; /* Move title even more to the left */
  z-index: 1;
  position: relative;
  left: 0;
  justify-content: flex-start;
  padding-left: 0;
}

.map-header :deep(.header-actions) {
  width: auto;
  min-width: 240px;
  z-index: 2;
  display: flex;
  justify-content: flex-end;
}

.title-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: auto;
  justify-content: flex-start;
}

.title-with-icon span {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334960;
}

.header-icon {
  font-size: 1.5rem;
  color: #334960;
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: space-between;
  width: 100%;
  max-width: 240px; /* Adjust as needed to control spacing */
}

.radius-button {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 0.4rem 1rem;
  font-size: 0.9rem;
  color: #334960;
  font-weight: 500;
  white-space: nowrap;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-round-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  color: #334960;
  font-size: 1rem;
  cursor: pointer;
}

.favorite-button i {
  color: #597694;
  font-size: 1.2rem;
}

.filter-button i {
  color: #597694;
  font-size: 1rem;
}

.back-button i {
  color: #597694;
  font-size: 1rem;
}

.map-container {
  height: calc(100vh - 60px); /* Adjust based on your header height */
  width: 100%;
  background-color: #f0f0f0; /* Fallback color */
  position: relative;
  z-index: 1;
}

.leaflet-map {
  height: 100% !important;
  width: 100% !important;
  z-index: 1;
  position: absolute !important;
  top: 0;
  left: 0;
}

.map-tools {
  position: absolute;
  top: calc(50% + 45px); /* Adjust for header (half of 90px) */
  left: 1rem;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  z-index: 10;
}

.tool-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  color: #334960;
  font-size: 1.1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-layers {
  position: absolute;
  bottom: 2rem;
  left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  z-index: 10;
}

.layer-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  color: #334960;
  font-size: 1.1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-placeholder, .map-loading, .map-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  text-align: center;
  padding: 2rem;
  background-color: #f5f5f5;
  z-index: 5;
}

.fallback-map {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  margin: 0;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  position: absolute;
  top: 0;
  left: 0;
}

.error-message {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 10px 20px;
  text-align: center;
  z-index: 6;
}

.error-message p {
  margin: 0;
  font-size: 14px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(51, 73, 96, 0.2);
  border-radius: 50%;
  border-top-color: #334960;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>

<style>
/* Global styles for Leaflet map */
.custom-popup .leaflet-popup-content-wrapper {
  border-radius: 8px;
  padding: 0;
  overflow: hidden;
  max-width: 250px;
  box-shadow: 0 3px 14px rgba(0,0,0,0.2);
}

.custom-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
  width: 100% !important;
}

.marker-info-window {
  padding: 10px;
}

.marker-info-window h3 {
  margin: 0 0 5px 0;
  font-size: 15px;
  color: #334960;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.marker-info-window p {
  margin: 3px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.3;
}

.view-details-btn {
  background-color: #334960;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  margin-top: 6px;
  width: 100%;
  transition: background-color 0.2s;
}

.view-details-btn:hover {
  background-color: #4a6583;
}

/* Fix for popup tip position */
.custom-popup .leaflet-popup-tip {
  background-color: white;
}

/* Adjust zoom control styles */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
}

.leaflet-control-zoom a {
  background-color: white !important;
  color: #334960 !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 16px !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f5f5f5 !important;
}

/* Custom marker cluster styles */
.marker-cluster-custom {
  background: transparent;
  border: none;
}

/* Override default marker cluster styles */
.leaflet-marker-icon.leaflet-div-icon.marker-cluster-custom {
  background-color: transparent !important;
  border: none !important;
}

/* Animation for marker clusters */
.marker-cluster-custom div {
  transition: all 0.3s ease;
}

.marker-cluster-custom div:hover {
  transform: scale(1.05);
}

/* User location marker styles */
.user-location-marker {
  background: transparent;
  border: none;
}

.user-dot {
  width: 24px;
  height: 24px;
  background-color: #4a90e2;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  position: relative;
  animation: pulse 2s infinite;
}

.tool-button.active {
  background-color: #334960;
  color: white;
}

.tool-button.active i {
  color: white;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

.custom-map-marker {
  background: transparent;
  border: none;
}
</style>
