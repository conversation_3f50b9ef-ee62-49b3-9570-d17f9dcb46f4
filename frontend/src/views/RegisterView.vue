<template>
  <div class="login-screen registration-screen">
    <div class="login-content">
      <div class="login-logo-container">
        <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience" class="login-logo" />
        <p class="tagline">Crear una cuenta nueva</p>
      </div>

      <form @submit.prevent="submitRegistration" class="login-form">
        <div v-if="registerError" class="error-message">
          {{ registerError }}
        </div>

        <div class="input-group">
          <input
            type="text"
            v-model="name"
            placeholder="Nombre completo"
            class="username-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="email"
            v-model="email"
            placeholder="Correo electrónico"
            class="username-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="password"
            v-model="password"
            placeholder="Contraseña"
            class="password-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="password"
            v-model="passwordConfirmation"
            placeholder="Confirmar contraseña"
            class="password-input"
            required
          />
        </div>

        <button type="submit" class="login-button" :disabled="isLoading">
          {{ isLoading ? 'Cargando...' : 'Registrarse' }}
        </button>

        <button type="button" class="register-button" @click="backToLogin">
          Volver al inicio de sesión
        </button>
      </form>

      <p class="terms-text">
        Al registrarte, aceptas nuestros <span class="highlight">Términos y Condiciones</span> y nuestra <span class="highlight">Política de Privacidad</span>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { register as authRegister } from '@/services/auth'

const router = useRouter()
const name = ref('')
const email = ref('')
const password = ref('')
const passwordConfirmation = ref('')
const isLoading = ref(false)
const registerError = ref('')

const submitRegistration = async () => {
  if (!name.value || !email.value || !password.value || !passwordConfirmation.value) {
    registerError.value = 'Por favor, completa todos los campos';
    return;
  }

  if (password.value !== passwordConfirmation.value) {
    registerError.value = 'Las contraseñas no coinciden';
    return;
  }

  try {
    isLoading.value = true;
    registerError.value = '';

    // Call the authentication service to register
    await authRegister(name.value, email.value, password.value, passwordConfirmation.value);

    // Check for post-login redirect
    const redirectPath = localStorage.getItem('post_login_redirect');
    if (redirectPath) {
      localStorage.removeItem('post_login_redirect');
      router.push(redirectPath);
    } else {
      // If successful, navigate to home
      router.push('/inicio');
    }
  } catch (error) {
    if (error instanceof Error) {
      registerError.value = error.message;
    } else {
      registerError.value = 'Error al registrarse. Por favor, inténtalo de nuevo.';
    }
    console.error('Registration error:', error);
  } finally {
    isLoading.value = false;
  }
}

const backToLogin = () => {
  router.push('/login');
}
</script>

<style scoped>
/* Registration Screen Styles - inherits from login screen */
.login-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
  max-width: 100vw;
  min-height: 100vh;
  overflow-y: auto;
  background-image:
    linear-gradient(to bottom,
      rgba(238, 174, 143, 0.5) 0%,
      rgba(220, 137, 96, 0.8) 40%,
      rgba(154, 82, 46, 0.95) 100%),
    url('/assets/backgrounds/backgroundLogin.png');
  background-size: cover;
  background-position: center;
  background-color: #9A522E; /* Fallback color */
}

.login-content {
  width: 100%;
  max-width: 320px;
  padding: 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.login-logo-container {
  margin-bottom: 2rem;
}

.login-logo {
  max-width: 80%;
  max-height: 80px;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
  opacity: 0.95;
}

.tagline {
  color: white;
  font-size: 1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  font-weight: 500;
  margin: 0;
}

.login-form {
  width: 100%;
  margin-bottom: 1rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

.username-input, .password-input {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #333;
  box-sizing: border-box;
}

.username-input:focus, .password-input:focus {
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.error-message {
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  text-align: center;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: 0.5rem;
}

.login-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.register-button {
  width: 100%;
  padding: 0.75rem;
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.register-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.terms-text {
  font-size: 0.75rem;
  color: white;
  line-height: 1.5;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  margin-top: 1.5rem;
  max-width: 90%;
}

.highlight {
  font-weight: bold;
}

/* Responsive styles */
@media (min-width: 768px) {
  .login-content {
    padding: 2rem;
    max-width: 360px;
  }

  .login-logo {
    max-height: 100px;
  }

  .terms-text {
    font-size: 0.8rem;
    margin-top: 2.5rem;
  }
}
</style>
