<template>
  <div class="guide-dashboard">
    <!-- Header with Agency Branding -->
    <AppHeader
      :title="agencyConfig?.name || 'Panel de Guía'"
      :showBackButton="false"
      :showMenuButton="true"
      :showLogo="true"
      :logoSrc="agencyConfig?.logo || '/assets/logos/CorkExpLogoBlack.png'"
      @toggleMenu="toggleSidebar"
    />

    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando panel de guía...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadDashboard" class="retry-button">Reintentar</button>
      </div>

      <!-- Dashboard Content -->
      <div v-else class="dashboard-content">
        <!-- Welcome Section -->
        <div class="welcome-section" :style="{ backgroundColor: agencyConfig?.primary_color || '#2c5aa0' }">
          <h2>Bienvenido, {{ currentUser?.name }}</h2>
          <p>{{ agencyConfig?.welcome_message || 'Gestiona tus experiencias y grupos desde aquí' }}</p>
        </div>

        <!-- Quick Stats -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="pi pi-calendar"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStats?.total_experiences || 0 }}</h3>
              <p>Experiencias</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="pi pi-users"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStats?.total_groups || 0 }}</h3>
              <p>Grupos</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="pi pi-bookmark"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStats?.active_reservations || 0 }}</h3>
              <p>Reservas Activas</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="pi pi-star"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStats?.average_rating || '0.0' }}</h3>
              <p>Valoración Media</p>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3>Acciones Rápidas</h3>
          <div class="action-buttons">
            <button
              @click="navigateTo('/guia/experiencias')"
              class="action-button experiences-btn"
              :style="{ backgroundColor: agencyConfig?.secondary_color || '#f39c12' }"
            >
              <i class="pi pi-calendar"></i>
              <span>Gestionar Experiencias</span>
            </button>

            <button
              @click="navigateTo('/guia/grupos')"
              class="action-button groups-btn"
              :style="{ backgroundColor: agencyConfig?.secondary_color || '#f39c12' }"
            >
              <i class="pi pi-users"></i>
              <span>Gestionar Grupos</span>
            </button>

            <button
              @click="navigateTo('/guia/configuracion')"
              class="action-button config-btn"
              :style="{ backgroundColor: agencyConfig?.secondary_color || '#f39c12' }"
            >
              <i class="pi pi-cog"></i>
              <span>Configuración</span>
            </button>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
          <h3>Actividad Reciente</h3>
          <div v-if="dashboardStats?.recent_activities?.length" class="activity-list">
            <div
              v-for="activity in dashboardStats.recent_activities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <i :class="getActivityIcon(activity.type)"></i>
              </div>
              <div class="activity-content">
                <p class="activity-description">{{ activity.description }}</p>
                <span class="activity-time">{{ formatTime(activity.created_at) }}</span>
              </div>
            </div>
          </div>
          <div v-else class="no-activity">
            <p>No hay actividad reciente</p>
          </div>
        </div>
      </div>
    </main>

    <!-- Toast Notifications -->
    <Toast v-if="toast.show" :message="toast.message" :type="toast.type" @close="hideToast" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';
import SidebarMenu from '../components/SidebarMenu.vue';
import Toast from '../components/ui/Toast.vue';
import apiService from '../services/api';
import { currentUser, isAuthenticated } from '../services/auth';

const router = useRouter();

// Reactive state
const loading = ref(true);
const error = ref<string | null>(null);
const isSidebarOpen = ref(false);
const dashboardStats = ref<any>(null);
const agencyConfig = ref<any>(null);

// Toast state
const toast = ref({
  show: false,
  message: '',
  type: 'info' as 'success' | 'error' | 'warning' | 'info'
});

// Check if user is a guide
const isGuide = computed(() => {
  return currentUser.value?.role === 'guide' ||
         currentUser.value?.groups?.some(group => group.is_guide);
});

// Methods
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

const navigateTo = (path: string) => {
  router.push(path);
};

const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  toast.value = { show: true, message, type };
};

const hideToast = () => {
  toast.value.show = false;
};

const getActivityIcon = (type: string): string => {
  const icons: Record<string, string> = {
    'experience_created': 'pi pi-plus-circle',
    'experience_updated': 'pi pi-pencil',
    'group_created': 'pi pi-users',
    'group_updated': 'pi pi-user-edit',
    'reservation_created': 'pi pi-bookmark',
    'reservation_updated': 'pi pi-bookmark-fill',
    'default': 'pi pi-info-circle'
  };
  return icons[type] || icons.default;
};

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) {
    return 'Hace menos de una hora';
  } else if (diffInHours < 24) {
    return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`;
  }
};

const loadDashboard = async () => {
  try {
    loading.value = true;
    error.value = null;

    // Check authentication and guide role
    if (!isAuthenticated.value) {
      router.push('/home');
      return;
    }

    if (!isGuide.value) {
      error.value = 'No tienes permisos para acceder al panel de guía';
      return;
    }

    // Load dashboard data in parallel
    const [statsResponse, profileResponse] = await Promise.all([
      apiService.guide.getDashboardStats(),
      apiService.guide.getGuideProfile()
    ]);

    dashboardStats.value = statsResponse.data;
    agencyConfig.value = profileResponse.data;

  } catch (err: any) {
    console.error('Error loading guide dashboard:', err);
    error.value = err.message || 'Error al cargar el panel de guía';
    showToast('Error al cargar los datos del panel', 'error');
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(() => {
  loadDashboard();
});
</script>

<style scoped>
.guide-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.main-content {
  padding: 1rem;
  margin-top: 60px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container i {
  font-size: 2rem;
  color: #2c5aa0;
  margin-bottom: 1rem;
}

.error-container i {
  font-size: 2rem;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #2c5aa0;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

.welcome-section {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  text-align: center;
}

.welcome-section h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.welcome-section p {
  margin: 0;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 8px;
  color: #2c5aa0;
}

.stat-icon i {
  font-size: 1.25rem;
}

.stat-content h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c5aa0;
}

.stat-content p {
  margin: 0;
  color: #6c757d;
  font-size: 0.875rem;
}

.quick-actions,
.recent-activity {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.quick-actions h3,
.recent-activity h3 {
  margin: 0 0 1rem 0;
  color: #2c5aa0;
  font-weight: 600;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  font-weight: 500;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-button i {
  font-size: 1.25rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-icon {
  background: #2c5aa0;
  color: white;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.activity-content {
  flex: 1;
}

.activity-description {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
}

.activity-time {
  color: #6c757d;
  font-size: 0.875rem;
}

.no-activity {
  text-align: center;
  color: #6c757d;
  padding: 2rem;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .welcome-section {
    padding: 1.5rem;
  }

  .welcome-section h2 {
    font-size: 1.25rem;
  }
}
</style>
