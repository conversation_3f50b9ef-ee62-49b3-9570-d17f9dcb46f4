<template>
  <div class="event-detail-view">
    <!-- Sidebar Menu (reused from HomeView) -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Agenda"
      :showFavoriteButton="true"
      :showBackButton="true"
      @toggleMenu="toggleSidebar"
      @goBack="goBack"
    />

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <p>Cargando evento...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button class="primary-button" @click="goBack">Volver</button>
    </div>

    <!-- Event Content -->
    <div v-else-if="event" class="event-content">
      <!-- Event Image Banner -->
      <div class="event-banner">
        <img :src="event.image" :alt="event.title" class="event-banner-image" />
      </div>

      <!-- Event Details -->
      <div class="event-details">
        <h1 class="event-title">{{ event.title }}</h1>
        <p class="event-description">{{ event.description }}</p>

        <!-- Event Info -->
        <div class="event-info">
          <div class="info-item" v-if="event.phone">
            <i class="pi pi-phone"></i>
            <span>{{ event.phone }}</span>
          </div>
          <div class="info-item">
            <i class="pi pi-calendar"></i>
            <span>{{ event.year }}</span>
          </div>
          <div class="info-item">
            <i class="pi pi-map-marker"></i>
            <span>{{ event.location }}</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button class="action-button agency-button">Agencia</button>
          <button class="action-button reserve-button">Reserva</button>
        </div>
      </div>
    </div>

    <!-- No Event Found -->
    <div v-else class="error-container">
      <p>No se encontró información del evento</p>
      <button class="primary-button" @click="goBack">Volver</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import apiService from '../services/api';

// State for sidebar
const isSidebarOpen = ref(false);

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

const router = useRouter();
const route = useRoute();

const goBack = () => {
  router.back();
};

// Define event interface
interface EventDetail {
  id: number;
  title: string;
  description: string;
  image: string;
  phone?: string;
  year: string;
  location: string;
  startTime: string;
  endTime: string;
  dateTime: string;
}

// State for event data
const event = ref<EventDetail | null>(null);
const loading = ref<boolean>(true);
const error = ref<string | null>(null);

// Format date and time
const formatDate = (dateString: string | null): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', { year: 'numeric', month: 'long', day: 'numeric' });
};

const formatTime = (dateString: string | null): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' });
};

// Get image URL with proper path
const getImageUrl = (path: string | null): string => {
  if (!path) return '/assets/images/placeholder.png';
  return path.startsWith('http') || path.startsWith('/') ? path : `/assets/images/agenda/${path}`;
};

onMounted(async () => {
  try {
    loading.value = true;
    error.value = null;
    const eventId = route.params.id as string;

    // Fetch event data from API
    const response = await apiService.events.getById(eventId);

    if (response && response.data && response.data.length > 0) {
      const eventData = response.data[0];

      // Transform API data to match our component's structure
      event.value = {
        id: eventData.id,
        title: eventData.title,
        description: eventData.description,
        image: getImageUrl(eventData.image),
        // Add phone if available (not in the EventItem interface but useful for display)
        phone: 'No disponible', // Default value since it's not in the API response
        year: formatDate(eventData.start_datetime),
        location: eventData.location,
        startTime: formatTime(eventData.start_datetime),
        endTime: formatTime(eventData.end_datetime),
        dateTime: `${formatDate(eventData.start_datetime)} ${formatTime(eventData.start_datetime)} - ${formatTime(eventData.end_datetime)}`
      };
    } else {
      error.value = 'No se pudo encontrar el evento';
    }
  } catch (err) {
    console.error('Error fetching event:', err);
    error.value = 'Error al cargar el evento. Por favor, inténtelo de nuevo más tarde.';
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.event-detail-view {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.primary-button {
  background-color: #334960;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 1rem;
}

.event-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.event-banner {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.event-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-details {
  padding: 1.5rem;
  flex: 1;
}

.event-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #000;
  margin: 0 0 0.75rem 0;
}

.event-description {
  font-size: 0.9rem;
  color: #333;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.event-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: #333;
}

.info-item i {
  color: #597694;
  font-size: 1.2rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.action-button {
  flex: 1;
  padding: 0.75rem 0;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  border: none;
}

.agency-button {
  background-color: #334960;
  color: white;
}

.reserve-button {
  background-color: #334960;
  color: white;
}

/* Browser Bar (for design purposes only) */
.browser-bar {
  background-color: #f2f2f2;
  padding: 0.5rem 1rem;
  border-top: 1px solid #d0d0d0;
}

.browser-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #e0e0e0;
  border-radius: 8px;
  padding: 0.25rem 0.75rem;
  margin-bottom: 0.5rem;
}

.text-size, .reader-mode, .refresh {
  color: #666;
  font-size: 0.8rem;
}

.url {
  color: #333;
  font-size: 0.8rem;
  flex: 1;
  text-align: center;
}

.browser-navigation {
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
}

.nav-button {
  color: #0080ff;
  font-size: 1.2rem;
}
</style>
