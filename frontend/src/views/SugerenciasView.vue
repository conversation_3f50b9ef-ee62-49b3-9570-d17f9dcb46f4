<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import AppHeader from '@/components/layout/AppHeader.vue'

const router = useRouter()

// No sidebar state needed

// Sample data for suggestions
const suggestions = ref([
  {
    title: 'Ropa',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Consectetur blandit nisi.',
    items: [
      'Pantalones cortos o mallas acolchadas',
      'Maillot o camiseta de ciclista',
      'Maillot o camisetas',
      'Guantes',
      'Zapatillas de entrenamiento / Zapatillas casuales',
      'Chanclas / Crocs',
      'Medias',
      'Ropa casual para el clima primaveral',
      'Traje de baño y toalla de playa',
      'Chaqueta abrigada'
    ]
  },
  {
    title: 'No olvides traer',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Consectetur blandit nisi.',
    items: [
      'Protección para los ojos (gafas de sol, lentes transparentes)',
      'También se recomienda usar crema solar y bálsamo labial',
      'Cargador de teléfono y cámara',
      'Adaptador eléctrico europeo'
    ]
  }
])
</script>

<template>
  <div class="sugerencias-view">
    <!-- No Sidebar Menu needed -->

    <!-- Header -->
    <AppHeader
      title="Sugerencias"
      :showBackButton="true"
      :showFavoriteButton="true"
      :showLogo="false"
      @goBack="router.back()"
    />

    <!-- Hero Image -->
    <div class="hero-image-container">
      <img src="/assets/images/operators/suggestions.jpeg" alt="Sugerencias" class="hero-image" />
    </div>

    <!-- Suggestions Content -->
    <div class="suggestions-content">
      <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-section">
        <h2 class="suggestion-title">{{ suggestion.title }}</h2>
        <p class="suggestion-description">{{ suggestion.description }}</p>

        <div class="suggestion-items">
          <div v-for="(item, itemIndex) in suggestion.items" :key="itemIndex" class="suggestion-item">
            <p class="item-text">{{ item }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- No browser bar as it's just part of the Figma mockup -->
  </div>
</template>

<style scoped>
.sugerencias-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* Hero Image Styles */
.hero-image-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Suggestions Content Styles */
.suggestions-content {
  padding: 1.5rem 1rem;
  flex: 1;
}

.suggestion-section {
  margin-bottom: 2rem;
}

.suggestion-title {
  color: #334960;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.suggestion-description {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.suggestion-items {
  border-left: 2px solid #8BA8C7;
  padding-left: 1rem;
  margin-left: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  position: relative;
  padding-bottom: 1rem;
}

.suggestion-item:not(:last-child) {
  border-bottom: 1px solid rgba(139, 168, 199, 0.15);
}

.item-text {
  color: #334960;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

/* Browser bar styles removed as it's just part of the Figma mockup */
</style>
