<template>
  <div class="login-screen">
    <div class="login-content">
      <div class="login-logo-container">
        <img src="/assets/logos/CorkExpLogo.png" alt="Cork Experience" class="login-logo" />
        <p class="tagline">El corcho, un mundo por descubrir</p>
      </div>

      <form @submit.prevent="login" class="login-form">
        <div v-if="loginError" class="error-message">
          {{ loginError }}
        </div>

        <div class="input-group">
          <input
            type="text"
            v-model="username"
            placeholder="Usuario o Email"
            class="username-input"
            required
          />
        </div>

        <div class="input-group">
          <input
            type="password"
            v-model="password"
            placeholder="Contraseña"
            class="password-input"
            required
          />
        </div>

        <button type="submit" class="login-button" :disabled="isLoading">
          {{ isLoading ? 'Cargando...' : 'Iniciar sesión' }}
        </button>

        <button type="button" class="register-button" @click="goToRegister">
          Registrarse
        </button>

        <button type="button" class="guest-button" @click="continueAsGuest">
          Continuar como invitado
        </button>

        <div class="divider">
          <span class="divider-text">o</span>
        </div>

        <div class="social-login">
          <button type="button" class="social-button google-button" @click="loginWithGoogle">
            <i class="pi pi-google"></i>
            <span>Continuar con Google</span>
          </button>

          <button type="button" class="social-button apple-button" @click="loginWithApple">
            <i class="pi pi-apple"></i>
            <span>Continuar con Apple</span>
          </button>
        </div>
      </form>

      <p class="terms-text">
        Por favor <span class="highlight">revise su bandeja de entrada</span> de correo electrónico<br>para encontrar su nombre de usuario
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { login as authLogin, enterGuestMode } from '@/services/auth'

const router = useRouter()
const username = ref('')
const password = ref('')
const isLoading = ref(false)
const loginError = ref('')

const login = async () => {
  if (!username.value || !password.value) {
    loginError.value = 'Por favor, introduce tu nombre de usuario y contraseña';
    return;
  }

  try {
    isLoading.value = true;
    loginError.value = '';

    console.log('Login attempt in LoginView with:', { username: username.value });

    // Call the authentication service to login
    // We're using the username field for either username or email
    await authLogin(username.value, password.value);

    console.log('Login successful, navigating to home');

    // Check for post-login redirect
    const redirectPath = localStorage.getItem('post_login_redirect');
    if (redirectPath) {
      localStorage.removeItem('post_login_redirect');
      router.push(redirectPath);
    } else {
      // If successful, navigate to home
      router.push('/inicio');
    }
  } catch (error) {
    console.error('Login error in LoginView:', error);

    if (error instanceof Error) {
      loginError.value = error.message;
    } else {
      loginError.value = 'Error al iniciar sesión. Por favor, inténtalo de nuevo.';
    }
  } finally {
    isLoading.value = false;
  }
}

const goToRegister = () => {
  router.push('/registro');
}

const continueAsGuest = () => {
  // Enter guest mode and navigate to home
  enterGuestMode()
  router.push('/inicio')
}

const loginWithGoogle = () => {
  // This would typically handle Google OAuth authentication
  console.log('Login with Google clicked')
  // For now, we'll just show an alert
  alert('Google login will be implemented in the future')
}

const loginWithApple = () => {
  // This would typically handle Apple OAuth authentication
  console.log('Login with Apple clicked')
  // For now, we'll just show an alert
  alert('Apple login will be implemented in the future')
}
</script>

<style scoped>
/* Login Screen Styles */
.login-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
  max-width: 100vw;
  min-height: 100vh;
  overflow-y: auto;
  background-image:
    linear-gradient(to bottom,
      rgba(238, 174, 143, 0.5) 0%,
      rgba(220, 137, 96, 0.8) 40%,
      rgba(154, 82, 46, 0.95) 100%),
    url('/assets/backgrounds/backgroundLogin.png');
  background-size: cover;
  background-position: center;
  background-color: #9A522E; /* Fallback color */
}

.login-content {
  width: 100%;
  max-width: 320px;
  padding: 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.login-logo-container {
  margin-bottom: 2rem;
}

.login-logo {
  max-width: 80%;
  max-height: 80px;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
  opacity: 0.95;
}

.tagline {
  color: white;
  font-size: 1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  font-weight: 500;
  margin: 0;
}

.login-form {
  width: 100%;
  margin-bottom: 1rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

.username-input, .password-input {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #333;
  box-sizing: border-box;
}

.username-input:focus, .password-input:focus {
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.error-message {
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  text-align: center;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: 0.5rem;
}

.login-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.register-button, .guest-button {
  width: 100%;
  padding: 0.75rem;
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.register-button:hover, .guest-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.guest-button {
  background-color: rgba(255, 255, 255, 0.1);
  margin-top: 0.5rem;
}

.divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  width: 100%;
  position: relative;
}

.divider::before, .divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.4);
}

.divider-text {
  padding: 0 0.75rem;
  color: white;
  font-size: 0.9rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.social-login {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.social-button i {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

.google-button {
  background-color: white;
  color: #444;
}

.google-button:hover {
  background-color: #f8f8f8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.apple-button {
  background-color: black;
  color: white;
}

.apple-button:hover {
  background-color: #222;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.terms-text {
  font-size: 0.75rem;
  color: white;
  line-height: 1.5;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  margin-top: 1.5rem;
  max-width: 90%;
}

.highlight {
  font-weight: bold;
}

/* Responsive styles */
@media (min-width: 768px) {
  .login-content {
    padding: 2rem;
    max-width: 360px;
  }

  .login-logo {
    max-height: 100px;
  }

  .terms-text {
    font-size: 0.8rem;
    margin-top: 2.5rem;
  }

  .social-button {
    padding: 0.85rem;
    font-size: 1rem;
  }

  .social-button i {
    font-size: 1.2rem;
  }

  .divider {
    margin: 1.75rem 0;
  }

  .divider-text {
    font-size: 1rem;
  }
}
</style>
