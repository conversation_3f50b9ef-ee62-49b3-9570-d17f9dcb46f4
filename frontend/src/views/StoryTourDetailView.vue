<template>
  <div class="storytour-detail-view">
    <!-- Sidebar Menu -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="StoryTour"
      :showBackButton="true"
      :showMenuButton="true"
      :leftAlignedTitle="true"
      @goBack="goBack"
      @toggleMenu="toggleSidebar"
    />

    <!-- Main Content -->
    <main class="main-content">
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner"></i>
        <p>Cargando StoryTour...</p>
      </div>
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button class="primary-button" @click="goBack">Volver</button>
      </div>
      <div v-else-if="!storyTour" class="error-container">
        <i class="pi pi-info-circle"></i>
        <p>No se encontró información del StoryTour</p>
        <button class="primary-button" @click="goBack">Volver</button>
      </div>
      <div v-else class="storytour-content">
        <!-- Hero Image -->
        <div class="hero-image-container">
          <img
            :src="storyTour.image || `/assets/images/storytours/${storyTour.type || 'default'}.png`"
            :alt="storyTour.title"
            class="hero-image"
            @error="handleImageError"
          />
        </div>

        <!-- StoryTour Info -->
        <div class="storytour-info">
          <h1 class="storytour-title">{{ storyTour.title }}</h1>

          <div class="storytour-metadata">
            <div v-if="storyTour.territory" class="metadata-item">
              <i class="pi pi-map-marker"></i>
              <span>{{ storyTour.territory }}</span>
            </div>
            <div v-if="storyTour.modality" class="metadata-item">
              <i class="pi pi-tag"></i>
              <span>{{ storyTour.modality }}</span>
            </div>
            <div v-if="storyTour.year" class="metadata-item">
              <i class="pi pi-calendar"></i>
              <span>{{ storyTour.year }}</span>
            </div>
            <div v-if="storyTour.location" class="metadata-item">
              <i class="pi pi-map"></i>
              <span>{{ storyTour.location.name }}</span>
            </div>
          </div>

          <div class="storytour-description">
            <p>{{ storyTour.description }}</p>
          </div>

          <!-- Media Buttons -->
          <div class="media-buttons">
            <button
              class="media-button audio-button"
              @click="storyTour.audio_file ? playAudio() : showMediaNotAvailable('audio')"
              :class="{ 'disabled': !storyTour.audio_file }"
            >
              <i class="pi pi-volume-up"></i>
              <span>Audio</span>
            </button>
            <button
              class="media-button video-button"
              @click="storyTour.video_file ? playVideo() : showMediaNotAvailable('video')"
              :class="{ 'disabled': !storyTour.video_file }"
            >
              <i class="pi pi-video"></i>
              <span>Video</span>
            </button>
            <button
              class="media-button ar-button"
              @click="storyTour.ar_model_file ? viewAR() : showMediaNotAvailable('ar')"
              :class="{ 'disabled': !storyTour.ar_model_file }"
            >
              <i class="pi pi-mobile"></i>
              <span>Realidad Aumentada</span>
            </button>
          </div>

          <!-- Media Players (hidden by default) -->
          <div v-if="showAudioPlayer && storyTour.audio_file" class="media-player audio-player">
            <h3>Audio</h3>
            <audio controls :src="storyTour.audio_file" class="audio-element"></audio>
          </div>

          <div v-if="showVideoPlayer && storyTour.video_file" class="media-player video-player">
            <h3>Video</h3>
            <video controls :src="storyTour.video_file" class="video-element"></video>
          </div>

          <div v-if="showARViewer && storyTour.ar_model_file" class="media-player ar-viewer">
            <h3>Realidad Aumentada</h3>
            <p>Para ver el contenido de Realidad Aumentada, escanea el código QR con tu dispositivo móvil.</p>
            <img src="/assets/images/QR.svg" alt="QR Code" class="qr-code-image" />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import SidebarMenu from '@/components/SidebarMenu.vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import apiService from '@/services/api'
import type { StoryTourItem } from '@/services/api'

// Sidebar state
const isSidebarOpen = ref(false)

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

const closeSidebar = () => {
  isSidebarOpen.value = false
}

// Router and route
const router = useRouter()
const route = useRoute()

// StoryTour data
const storyTour = ref<StoryTourItem | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// Media player states
const showAudioPlayer = ref(false)
const showVideoPlayer = ref(false)
const showARViewer = ref(false)

// Fetch StoryTour detail from API
const fetchStoryTourDetail = async () => {
  loading.value = true
  error.value = null

  try {
    const storyTourId = Number(route.params.id)
    const response = await apiService.storyTours.getById(storyTourId)

    if (response && response.data) {
      console.log('StoryTour detail:', response.data)

      // Fix image URLs if needed
      if (Array.isArray(response.data)) {
        if (response.data.length > 0) {
          storyTour.value = apiService.storyTours.fixImageUrls([response.data[0]])[0]
        } else {
          error.value = 'No se encontró información del StoryTour'
        }
      } else {
        storyTour.value = response.data

        // Fix image URL if needed
        if (storyTour.value && storyTour.value.image && typeof storyTour.value.image === 'string' &&
            storyTour.value.image.startsWith('http://localhost:8000/storage/')) {
          storyTour.value.image = storyTour.value.image.replace('http://localhost:8000/storage/', '/assets/')
        }
      }
    } else {
      error.value = 'No se pudo cargar la información del StoryTour'
    }
  } catch (err) {
    console.error('Error fetching StoryTour detail:', err)
    error.value = 'Error al cargar el StoryTour. Por favor, inténtalo de nuevo.'
  } finally {
    loading.value = false
  }
}

// Handle image loading errors
const handleImageError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  if (imgElement) {
    imgElement.src = '/assets/images/placeholder.png'
  }
}

// Media player functions
const playAudio = () => {
  showAudioPlayer.value = !showAudioPlayer.value
  showVideoPlayer.value = false
  showARViewer.value = false
}

const playVideo = () => {
  showVideoPlayer.value = !showVideoPlayer.value
  showAudioPlayer.value = false
  showARViewer.value = false
}

const viewAR = () => {
  showARViewer.value = !showARViewer.value
  showAudioPlayer.value = false
  showVideoPlayer.value = false
}

// Show message when media is not available
const showMediaNotAvailable = (mediaType: string) => {
  let mediaName = '';
  switch (mediaType) {
    case 'audio':
      mediaName = 'audio';
      break;
    case 'video':
      mediaName = 'video';
      break;
    case 'ar':
      mediaName = 'realidad aumentada';
      break;
    default:
      mediaName = 'contenido';
  }
  alert(`Este StoryTour no tiene ${mediaName} disponible.`);
}

// Navigation function
const goBack = () => {
  router.back()
}

// Fetch StoryTour detail on component mount
onMounted(() => {
  fetchStoryTourDetail()
})
</script>

<style scoped>
.storytour-detail-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.main-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #334960;
  height: 50vh;
}

.loading-container i,
.error-container i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.error-container i {
  color: #f44336;
}

.primary-button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: #E91E63;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-button:hover {
  background-color: #D81B60;
}

.hero-image-container {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.storytour-info {
  padding: 1.5rem;
  background-color: white;
  border-radius: 16px 16px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 2;
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
}

.storytour-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 1rem;
}

.storytour-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667085;
  font-size: 0.9rem;
}

.metadata-item i {
  color: #E91E63;
}

.storytour-description {
  margin-bottom: 1.5rem;
  color: #334960;
  line-height: 1.6;
}

.media-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.media-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: #f0f5fa;
  border: 1px solid #E4EBF2;
  border-radius: 8px;
  color: #334960;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.media-button:hover {
  background-color: #e4ebf2;
}

.media-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.media-button.disabled:hover {
  background-color: #f0f5fa;
  transform: none;
}

.media-button i {
  font-size: 1.1rem;
}

.audio-button i {
  color: #2196F3;
}

.video-button i {
  color: #E91E63;
}

.ar-button i {
  color: #4CAF50;
}

.media-player {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background-color: #f0f5fa;
  border-radius: 8px;
  border: 1px solid #E4EBF2;
}

.media-player h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334960;
  margin-bottom: 1rem;
}

.audio-element,
.video-element {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.video-element {
  max-height: 300px;
}

.qr-code-image {
  width: 150px;
  height: 150px;
  margin: 1rem auto;
  display: block;
}

@media (min-width: 768px) {
  .hero-image-container {
    height: 350px;
  }

  .storytour-info {
    padding: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
  }

  .storytour-title {
    font-size: 2rem;
  }
}
</style>
