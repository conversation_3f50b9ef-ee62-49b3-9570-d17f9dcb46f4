<template>
  <div class="preferences-view">
    <!-- Header -->
    <AppHeader
      :showLogo="true"
      :showBackButton="true"
      @goBack="goBack"
    />

    <!-- Preferences Title -->
    <div class="preferences-title">
      <h2>E<PERSON><PERSON> tus preferencias</h2>
    </div>

    <!-- Preferences Options -->
    <div class="preferences-options">
      <div class="preference-item">
        <span class="preference-text">Naturaleza y Aventura</span>
        <label class="toggle-switch">
          <input type="checkbox" v-model="preferences.naturaleza">
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="preference-item">
        <span class="preference-text">Cultura e Historia</span>
        <label class="toggle-switch">
          <input type="checkbox" v-model="preferences.cultura">
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="preference-item">
        <span class="preference-text">Gastronomía y Enoturismo</span>
        <label class="toggle-switch">
          <input type="checkbox" v-model="preferences.gastronomia">
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="preference-item">
        <span class="preference-text">Sol y Playa</span>
        <label class="toggle-switch">
          <input type="checkbox" v-model="preferences.playa">
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>

    <!-- Notification Preferences -->
    <div class="notification-section">
      <h3 class="notification-title">¿Quieres que te avisemos?</h3>

      <div class="preference-item">
        <span class="preference-text">Recibir notificaciones</span>
        <label class="toggle-switch">
          <input type="checkbox" v-model="preferences.notifications">
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>

    <!-- Save Button -->
    <div class="save-button-container">
      <button class="save-button" @click="savePreferences">Guardar</button>
    </div>

    <!-- Spacer to push content up -->
    <div class="bottom-spacer"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import AppHeader from '../components/layout/AppHeader.vue';

// Router for navigation
const router = useRouter();

// Go back function
const goBack = () => {
  router.back();
};

// Preferences state
const preferences = ref({
  naturaleza: true,
  cultura: false,
  gastronomia: true,
  playa: true,
  notifications: true
});

// Save preferences function
const savePreferences = () => {
  // In a real app, this would save to an API or local storage
  console.log('Saving preferences:', preferences.value);

  // Show success message or notification
  alert('Preferencias guardadas correctamente');

  // Navigate back to profile
  router.back();
};
</script>

<style scoped>
.preferences-view {
  min-height: 100vh;
  background-color: #f5f8fa; /* Light blue-gray background */
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem; /* Add padding at the bottom */
}

.preferences-title {
  padding: 1.5rem 1.5rem 1rem;
  margin-bottom: 0.5rem;
}

.preferences-title h2 {
  font-size: 1.1rem;
  color: #8BA8C7;
  font-weight: 400;
  margin: 0;
}

.preferences-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0 1.5rem;
  margin-bottom: 2rem;
}

.notification-section {
  padding: 0 1.5rem;
  margin-bottom: 1.5rem;
}

.notification-title {
  font-size: 1rem;
  color: #8BA8C7;
  font-weight: 400;
  margin: 2rem 0 1rem 0;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 1rem 1.25rem;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.preference-text {
  font-size: 0.95rem;
  color: #334960;
  font-weight: 600;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 30px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e0e0e0;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

input:checked + .toggle-slider {
  background-color: #4ade80;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Save Button Styles */
.save-button-container {
  padding: 1.5rem;
  margin-top: 2rem; /* Fixed value for spacing */
}

.bottom-spacer {
  height: 4rem; /* Extra space at the bottom */
}

.save-button {
  width: 100%;
  padding: 1rem;
  background-color: #334960;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
