<template>
  <div class="profile-view">
    <!-- Sidebar Menu (reused from HomeView) -->
    <SidebarMenu :is-open="isSidebarOpen" @close="closeSidebar" />

    <!-- Header -->
    <AppHeader
      title="Perfil"
      :showFavoriteButton="true"
      :showMenuButton="true"
      @toggleMenu="toggleSidebar"
    />

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
      <p>Cargando perfil...</p>
    </div>

    <!-- Guest User - Login/Register Options -->
    <div v-else-if="!isAuthenticated" class="guest-profile">
      <div class="guest-header">
        <div class="guest-icon">
          <i class="pi pi-user" style="font-size: 3rem; color: #666;"></i>
        </div>
        <h2>Bienvenido a Cork Experience</h2>
        <p>Inicia sesión o regístrate para acceder a todas las funciones</p>
      </div>

      <div class="auth-buttons">
        <button class="auth-button primary" @click="navigateTo('login')">
          <i class="pi pi-sign-in"></i>
          <span>Iniciar Sesión</span>
        </button>
        <button class="auth-button secondary" @click="navigateTo('register')">
          <i class="pi pi-user-plus"></i>
          <span>Registrarse</span>
        </button>
      </div>

      <div class="guest-features">
        <h3>Mientras tanto, puedes:</h3>
        <div class="feature-list">
          <div class="feature-item" @click="navigateTo('experiencias')">
            <i class="pi pi-map"></i>
            <span>Explorar experiencias</span>
          </div>
          <div class="feature-item" @click="navigateTo('agenda')">
            <i class="pi pi-calendar"></i>
            <span>Ver agenda de eventos</span>
          </div>
          <div class="feature-item" @click="navigateTo('mapa')">
            <i class="pi pi-map-marker"></i>
            <span>Explorar el mapa</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Authenticated User Profile Information -->
    <div v-else class="profile-info">
      <div class="profile-picture-container">
        <img
          :src="userProfilePicture"
          :alt="currentUser?.name + ' Profile Picture'"
          class="profile-picture"
        />
      </div>
      <div class="profile-details">
        <h2 class="profile-username">{{ currentUser?.name || 'Usuario' }}</h2>
        <p class="profile-email">{{ currentUser?.email || 'Sin correo electrónico' }}</p>
        <button class="profile-photo-button">
          <i class="pi pi-camera"></i>
          <span>Foto de perfil</span>
        </button>
      </div>
    </div>

    <!-- Profile Menu Options (only for authenticated users) -->
    <div v-if="!isLoading && isAuthenticated" class="profile-menu">
      <div class="menu-item" @click="navigateTo('experiencias')">
        <span class="menu-text">Experiencias</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('preferencias')">
        <span class="menu-text">Preferencias</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('huella')">
        <span class="menu-text">Huella de carbono</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('reservas')">
        <span class="menu-text">Mis Reservas</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('recompensas')">
        <span class="menu-text">Recompensas</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('favoritos')">
        <span class="menu-text">Favoritos</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <div class="menu-item" @click="navigateTo('configuracion')">
        <span class="menu-text">Configuración</span>
        <i class="pi pi-chevron-right"></i>
      </div>

      <!-- Logout Button -->
      <div class="menu-item logout-item" @click="handleLogout">
        <span class="menu-text logout-text">Cerrar Sesión</span>
        <i class="pi pi-sign-out"></i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SidebarMenu from '../components/SidebarMenu.vue';
import AppHeader from '../components/layout/AppHeader.vue';
import { currentUser, isAuthenticated, logout, enterGuestMode } from '../services/auth';

// Router for navigation
const router = useRouter();

// State for sidebar
const isSidebarOpen = ref(false);
const isLoading = ref(true);

// Default profile picture
const defaultProfilePic = '/assets/images/profile/profilepicture.png';

// Computed property for user profile picture
const userProfilePicture = ref(defaultProfilePic);

// Initialize component
onMounted(() => {
  // Set profile picture if available and user is authenticated
  if (isAuthenticated.value && currentUser.value && 'profile_picture' in currentUser.value) {
    userProfilePicture.value = (currentUser.value as any).profile_picture;
  }

  isLoading.value = false;
});

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

const closeSidebar = () => {
  isSidebarOpen.value = false;
};

// Navigation function
const navigateTo = (destination: string) => {
  switch (destination) {
    case 'login':
      router.push('/login');
      break;
    case 'register':
      router.push('/registro');
      break;
    case 'experiencias':
      router.push({ name: 'experiencias' });
      break;
    case 'agenda':
      router.push('/agenda');
      break;
    case 'mapa':
      router.push('/mapa');
      break;
    case 'preferencias':
      router.push({ name: 'preferencias' });
      break;
    case 'reservas':
      router.push('/reservas');
      break;
    case 'huella':
      // Placeholder - will be implemented later
      console.log('Navigate to huella de carbono');
      break;
    case 'recompensas':
      // Placeholder - will be implemented later
      console.log('Navigate to recompensas');
      break;
    case 'favoritos':
      // Placeholder - will be implemented later
      console.log('Navigate to favoritos');
      break;
    case 'configuracion':
      // Placeholder - will be implemented later
      console.log('Navigate to configuración');
      break;
    default:
      break;
  }
};

// Logout function
const handleLogout = async () => {
  try {
    await logout();
    // Enter guest mode and redirect to home
    enterGuestMode();
    router.push('/inicio');
  } catch (error) {
    console.error('Error during logout:', error);
    // Even if logout fails, clear local state and redirect
    enterGuestMode();
    router.push('/inicio');
  }
};
</script>

<style scoped>
.profile-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  margin-top: 2rem;
}

/* Guest Profile Styles */
.guest-profile {
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.guest-header {
  text-align: center;
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guest-icon {
  margin-bottom: 1rem;
}

.guest-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334960;
  margin: 0 0 0.5rem 0;
}

.guest-header p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.auth-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.auth-button.primary {
  background-color: #334960;
  color: white;
}

.auth-button.primary:hover {
  background-color: #2a3d52;
}

.auth-button.secondary {
  background-color: white;
  color: #334960;
  border: 2px solid #334960;
}

.auth-button.secondary:hover {
  background-color: #334960;
  color: white;
}

.guest-features {
  background-color: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guest-features h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334960;
  margin: 0 0 1rem 0;
  text-align: center;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.feature-item:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
}

.feature-item i {
  color: #DC8960;
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.feature-item span {
  color: #334960;
  font-weight: 500;
}

.profile-info {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background-color: white;
  margin-bottom: 1rem;
}

.profile-picture-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1.5rem;
  border: 2px solid #e0e0e0;
}

.profile-picture {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-details {
  display: flex;
  flex-direction: column;
}

.profile-username {
  font-size: 1.25rem;
  font-weight: 600;
  color: #334960;
  margin: 0 0 0.25rem 0;
}

.profile-email {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 0.75rem 0;
}

.profile-photo-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #6366F1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  width: fit-content;
}

.profile-photo-button i {
  font-size: 1rem;
}

.profile-menu {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0 1rem;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 1rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.menu-text {
  font-size: 1rem;
  color: #334960;
  font-weight: 500;
}

.menu-item i {
  color: #9aa5b1;
  font-size: 1rem;
}

.logout-item {
  margin-top: 1rem;
  border: 1px solid #dc3545;
  background-color: #fff5f5;
}

.logout-item:hover {
  background-color: #dc3545;
}

.logout-item:hover .logout-text {
  color: white;
}

.logout-item:hover i {
  color: white;
}

.logout-text {
  color: #dc3545;
  font-weight: 600;
}

.logout-item i {
  color: #dc3545;
}
</style>
